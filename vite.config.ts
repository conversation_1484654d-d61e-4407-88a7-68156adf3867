import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";



// const proxyConfig: ProxyConfig = {
//   '/api/proxy/purchase-order-details': {
//     target: API_CONFIG.baseUrl,
//     changeOrigin: true,
//     rewrite: (path) => path.replace('/api/proxy/purchase-order-details', API_CONFIG.endpoints.purchaseOrderDetails),
//     configure: (proxy: any, _options: any) => {
//       proxy.on('proxyReq', (proxyReq: any, _req: any, _res: any) => {
//         proxyReq.setHeader('x-api-key', API_CONFIG.apiKey);
//         proxyReq.setHeader('Content-Type', 'application/json');
//       });
//     },
//   },
//   '/api/proxy/quarterly-metrics': {
//     target: API_CONFIG.baseUrl,
//     changeOrigin: true,
//     rewrite: (path) => path.replace('/api/proxy/quarterly-metrics', API_CONFIG.endpoints.quarterlyMetrics),
//     configure: (proxy: any, _options: any) => {
//       proxy.on('proxyReq', (proxyReq: any, _req: any, _res: any) => {
//         proxyReq.setHeader('x-api-key', API_CONFIG.apiKey);
//         proxyReq.setHeader('Content-Type', 'application/json');
//       });
//     },
//   },
//   '/api/proxy/margins': {
//     target: API_CONFIG.baseUrl,
//     changeOrigin: true,
//     rewrite: (path) => path.replace('/api/proxy/margins', API_CONFIG.endpoints.margins),
//     configure: (proxy: any, _options: any) => {
//       proxy.on('proxyReq', (proxyReq: any, _req: any, _res: any) => {
//         proxyReq.setHeader('x-api-key', API_CONFIG.apiKey);
//         proxyReq.setHeader('Content-Type', 'application/json');
//       });
//     },
//   },
//   '/api/proxy/achievement': {
//     target: API_CONFIG.baseUrl,
//     changeOrigin: true,
//     rewrite: (path) => path.replace('/api/proxy/achievement', API_CONFIG.endpoints.achievement),
//     configure: (proxy: any, _options: any) => {
//       proxy.on('proxyReq', (proxyReq: any, _req: any, _res: any) => {
//         proxyReq.setHeader('x-api-key', API_CONFIG.apiKey);
//         proxyReq.setHeader('Content-Type', 'application/json');
//       });
//     },
//   },
//   '/api/proxy/sales-progress': {
//     target: API_CONFIG.baseUrl,
//     changeOrigin: true,
//     rewrite: (path) => path.replace('/api/proxy/sales-progress', API_CONFIG.endpoints.salesProgress),
//     configure: (proxy: any, _options: any) => {
//       proxy.on('proxyReq', (proxyReq: any, _req: any, _res: any) => {
//         proxyReq.setHeader('x-api-key', API_CONFIG.apiKey);
//         proxyReq.setHeader('Content-Type', 'application/json');
//       });
//     },
//   },
//   '/api/proxy/customer-metrics': {
//     target: API_CONFIG.baseUrl,
//     changeOrigin: true,
//     rewrite: (path) => path.replace('/api/proxy/customer-metrics', API_CONFIG.endpoints.customerMetrics),
//     configure: (proxy: any, _options: any) => {
//       proxy.on('proxyReq', (proxyReq: any, _req: any, _res: any) => {
//         proxyReq.setHeader('x-api-key', API_CONFIG.apiKey);
//         proxyReq.setHeader('Content-Type', 'application/json');
//       });
//     },
//   },
//   '/api/proxy/bu-head': {
//     target: API_CONFIG.baseUrl,
//     changeOrigin: true,
//     rewrite: (path) => {
//       const basePath = path.replace('/api/proxy/bu-head', API_CONFIG.endpoints.salesProgressBuHead);
//       return `${basePath}?export-format=json`;
//     },
//     configure: (proxy: any, _options: any) => {
//       proxy.on('proxyReq', (proxyReq: any, _req: any, _res: any) => {
//         proxyReq.setHeader('x-api-key', API_CONFIG.apiKey);
//         proxyReq.setHeader('Content-Type', 'application/json');
//       });
//     },
//   },
//   '/api/catalog': {
//     target: 'https://api-stg.mstack.co/cs/api/v1',
//     changeOrigin: true,
//     rewrite: (path) => path.replace(/^\/api\/catalog/, ''),
//   },
// };

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8081,
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
