name: staging-automatic-workflow

on:
  push:
    branches:
      - main  # Trigger on push to main branch

jobs:
  build_and_deploy:
    uses: Mstack-Chemicals/shared-workflows/.github/workflows/build_and_deploy_aws.yaml@main
    with:
      branch: ${{ github.event.inputs.branch }}
      service_name: salesstack
      dockerfile_path: .
      values_file_path: mstack/salesstack/staging.values.yaml
    secrets: inherit