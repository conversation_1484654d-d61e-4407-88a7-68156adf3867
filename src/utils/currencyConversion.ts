// Add currency conversion rates (you might want to fetch these from an API in production)
const CURRENCY_RATES = {
      "USD": 1,
      "INR": 0.01141,
      "EURO": 0.8552,
      "YUAN": 0.1388,
      "YEN": 0.00679,
      "AED": 0.2722
    };

// Helper function to convert currency to USD
export const convertToUSD = (amount: number, fromCurrency: string): number => {
  const rate = CURRENCY_RATES[fromCurrency as keyof typeof CURRENCY_RATES] || 1;
  return amount * rate;
};
