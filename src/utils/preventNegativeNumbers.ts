/**
 * Utility to prevent negative numbers in input fields
 * 
 * This can be used in two ways:
 * 
 * 1. As an event handler directly:
 *    <input type="number"
min="0" onInput={preventNegativeNumbers} />
 * 
 * 2. In a custom onChange handler:
 *    const handleChange = (e) => {
 *      preventNegativeNumbers(e);
 *      // Your additional logic here
 *    };
 */

export const preventNegativeNumbers = (event: React.FormEvent<HTMLInputElement>) => {
  const input = event.currentTarget;
  
  // Set min attribute if not already set
  if (!input.hasAttribute('min')) {
    input.setAttribute('min', '0');
  }
  
  // Get the current value
  const value = input.value;
  
  // If the value is negative or starts with a negative sign, reset to 0 or empty
  if (value.startsWith('-')) {
    input.value = '';
  }
  
  // For browsers that don't respect the min attribute
  const numValue = parseFloat(value);
  if (!isNaN(numValue) && numValue < 0) {
    input.value = '0';
  }
};

/**
 * Higher-order function to create an onChange handler that prevents negative numbers
 * 
 * Usage:
 * const handleChange = withPreventNegativeNumbers((value) => {
 *   // Your logic with the sanitized value
 *   console.log('Positive value:', value);
 * });
 */
export const withPreventNegativeNumbers = (
  onChange: (value: number | null) => void
) => {
  return (event: React.ChangeEvent<HTMLInputElement>) => {
    preventNegativeNumbers(event);
    
    const value = event.currentTarget.value;
    
    // Pass null for empty values, otherwise the parsed number
    onChange(value === '' ? null : parseFloat(value));
  };
};
