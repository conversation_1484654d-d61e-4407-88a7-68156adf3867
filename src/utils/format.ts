/**
 * Formats an email address into a display name
 * Example: <EMAIL> -> <PERSON>
 */
export const formatEmailToName = (email: string): string => {
  if (!email) return '';
  // Get the part before @
  const name = email.split('@')[0];
  // Split by dots and underscores
  const parts = name.split(/[._]/);
  // Capitalize each part and join with space
  return parts.map(part => part.charAt(0).toUpperCase() + part.slice(1)).join(' ');
}; 