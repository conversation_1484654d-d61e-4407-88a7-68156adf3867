// Country code to full name mapping
export const countryCodeToName = {
  USA: "United States",
  IND: "India",
  Canada: "Canada",
  UAE: "United Arab Emirates",
  China: "China",
  Saudi: "Saudi Arabia",
  Nigeria: "Nigeria",
  Indonesia: "Indonesia",
  Brazil: "Brazil",
  Singapore: "Singapore",
  Bahrain: "Bahrain",
  Chile: "Chile",
  Colombia: "Colombia",
  Ecuador: "Ecuador",
  Georgia: "Georgia",
  Mexico: "Mexico",
  Peru: "Peru",
  Thailand: "Thailand",
  Vietnam: "Vietnam"
};

// Country flag mapping
export const countryFlags = {
  USA: "🇺🇸",
  IND: "🇮🇳",
  Canada: "🇨🇦",
  UAE: "🇦🇪",
  China: "🇨🇳",
  Saudi: "🇸🇦",
  Nigeria: "🇳🇬",
  Indonesia: "🇮🇩",
  Brazil: "🇧🇷",
  Singapore: "🇸🇬",
  Bahrain: "🇧🇭",
  Chile: "🇨🇱", 
  Colombia: "🇨🇴", 
  Ecuador: "🇪🇨", 
  Georgia: "🇬🇪", 
  Mexico: "🇲🇽", 
  Peru: "🇵🇪", 
  Thailand: "🇹🇭", 
  Vietnam: "🇻🇳"
};

// Cities organized by country
export const citiesByCountry = {
  USA: [
    { value: "New York" },
    { value: "Los Angeles" },
    { value: "Chicago" },
    { value: "Houston" },
    { value: "Phoenix" },
    { value: "Philadelphia" },
    { value: "San Antonio" },
    { value: "San Diego" },
    { value: "Dallas" },
    { value: "San Jose" },
    { value: "Austin" },
    { value: "Jacksonville" },
    { value: "San Francisco" },
    { value: "Columbus" },
    { value: "Indianapolis" },
    { value: "Seattle" },
    { value: "Denver" },
    { value: "Washington D.C." },
    { value: "Boston" },
    { value: "Nashville" },
    { value: "Detroit" },
    { value: "Portland" },
    { value: "Las Vegas" },
    { value: "Memphis" },
    { value: "Louisville" },
    { value: "Baltimore" },
    { value: "Milwaukee" },
    { value: "Albuquerque" },
    { value: "Tucson" },
    { value: "Fresno" },
    { value: "Sacramento" },
    { value: "Kansas City" },
    { value: "Atlanta" },
    { value: "Miami" },
    { value: "Minneapolis" },
    { value: "Cleveland" },
    { value: "Tampa" },
    { value: "St. Louis" },
    { value: "Pittsburgh" },
    { value: "Cincinnati" },
    { value: "Raleigh" },
    { value: "Texas" },
    { value: "Liberty" },
    { value: "Hempstead" },
    { value: "Palmyra" },
    { value: "Alabama" },
    { value: "Midland" },
    { value: "Clover" },
    { value: "Northcarolina" },
    { value: "Illinois" },
    { value: "Milwaukee" },
    { value: "Virginia Beach" },
    { value: "Eugene" },
    { value: "Mesquite" },
    { value: "Sugarland" },
    { value: "Richmond" },
    { value: "Mequon" },
    { value: "Gardnerville" },
    { value: "Florida" },
    { value: "Big Spring" },
    { value: "Rockingham" },
    { value: "El Reno" },
    { value: "Wisconsin" },
    { value: "Palmyra" },
    { value: "Illinois" },
    { value: "Los Angeles" },
    { value: "Baltimore" },
    { value: "Gloucester" },
    { value: "North Carolina" },
    { value: "Ambridge" },
    { value: "Conroe" },
    { value: "Beaver" },
    { value: "Wharton" },
    { value: "Houston Port" },
    { value: "St. Petersburg" },
    { value: "Ohio" },
    { value: "Georgia" },
    { value: "Wilmington" },
    { value: "Illinois" },
    { value: "Gardendale" },
    { value: "Kentucky" },
    { value: "Berlin-USA" },
    { value: "New Jersey" },
    { value: "Fresno" },
    { value: "Greenville" },
    { value: "Ennis" },
    { value: "Luling" },
    { value: "Pennsauken" },
    { value: "Paterson" },
    { value: "Clinton" },
    { value: "Florida" },
    { value: "Mechanicville" },
    { value: "North Carolina" },
    { value: "Richwood" },
    { value: "El Campo" },
    { value: "Seagraves" },
    { value: "Ennis" },
    { value: "Easton" },
    { value: "Baltimore" },
    { value: "St. Medina" },
    { value: "Bay city" },
    { value: "Greenville" },
    { value: "Nashwua" },
    { value: "Oklahoma" },
    { value: "Bensalem" },
    { value: "Gardendale" },
    { value: "Brownwood" },
    { value: "Waukesha" },
    { value: "SCHAUMBURG" },
    { value: "West Bend" },
    { value: "Newell" },
    { value: "Brookfield" },
    { value: "Lockport" },
    { value: "Algeria-USA" },
    { value: "Richmond" },
    { value: "McKinney" },
    { value: "Dalton" },
    { value: "Conroe" },
    { value: "Sugar Land" },
    { value: "Texas" },
    { value: "Arizona" },
    { value: "Middleborough" },
    { value: "Springfield" },
    { value: "Lafayette" },
    { value: "Avenel" },
    { value: "Cincinnati" },
    { value: "Sheboygan" },
    { value: "Scott" },
    { value: "Odessa" },
    { value: "Wisconsin" },
    { value: "Phoenix" },
    { value: "Browndeer" },
    { value: "Milwaukee" },
    { value: "Claremont" },
    { value: "Batavia" },
    { value: "Pearland" },
    { value: "Grand Rapids" },
    { value: "Greeley" },
    { value: "Bastrop" },
    { value: "El campo" },
    { value: "Palmyra" },
    { value: "Augusta" },
    { value: "St.Charles" },
    { value: "Hempstead" },
    { value: "Dallas" },
    { value: "Seagreaves" },
    { value: "Odessa" },
    { value: "Indiana" },
    { value: "Springfield" },
    { value: "Bakersfield" },
    { value: "Mequon" },
    { value: "Pittsburgh" },
    { value: "St.Charles" },
    { value: "Bay City" },
    { value: "Pasadena" },
    { value: "Deer Park" },
    { value: "Baytown" },
    { value: "Freeport" },
    { value: "Baton Rouge" },
    { value: "La Porte" },
    { value: "Texas City" },
    { value: "Geismar" },
    { value: "Channelview" },
    { value: "Plaquemine" },
    { value: "Lake Charles" },
    { value: "Norco" },
    { value: "Corpus Christi" },
    { value: "Beaumont" },
    { value: "Port Arthur" },
    { value: "Port Neches" },
    { value: "St. Gabriel" },
    { value: "Alvin" },
    { value: "Orange" },
    { value: "Victoria" },
    { value: "Louisville" },
    { value: "Cincinnati" },
    { value: "Geismar (Ascension Parish)" },
    { value: "Philadelphia" },
    { value: "Channelview (Jacinto City area)" },
    { value: "La Porte (Morgan’s Point)" },
    { value: "Cleveland" },
    { value: "Joliet" },
    { value: "Kingsport" },
    { value: "Garyville" },
    { value: "South Charleston" },
    { value: "Charleston" },
    { value: "Institute" },
    { value: "Freeport (Old Ocean)" },
    { value: "Norco (St. Charles Parish)" },
    { value: "Baton Rouge (Port Allen)" },
    { value: "Martinez" },
    { value: "Galena Park" },
    { value: "Billerica" },
    { value: "Addyston" },
    { value: "Pensacola" },
    { value: "Chicago" },
    { value: "Marietta" },
    { value: "Portland" },
    { value: "Linden" },
    { value: "Newark" },
    { value: "Baton Rouge (Ascension Parish)" },
    { value: "Tulsa" },
    { value: "Mobile" },
    { value: "Monaca" },
    { value: "Billerica (Boston area)" },
    { value: "Kenner" },
    { value: "New Orleans" },
    { value: "Sterling" },
    { value: "Pittsburgh" },
    { value: "Allentown" },
    { value: "Rahway" },
    { value: "East Hanover" },
    { value: "Thousand Oaks" },
    { value: "Greenville" },
    { value: "Augusta" },
    { value: "Indianapolis" },
    { value: "Spartanburg" },
    { value: "Wilmington" },
    { value: "Charleston" },
    { value: "Memphis" },
    { value: "Savannah" },
    { value: "Wichita" },
    { value: "Plaquemine (Iberville Parish)" },
    { value: "St. James" },
    { value: "Freeport (Angleton)" },
    { value: "Parkersburg" },
    { value: "Addis" },
    { value: "Borger" },
    { value: "Toledo" },
    { value: "LaPlace" },
    { value: "Geismar (St. James Parish)" },
    { value: "Billerica (Mass.)" },
    { value: "Monroe" },
    { value: "Kansas City" },
    { value: "Decatur" },
    { value: "Ponca City" },
    { value: "Hahnville" },
    { value: "St. Charles (Parish)" },
    { value: "Martinez (Bay Area)" },
    { value: "Trenton" },
    { value: "Detroit" },
    { value: "El Dorado" },
    { value: "Billerica (MA)" },
    { value: "Birmingham" },
    { value: "Akron" },
    { value: "Calvert City" },
    { value: "Chicago Heights" },
    { value: "Cedar Bayou (Baytown area)" },
    { value: "Marshalls Creek" },
    { value: "Pennsauken" },
    { value: "Hudson" },
    { value: "Greensboro" },
    { value: "Fayetteville" },
    { value: "Monroe" },
    { value: "Addyston (OH)" },
    { value: "Sheldon" },
    { value: "La Porte (TX)" },
    { value: "Rochester" },
    { value: "Clinton" },
    { value: "Tonawanda" },
    { value: "Pascagoula" },
    { value: "Gary" },
    { value: "La Porte (TX)" },
    { value: "Collegeville" },
    { value: "South San Francisco" },
    { value: "Billerica (MA)" },
    { value: "Lafayette" },
    { value: "Bayport (Pasadena/La Porte)" },
    { value: "Carteret" },
    { value: "Billerica (MA)" },
    { value: "Houston (TX)" },
    { value: "Baton Rouge (LA)" },
    { value: "Tuscaloosa" },
    { value: "Plaquemine (LA)" },
    { value: "Kenova" },
    { value: "Chocolate Bayou (Alvin)" },
    { value: "Kingsport (TN)" },
    { value: "Oklahoma City" },
    { value: "Gonzales" },
    { value: "Axis" },
    { value: "Hopewell" },
    { value: "Charleston" },
    { value: "Anderson" },
    { value: "La Porte (TX)" },
    { value: "Greenwood" },
    { value: "Marion" },
    { value: "Mont Belvieu" },
    { value: "Schenectady" },
    { value: "Brockway" },
    { value: "Greenville" },
    { value: "Sheboygan" },
    { value: "Trenton" },
    { value: "Niagara Falls" },
    { value: "Greeley" },
    { value: "LaSalle" },
    { value: "Bayonne" },
    { value: "Lima" },
    { value: "Reading" },
    { value: "El Paso" },
    { value: "Addison" },
    { value: "Billerica (MA)" },
    { value: "Nashville" },
    { value: "Columbus" },
    { value: "Claremont" },
    { value: "Nederland" },
    { value: "Port Allen" },
    { value: "Huntington" },
    { value: "Carson" },
    { value: "Martinez (CA)" },
    { value: "Anacortes" },
    { value: "Woodward" },
    { value: "Moses Lake" },
    { value: "Belle" },
    { value: "Sheldon (TX)" },
    { value: "Lake Charles (LA)" },
    { value: "Westlake" },
    { value: "Marcus Hook" },
    { value: "Kenner (LA)" },
    { value: "Geismar (LA)" },
    { value: "Gonzales (LA)" },
    { value: "Gainesville" },
    { value: "Waterford" },
    { value: "Seattle" },
    { value: "Denver" },
    { value: "Fresno" },
    { value: "Yuma" },
    { value: "Dover" },
    { value: "Bridgeport" },
    { value: "Deepwater" },
    { value: "Bayport (TX)" },
    { value: "Hopewell (VA)" },
    { value: "La Porte (TX)" },
    { value: "Portland" },
    { value: "Freeport (TX)" },
    { value: "Carson (CA)" },
    { value: "Wilmington (CA)" },
    { value: "Lansing" },
    { value: "Buffalo" },
    { value: "Trona" },
    { value: "Ogden" },
    { value: "Cheyenne" },
    { value: "Billings" },
    { value: "Anaconda" },
    { value: "Mobile (Axis)" },
    { value: "Sheboygan (WI)" },
    { value: "Stamford" },
    { value: "Waterford (NY)" },
    { value: "Pensacola (FL)" },
    { value: "Martinsville" },
    { value: "Kingsport (TN)" },
    { value: "Belle (WV)" },
    { value: "St. Louis (MO)" },
    { value: "Institute (WV)" },
    { value: "Cincinnati (OH)" },
    { value: "Houma" },
    { value: "Midland (TX)" },
    { value: "Sarnia" },
    { value: "Cedar Rapids" },
    { value: "Springfield" },
    { value: "Cedar Bayou (TX)" },
    { value: "Omaha" },
    { value: "Council Bluffs" },
    { value: "Minneapolis" },
    { value: "St. Paul" },
    { value: "Green Bay" },
    { value: "La Crosse" },
    { value: "Milwaukee" },
    { value: "Hartford" },
    { value: "Philadelphia (PA)" },
    { value: "Norwalk" },
    { value: "New York" },
    { value: "Rahway (NJ)" },
    { value: "Parsippany" },
    { value: "Boston" },
    { value: "East St. Louis" },
    { value: "Brooklyn" },
    { value: "Los Angeles" },
    { value: "Dover (DE)" },
    { value: "Wilmington (NC)" },
    { value: "Alexandria" },
    { value: "Baytown (TX)" },
    { value: "Pasadena (TX)" },
    { value: "Shepherdsville" },
    { value: "Nitro" },
    { value: "Laurel" },
    { value: "Sulphur" },
    { value: "Wichita Falls" },
    { value: "Bismarck" },
    { value: "Gonzales (TX)" },
    { value: "Midland (MI)" },
    { value: "Moss Point" },
    { value: "Anaheim" },
    { value: "Warren" },
    { value: "Haverhill" },
    { value: "Middlesboro" },
    { value: "Provo" },
    { value: "Baton Rouge (LA)" },
    { value: "Great Falls" },
    { value: "Kingsport (TN)" },
    { value: "Augusta (GA)" },
    { value: "Shreveport" },
    { value: "Niagara Falls (NY)" },
    { value: "Spokane" },
    { value: "Fairmont" },
    { value: "Baton Rouge (West)" },
    { value: "Macon" },
    { value: "Oxford" },
    { value: "Nashua" },
    { value: "Butte" },
    { value: "Freeport (TX)" },
    { value: "Oak Ridge" },
    { value: "Troutdale" },
    { value: "Westwego" },
    { value: "Columbia" },
    { value: "Greenville (TX)" },
    { value: "Grand Rapids" },
    { value: "Lynchburg" },
    { value: "Morristown" },
    { value: "Westlake (LA)" },
    { value: "Cedar Bayou (TX)" },
    { value: "Belle Chasse" },
    { value: "Baton Rouge (LA)" },
    { value: "Marion" },
    { value: "Sunshine" },
    { value: "Woodbine" },
    { value: "Portland (OR)" },
    { value: "Buffalo (NY)" },
    { value: "Newark (NJ)" },
    { value: "Lima (OH)" },
    { value: "Ogden (UT)" },
    { value: "Provo (UT)" },
    { value: "Springfield (IL)" },
    { value: "Jackson" },
    { value: "Des Moines" },
    { value: "Little Rock" },
    { value: "Hibbing" },
    { value: "Superior" },
    { value: "Burlington" },
    { value: "Saginaw" },
    { value: "Hartford (IL)" },
    { value: "Helena" },
    { value: "Garyville (LA)" },
    { value: "Kenner (LA)" },
    { value: "Reserve" },
    { value: "Burnside" },
    { value: "Plaquemine (LA)" },
    { value: "El Segundo" },
    { value: "Valdez" },
    { value: "Princeton" },
    { value: "Rahway (NJ)" },
    { value: "Scotch Plains" },
    { value: "Albany" },
    { value: "Syracuse" },
    { value: "Kingsport (TN)" },
    { value: "Farmington" },
    { value: "Phoenix" },
    { value: "Salt Lake City" },
    { value: "Tampa" },
    { value: "Bartow" },
    { value: "Palatka" },
    { value: "Calvert City (KY)" },
    { value: "Franklin" },
    { value: "Port Lavaca" },
    { value: "Point Comfort" },
    { value: "Seadrift" },
    { value: "Ingleside" },
    { value: "Enid" },
    { value: "Aurora" },
    { value: "Hopewell (VA)" },
    { value: "Martinsville (VA)" },
    { value: "Belpre" },
    { value: "Gulfport" },
    { value: "Bay St. Louis" },
    { value: "Picayune" },
    { value: "Pasadena (TX)" },
    { value: "Wilmington (DE)" },
    { value: "Summit" },
    { value: "Oak Ridge (TN)" },
    { value: "Mapleton" },
    { value: "Coffeyville" },
    { value: "Wilmington (CA)" },
    { value: "La Porte (TX)" },
    { value: "Pasadena (TX)" },
    { value: "Henderson" },
    { value: "Sparks" },
    { value: "Kingman" },
    { value: "Inver Grove Heights" },
    { value: "El Dorado (AR)" },
    { value: "Ruston" },
    { value: "Carville" },
    { value: "Osceola" },
    { value: "Chicago (IL)" },
    { value: "Monroe (LA)" },
    { value: "Sheldon (TX)" },
    { value: "Mandan" },
    { value: "Dickinson" },
    { value: "New Iberia" },
    { value: "Houma (LA)" },
    { value: "Longview" },
    { value: "Victoria (TX)" },
    { value: "Kennewick" },
    { value: "Gulfport (MS)" },
    { value: "Iowa City" },
    { value: "Gadsden" },
    { value: "Gonzales (LA)" },
    { value: "Mossville" },
    { value: "Westwego (LA)" },
    { value: "Homer" },
    { value: "Macon (GA)" },
    { value: "Columbus (OH)" },
    { value: "Marietta (OH)" },
    { value: "Port Neches (TX)" },
    { value: "Fort Worth" },
    { value: "Dallas" },
    { value: "Kenedy" },
    { value: "Lubbock" },
    { value: "Amarillo" },
    { value: "Hampton" },
    { value: "Decatur (AL)" },
    { value: "Cedar City" },
    { value: "Lake Charles (LA)" },
    { value: "Natchez" },
    { value: "Panama City" },
    { value: "Toronto" },
    { value: "Ashtabula" },
    { value: "Steubenville" },
    { value: "Weirton" },
    { value: "Charleston (WV)" },
    { value: "Fairfield" },
    { value: "Pasadena (TX)" },
    { value: "Lake Charles (LA)" },
    { value: "Pine Bluff" },
    { value: "Montgomery" },
    { value: "Hickory" },
    { value: "Ogden (UT)" },
    { value: "Wilmington (NC)" },
    { value: "Joliet (IL)" },
    { value: "LaSalle (IL)" },
    { value: "Kankakee" },
    { value: "Augusta (GA)" },
    { value: "Trenton (NJ)" },
    { value: "St. Gabriel (LA)" },
    { value: "Lake Charles (LA)" },
    { value: "Sulphur (LA)" },
    { value: "Mossville (LA)" },
    { value: "Los Angeles (CA)" },
    { value: "San Francisco" },
    { value: "San Jose" },
    { value: "Oakland" },
    { value: "Richmond" },
    { value: "Long Beach" },
    { value: "Martinez (CA)" },
    { value: "Sacramento" },
    { value: "Stockton" },
    { value: "Tracy" },
    { value: "Portland (ME)" },
    { value: "Newark (NJ)" },
    { value: "Jersey City" },
    { value: "Bayonne (NJ)" },
    { value: "Carteret (NJ)" },
    { value: "Perth Amboy" },
    { value: "New Brunswick" },
    { value: "Stamford (CT)" },
    { value: "Naugatuck" },
    { value: "Wilmington (DE)" },
    { value: "Savannah (GA)" },
    { value: "Marcus Hook (PA)" },
    { value: "Montgomery (AL)" },
    { value: "Huntsville" },
    { value: "Anniston" },
    { value: "Florence" },
    { value: "Kingsport (TN)" },
    { value: "Charleston (SC)" },
    { value: "Danville" },
    { value: "Martinsburg" },
    { value: "Philadelphia (PA)" },
    { value: "Pensacola (FL)" },
    { value: "Lafayette (LA)" },
    { value: "Bismarck (ND)" },
    { value: "Billings (MT)" },
    { value: "Great Falls (MT)" },
    { value: "Minot" },
    { value: "Casper" },
    { value: "Green River" },
    { value: "Pocatello" },
    { value: "Kalispell" },
    { value: "Hobbs" },
    { value: "Baton Rouge (LA)" },
    { value: "San Diego" },
    { value: "Oceanside" },
    { value: "Vacaville" },
    { value: "Boulder" },
    { value: "Richmond (VA)" },
    { value: "Laurel (MD)" },
    { value: "Halethorpe" },
    { value: "Wilmington (NC)" },
    { value: "Springfield (MA)" },
    { value: "Lincoln" },
    { value: "Newark (DE)" },
    { value: "Seaford" },
    { value: "Dover (DE)" },
    { value: "Bay St. Louis (MS)" },
    { value: "Jackson (MS)" },
    { value: "Clinton" },
    { value: "Tallahassee" },
    { value: "Hialeah" },
    { value: "Orlando" },
    { value: "Carson City" },
    { value: "Las Vegas" },
    { value: "Barre" },
    { value: "Manchester" },
    { value: "Providence" },
    { value: "New Haven" },
    { value: "Trenton (NJ)" },
    { value: "Camden" },
    { value: "Akron (OH)" },
    { value: "Cleveland (OH)" },
    { value: "Dayton" },
    { value: "Holland" },
    { value: "York" },
    { value: "Erie" },
    { value: "St. Clair" },
    { value: "Laurel (MS)" },
    { value: "Pine Bend (MN)" },
    { value: "Torrance" },
    { value: "Lake Charles (LA)" },
    { value: "Baton Rouge (LA)" },
    { value: "Dallas (TX)" },
    { value: "Chicago (IL)" },
    { value: "New York (NY)" },
    { value: "Los Angeles (CA)" },
    { value: "San Jose (CA)" },
    { value: "Philadelphia (PA)" },
    { value: "Detroit (MI)" },
    { value: "Cleveland (OH)" },
    { value: "Newark (NJ)" },
    { value: "Jersey City (NJ)" },
    { value: "Portland (OR)" },
    { value: "Portland (ME)" },
    { value: "Anchorage" },
    { value: "Honolulu" },
    { value: "Guayama" },
    { value: "Bayamón" },
    { value: "Barceloneta" },
    { value: "Ponce" },
  ],
  IND: [
    { value: "Mumbai" },
    { value: "Delhi" },
    { value: "Bangalore" },
    { value: "Hyderabad" },
    { value: "Ahmedabad" },
    { value: "Chennai" },
    { value: "Kolkata" },
    { value: "Surat" },
    { value: "Pune" },
    { value: "Jaipur" },
    { value: "Lucknow" },
    { value: "Kanpur" },
    { value: "Nagpur" },
    { value: "Indore" },
    { value: "Thane" },
    { value: "Bhopal" },
    { value: "Visakhapatnam" },
    { value: "Patna" },
    { value: "Vadodara" },
    { value: "Ghaziabad" },
    { value: "Ludhiana" },
    { value: "Agra" },
    { value: "Nashik" },
    { value: "Faridabad" },
    { value: "Meerut" },
    { value: "Rajkot" },
    { value: "Varanasi" },
    { value: "Srinagar" },
    { value: "Aurangabad" },
    { value: "Dhanbad" },
    { value: "Amritsar" },
    { value: "Allahabad" },
    { value: "Ranchi" },
    { value: "Howrah" },
    { value: "Coimbatore" },
    { value: "Jabalpur" },
    { value: "Gwalior" },
    { value: "Vijayawada" },
    { value: "Jodhpur" },
    { value: "Madurai" },
    { value: "Raipur" },
    { value: "Panoli" },
    { value: "Ambala" },
    { value: "Noida" },
    { value: "Mumbai" },
    { value: "Vadodra" },
    { value: "Malout" },
    { value: "Raipur" },
    { value: "Meerut" },
    { value: "Karnal" },
    { value: "Kandla" },
    { value: "Hingana" },
    { value: "Dhule" },
    { value: "Vidisha" },
    { value: "India" },
    { value: "Thiruvallur" },
    { value: "GUMMADIDALA" },
    { value: "Karjat" },
    { value: "Bhiwandi" },
    { value: "Kolkata" },
    { value: "Dhar" },
    { value: "Vapi" },
    { value: "Raigad" },
    { value: "LONAVLA" },
    { value: "Bhatinda" },
    { value: "Sonipat" },
    { value: "Bathinda" },
    { value: "India" },
    { value: "LUDHIANA" },
    { value: "Sampla" },
    { value: "Ludhiana" },
    { value: "Panipat" },
    { value: "Bijnor" },
    { value: "Ahmedabad" },
    { value: "Jaipur" },
    { value: "Lonavla" },
    { value: "Nashik" },
    { value: "karnal" },
    { value: "Sohna" },
    { value: "Palwal" },
    { value: "Mewat" },
    { value: "Satara" },
    { value: "AHMEDABAD" },
    { value: "INDIA" },
    { value: "Bharuch" }
  ],
  Canada: [
    { value: "Toronto" },
    { value: "Montreal" },
    { value: "Vancouver" },
    { value: "Calgary" },
    { value: "Edmonton" },
    { value: "Ottawa" },
    { value: "Winnipeg" },
    { value: "Quebec City" },
    { value: "Halifax" },
    { value: "Regina" },
    { value: "Saskatoon" },
    { value: "Hamilton" },
    { value: "London" },
    { value: "Victoria" },
    { value: "St. John's" },
    { value: "Kelowna" },
    { value: "Windsor" },
    { value: "Oshawa" },
    { value: "Kitchener" },
    { value: "Richmond" },
    { value: "Burnaby" },
    { value: "Mississauga" },
    { value: "Brampton" },
    { value: "Surrey" },
    { value: "Laval" },
    { value: "Markham" },
    { value: "Vaughan" },
    { value: "Gatineau" },
    { value: "Longueuil" },
    { value: "Sherbrooke" },
    { value: "Burlington" },
    { value: "Sudbury" },
    { value: "Abbotsford" },
    { value: "Saguenay" },
    { value: "Trois-Rivières" },
    { value: "Moncton" },
    { value: "Brantford" },
    { value: "Thunder Bay" },
    { value: "Red Deer" },
    { value: "Lethbridge" },
    { value: "Kamloops" },
    { value: "Vancouver" },
    { value: "Edmonton" },
    { value: "Leduc" },
    { value: "Calgary" },
    { value: "Grande Prairie" },
    { value: "Calgary" }
  ],
  UAE: [
    { value: "Abu Dhabi" },
    { value: "Al Ain" },
    { value: "Al Dhafra" },
    { value: "Madinat Zayed" },
    { value: "Ruwais" },
    { value: "Ghayathi" },
    { value: "Liwa" },
    { value: "Bani Yas" },
    { value: "Mussafah" },
    { value: "Zayed City" },
    { value: "Khalifa City" },
    { value: "Shakhbout City" },
    { value: "Al Falah" },
    { value: "Al Shamkha" },
    { value: "Al Rahba" },
    { value: "Al Bahia" },
    { value: "Al Sila" },
    { value: "Al Marfa" },
    { value: "Al Yahar" },
    { value: "Al Raha" },
    { value: "Dubai" },
    { value: "Jebel Ali" },
    { value: "Dubai" },
    { value: "Abu dhabi" }
  ],
  China: [
    { value: "Beijing" },
    { value: "Shanghai" },
    { value: "Guangzhou" },
    { value: "Shenzhen" },
    { value: "Chengdu" },
    { value: "Chongqing" },
    { value: "Tianjin" },
    { value: "Wuhan" },
    { value: "Xi'an" },
    { value: "Hangzhou" },
    { value: "Nanjing" },
    { value: "Qingdao" },
    { value: "Changsha" },
    { value: "Dalian" },
    { value: "Shenyang" },
    { value: "Harbin" },
    { value: "Kunming" },
    { value: "Zhengzhou" },
    { value: "Jinan" },
    { value: "Taiyuan" },
    { value: "Shijiazhuang" },
    { value: "Hefei" },
    { value: "Nanning" },
    { value: "Changchun" },
    { value: "Urumqi" },
    { value: "Fuzhou" },
    { value: "Nanchang" },
    { value: "Guiyang" },
    { value: "Hohhot" },
    { value: "Lanzhou" },
    { value: "Yinchuan" },
    { value: "Xining" },
    { value: "Ningbo" },
    { value: "Wuxi" },
    { value: "Suzhou" },
    { value: "Wenzhou" },
    { value: "Xiamen" },
    { value: "Dongguan" },
    { value: "Foshan" },
    { value: "Zhongshan" },
    { value: "Huizhou" },
    { value: "ZHEJIANG" },
    { value: "Shanghai" },
    { value: "Huizhou" },
    { value: "Hangzhou" },
    { value: "Quzhou" },
    { value: "Suzhou" },
    { value: "suzhou" },
    { value: "guangzhou" },
    { value: "PUYANG Shengyuan" }
  ],
  Saudi: [
    { value: "Riyadh" },
    { value: "Jeddah" },
    { value: "Mecca" },
    { value: "Medina (Al-Madinah)" },
    { value: "Dammam" },
    { value: "Sultanah" },
    { value: "Buraidah" },
    { value: "Tabuk" },
    { value: "Saudi Arabia" }
  ],
  Nigeria: [
    { value: "Lagos" },
    { value: "Kano" },
    { value: "Ibadan" },
    { value: "Abuja" },
    { value: "Port Harcourt" },
    { value: "Benin City" },
    { value: "Onitsha" },
    { value: "Lagos" }
  ],
  Indonesia: [
    { value: "Jakarta" },
    { value: "Surabaya" },
    { value: "Medan" },
    { value: "Bandung" },
    { value: "Bekasi" },
    { value: "Depok" },
    { value: "Tangerang" },
    { value: "Indonesia" }
  ],
  Brazil: [
    { value: "São Paulo" },
    { value: "Rio de Janeiro" },
    { value: "Brasília" },
    { value: "Salvador" },
    { value: "Fortaleza" },
    { value: "Belo Horizonte" },
    { value: "Manaus" },
    { value: "Curitiba" },
    { value: "Recife" },
    { value: "Porto Alegre" },
    { value: "Santos" }
  ],
  Singapore: [
    { value: "Singapore" }
  ],
  Bahrain: [
    { value: "Bahrain" }
  ],
  Chile: [
    { value: "Arica" }
  ],
  Colombia: [
    { value: "Colombia" },
    { value: "Medellin" },
    { value: "Cartegena" },
    { value: "Barranquilla" },
    { value: "Buenaventura" }
  ],
  Ecuador: [
    { value: "Ecuador" }
  ],
  Georgia: [
    { value: "Oglethorpe" },
    { value: "Buford" },
    { value: "Savannah" },
    { value: "Macon" }
  ],
  Mexico: [
    { value: "Cartagena" },
    { value: "Ciudad de México" },
    { value: "Manzanillo" },
    { value: "Manzillo" }
  ],
  Peru: [
    { value: "Peru" }
  ],
  Thailand: [
    { value: "Thailand" }
  ],
  Vietnam: [
    { value: "Vietnam" }
  ],
};

// Ports organized by country
export const portsByCountry = {
  USA: [
    { value: "Port of Los Angeles/Long Beach (CA)" },
    { value: "Port of New York/New Jersey" },
    { value: "Port of Savannah (GA)" },
    { value: "Port of Houston (TX)" },
    { value: "Port of Seattle-Tacoma (WA)" },
    { value: "Port of Virginia (Norfolk, VA)" },
    { value: "Port of Oakland (CA)" },
    { value: "Port of Charleston (SC)" },
    { value: "Port of Miami (FL)" },
    { value: "Port of Baltimore (MD)" },
    { value: "Port of New Orleans (LA)" },
    { value: "Port of Corpus Christi (TX)" },
    { value: "Port of Mobile (AL)" },
    { value: "Port of Philadelphia (PA)" },
    { value: "Port Everglades (FL)" },
    { value: "Port of Wilmington (DE)" },
    { value: "Port of Boston (MA)" },
    { value: "Port of Jacksonville (FL)" },
    { value: "Port of Portland (OR)" },
    { value: "Port of San Juan (PR)" },
    { value: "Port of Tampa" },
    { value: "Port of Orlando" },
    { value: "Port of Illinois" },
  ],
  IND: [
    { value: "Jawaharlal Nehru Port (JNPT, Navi Mumbai)" },
    { value: "Mundra Port (Gujarat)" },
    { value: "Chennai Port (Tamil Nadu)" },
    { value: "Visakhapatnam Port (Andhra Pradesh)" },
    { value: "Kolkata Port (West Bengal)" },
    { value: "Paradip Port (Odisha)" },
    { value: "Mormugao Port (Goa)" },
    { value: "Kamarajar Port (Tamil Nadu)" },
    { value: "Cochin Port (Kerala)" },
    { value: "New Mangalore Port (Karnataka)" },
    { value: "Tuticorin Port (Tamil Nadu)" },
    { value: "Kandla Port (Gujarat)" },
    { value: "Haldia Port (West Bengal)" },
    { value: "Pipavav Port (Gujarat)" },
    { value: "Vadhavan Port (Maharashtra, upcoming)" },
    { value: "Deendayal Port (Gujarat)" },
    { value: "Krishnapatnam Port (Andhra Pradesh)" },
    { value: "Vidisha Port (Madhya Pradesh)" },
    { value: "Thiruvallur Port (Tamil Nadu)" }
  ],
  Canada: [
    { value: "Port of Vancouver (BC)" },
    { value: "Port of Montreal (QC)" },
    { value: "Port of Prince Rupert (BC)" },
    { value: "Port of Halifax (NS)" },
    { value: "Port of Toronto (ON)" },
    { value: "Port of Quebec (QC)" },
    { value: "Port of Saint John (NB)" },
    { value: "Port of Nanaimo (BC)" },
    { value: "Port of Thunder Bay (ON)" },
    { value: "Port of Hamilton (ON)" },
    { value: "Port of Sept-Îles (QC)" },
    { value: "Port of Sydney (NS)" },
    { value: "Port of Victoria (BC)" },
    { value: "Port of Windsor (ON)" },
    { value: "Port of Saguenay (QC)" },
    { value: "Port of Corner Brook (NL)" }
  ],
  UAE: [
    { value: "Khalifa Port" },
    { value: "Zayed Port" },
    { value: "Musaffah Port" },
    { value: "Al Mirfa Port" },
    { value: "Al Ruwais Port" },
    { value: "Al Sila Port" },
    { value: "Free Port (Western Region)" },
    { value: "Sir Bani Yas Port (cruise-focused)" },
  ],
  China: [
    { value: "Port of Shanghai" },
    { value: "Port of Ningbo-Zhoushan (Zhejiang)" },
    { value: "Port of Shenzhen (Guangdong)" },
    { value: "Port of Qingdao (Shandong)" },
    { value: "Port of Tianjin" },
    { value: "Port of Guangzhou (Guangdong)" },
    { value: "Port of Xiamen (Fujian)" },
    { value: "Port of Dalian (Liaoning)" },
    { value: "Port of Beibu (Guangxi)" },
    { value: "Port of Yiwu (Zhejiang)" },
    { value: "Port of Lianyungang (Jiangsu)" },
    { value: "Port of Rizhao (Shandong)" }
  ],
  Saudi: [
    { value: "Jeddah Islamic Port" },
    { value: "King Abdulaziz Port (Dammam Port)" },
    { value: "King Abdullah Port" },
    { value: "Yanbu Commercial Port" },
    { value: "Jubail Commercial Port" }
  ],
  Nigeria: [
    { value: "Lagos Port Complex (Apapa Port)" },
    { value: "Tin Can Island Port" },
    { value: "Port Harcourt Port" },
    { value: "Onne Port" },
    { value: "Calabar Port" }
  ],
  Indonesia: [
    { value: "Port of Tanjung Priok (Jakarta)" },
    { value: "Port of Tanjung Perak (Surabaya)" },
    { value: "Port of Belawan (Medan)" },
    { value: "Port of Makassar (Makassar)" },
    { value: "Port of Tanjung Emas (Semarang)" }
  ],
  Brazil: [
    { value: "Port of Santos" },
    { value: "Port of Paranaguá" },
    { value: "Port of Rio de Janeiro" },
    { value: "Port of Itaguaí (Sepetiba Bay)" },
    { value: "Port of Suape" },
    { value: "Port of Itajaí" },
    { value: "Port of Manaus" }
  ],
  Singapore: [
    { value: "Tuas Port" },
    { value: "Pasir Panjang Terminal" },
    { value: "Keppel Terminal" },
    { value: "Brani Terminal" },
    { value: "Jurong Port" }
  ],
}; 
