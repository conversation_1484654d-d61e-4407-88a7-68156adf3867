import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

export const SUPABASE_URL = "https://brdnlxqfwvsfpludfrho.supabase.co";
export const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJyZG5seHFmd3ZzZnBsdWRmcmhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM0ODE2NzIsImV4cCI6MjA1OTA1NzY3Mn0.IXGAjycRVC0lP8bFUjbJi_UUsFL6V7u_I7eoC5fpO5I";
// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

if (!SUPABASE_URL || !SUPABASE_PUBLISHABLE_KEY) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Storage bucket constants
export const STORAGE_BUCKETS = {
  ENQUIRY_DOCUMENTS: 'enquiry_documents',
  QUOTATION_DOCUMENTS: 'quotation-documents',
  PURCHASE_ORDER_DOCUMENTS: 'purchase_order_documents',
  SAMPLE_DOCUMENTS: 'sample-documents'
};

