export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      customer: {
        Row: {
          account_owner: string | null
          address: string | null
          city: string | null
          country: string | null
          created_at: string | null
          created_by: string | null
          id: string
          industries: string[] | null
          modified_at: string | null
          modified_by: string | null
          customer_id: string
          customer_full_name: string
          poc_email_id: string | null
          poc_name: string | null
          poc_phone_number: string | null
          size: string | null
          state: string | null
          type: string | null
        }
        Insert: {
          account_owner?: string | null
          address?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          created_by?: string | null
          id?: string
          industries?: string[] | null
          modified_at?: string | null
          modified_by?: string | null
          customer_full_name: string
          poc_email_id?: string | null
          poc_name?: string | null
          poc_phone_number?: string | null
          size?: string | null
          state?: string | null
          type?: string | null
        }
        Update: {
          account_owner?: string | null
          address?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          created_by?: string | null
          id?: string
          industries?: string[] | null
          modified_at?: string | null
          modified_by?: string | null
          customer_id?: string
          customer_full_name?: string
          poc_email_id?: string | null
          poc_name?: string | null
          poc_phone_number?: string | null
          size?: string | null
          state?: string | null
          type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_enquiries_fkey"
            columns: ["id"]
            isOneToOne: false
            referencedRelation: "enquiries"
            referencedColumns: ["customer_id"]
          }
        ]
      }
      customer_projects: {
        Row: {
          _id: string
          customer_id: string
          offset_chemical_id: string | null
          offset_chemical_name: string | null
          chemical_name: string
          project_type: 'ENQUIRY' | 'SAMPLE' | 'PURCHASE_ORDER' | 'NO_ACTIVITY'
          reference_id: string | null
          deleted: boolean | null
        }
        Insert: {
          _id?: string
          customer_id: string
          offset_chemical_id?: string | null
          offset_chemical_name?: string | null
          chemical_name: string
          project_type: 'ENQUIRY' | 'SAMPLE' | 'PURCHASE_ORDER' | 'NO_ACTIVITY'
          reference_id?: string | null
          deleted?: boolean | null
        }
        Update: {
          _id?: string
          customer_id?: string
          offset_chemical_id?: string | null
          offset_chemical_name?: string | null
          chemical_name?: string
          project_type?: 'ENQUIRY' | 'SAMPLE' | 'PURCHASE_ORDER' | 'NO_ACTIVITY'
          reference_id?: string | null
          deleted?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_projects_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer"
            referencedColumns: ["id"]
          }
        ]
      }
      customer_activity: {
        Row: {
          id: string
          activity_type: string | null
          description: string | null
          customer_id: string | null
          project_id: string | null
          reference_id: string | null
          created_by: string | null
          created_at: string
          enquiry_id: string | null
        }
        Insert: {
          id?: string
          activity_type?: string | null
          description?: string | null
          customer_id?: string | null
          project_id?: string | null
          reference_id?: string | null
          created_by?: string | null
          created_at?: string
          enquiry_id?: string | null
        }
        Update: {
          id?: string
          activity_type?: string | null
          description?: string | null
          customer_id?: string | null
          project_id?: string | null
          reference_id?: string | null
          created_by?: string | null
          created_at?: string
          enquiry_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_activity_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_activity_enquiry_id_fkey"
            columns: ["enquiry_id"]
            isOneToOne: false
            referencedRelation: "enquiries"
            referencedColumns: ["id"]
          }
        ]
      }
      account_planning: {
        Row: {
          id: string
          customer_id: string
          offset_chemical_id: string | null
          offset_chemical_name: string | null
          chemical_name: string
          grade: string
          quarterly_volume: number | null
          quarterly_volume_unit: string | null
          status: string
          quarterly_value: number | null
          previous_quarter_value: number | null
          target_this_quarter: number | null
          conversion_probability: number | null
          comments: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          customer_id: string
          offset_chemical_id?: string | null
          offset_chemical_name?: string | null
          chemical_name: string
          grade: string
          quarterly_volume?: number | null
          quarterly_volume_unit?: string | null
          status: string
          quarterly_value?: number | null
          previous_quarter_value?: number | null
          target_this_quarter?: number | null
          conversion_probability?: number | null
          comments?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          customer_id?: string
          offset_chemical_id?: string | null
          offset_chemical_name?: string | null
          chemical_name?: string
          grade?: string
          quarterly_volume?: number | null
          quarterly_volume_unit?: string | null
          status?: string
          quarterly_value?: number | null
          previous_quarter_value?: number | null
          target_this_quarter?: number | null
          conversion_probability?: number | null
          comments?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "account_planning_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer"
            referencedColumns: ["id"]
          }
        ]
      }
      account_planning_history: {
        Row: {
          id: string
          account_planning_id: string
          customer_id: string
          offset_chemical_id: string | null
          offset_chemical_name: string | null
          chemical_name: string
          grade: string
          quarterly_volume: number | null
          status: string
          quarterly_value: number | null
          previous_quarter_value: number | null
          target_this_quarter: number | null
          conversion_probability: number | null
          comments: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          account_planning_id: string
          customer_id: string
          offset_chemical_id?: string | null
          offset_chemical_name?: string | null
          chemical_name: string
          grade: string
          quarterly_volume?: number | null
          status: string
          quarterly_value?: number | null
          previous_quarter_value?: number | null
          target_this_quarter?: number | null
          conversion_probability?: number | null
          comments?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          account_planning_id?: string
          customer_id?: string
          offset_chemical_id?: string | null
          offset_chemical_name?: string | null
          chemical_name?: string
          grade?: string
          quarterly_volume?: number | null
          status?: string
          quarterly_value?: number | null
          previous_quarter_value?: number | null
          target_this_quarter?: number | null
          conversion_probability?: number | null
          comments?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "account_planning_history_account_planning_id_fkey"
            columns: ["account_planning_id"]
            isOneToOne: false
            referencedRelation: "account_planning"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_planning_history_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer"
            referencedColumns: ["id"]
          }
        ]
      }
      customer_quotations: {
        Row: {
          created_at: string | null
          created_by: string
          document_file_name: string | null
          document_file_path: string | null
          id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by: string
          document_file_name?: string | null
          document_file_path?: string | null
          id?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string
          document_file_name?: string | null
          document_file_path?: string | null
          id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      enquiries: {
        Row: {
          annual_procurement_scale: number | null
          application: string | null
          brand: string | null
          cas_number: string | null
          category: string | null
          chemical_name: string | null
          city: string | null
          confidence: string | null
          country: string
          created_at: string | null
          current_status: Database["public"]["Enums"]["enquiry_lifecycle_status"]
          customer_poc: string | null
          customer_email: string | null
          customer_full_name: string | null
          customer_phone: string | null
          description: string | null
          draft_id: string | null
          expected_procurement_volume: number | null
          expected_procurement_unit: string | null
          id: string
          incoterms: string | null
          industries: string[] | null
          is_new: boolean | null
          last_status_change: string | null
          notes: string | null
          procurement_unit: string | null
          procurement_volume: number | null
          product: string | null
          quantity: number
          quantity_unit: Database["public"]["Enums"]["unit_type"] | null
          quotation_location: string | null
          remarks: string | null
          sales_agent_id: string
          sales_team_member: string
          sample_requested: boolean | null
          sample_requested_at: string | null
          sampling_required: boolean | null
          target_price: number | null
          target_price_currency: string | null
          target_quotation_date: string | null
          customer_id : string | null
        }
        Insert: {
          annual_procurement_scale?: number | null
          application?: string | null
          brand?: string | null
          cas_number?: string | null
          category?: string | null
          chemical_name?: string | null
          offset_chemical_id?: string | null
          offset_chemical_name?: string | null
          city?: string | null
          confidence?: string | null
          country: string
          created_at?: string | null
          current_status?: Database["public"]["Enums"]["enquiry_lifecycle_status"]
          customer_poc?: string | null
          customer_email?: string | null
          customer_full_name?: string | null
          customer_phone?: string | null
          description?: string | null
          draft_id?: string | null
          expected_procurement_volume?: number | null
          expected_procurement_unit?: string | null
          id?: string
          incoterms?: string | null
          industries?: string[] | null
          last_status_change?: string | null
          notes?: string | null
          procurement_unit?: string | null
          procurement_volume?: number | null
          product?: string | null
          quantity: number
          quantity_unit?: Database["public"]["Enums"]["unit_type"] | null
          quotation_location?: string | null
          remarks?: string | null
          sales_agent_id: string
          sales_team_member: string
          sample_requested?: boolean | null
          sample_requested_at?: string | null
          sampling_required?: boolean | null
          target_price?: number | null
          target_quotation_date?: string | null
          customer_id : string | null
        }
        Update: {
          annual_procurement_scale?: number | null
          application?: string | null
          brand?: string | null
          cas_number?: string | null
          category?: string | null
          chemical_name?: string | null
          city?: string | null
          confidence?: string | null
          country?: string
          created_at?: string | null
          current_status?: Database["public"]["Enums"]["enquiry_lifecycle_status"]
          customer_poc?: string | null
          customer_email?: string | null
          customer_full_name?: string | null
          customer_phone?: string | null
          description?: string | null
          draft_id?: string | null
          expected_procurement_volume?: number | null
          expected_procurement_unit?: string | null
          id?: string
          incoterms?: string | null
          industries?: string[] | null
          last_status_change?: string | null
          notes?: string | null
          procurement_unit?: string | null
          procurement_volume?: number | null
          product?: string | null
          quantity?: number
          quantity_unit?: Database["public"]["Enums"]["unit_type"] | null
          quotation_location?: string | null
          remarks?: string | null
          sales_agent_id?: string
          sales_team_member?: string
          sample_requested?: boolean | null
          sample_requested_at?: string | null
          sampling_required?: boolean | null
          target_price?: number | null
          target_quotation_date?: string | null
          customer_id : string | null
        }
        Relationships: []
      }
      enquiry_clarifications: {
        Row: {
          created_at: string
          created_by: string
          enquiry_id: string
          id: string
          query: string
          resolved_at: string | null
          response: string | null
          status: string
        }
        Insert: {
          created_at?: string
          created_by: string
          enquiry_id: string
          id?: string
          query: string
          resolved_at?: string | null
          response?: string | null
          status?: string
        }
        Update: {
          created_at?: string
          created_by?: string
          enquiry_id?: string
          id?: string
          query?: string
          resolved_at?: string | null
          response?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "enquiry_clarifications_enquiry_id_fkey"
            columns: ["enquiry_id"]
            isOneToOne: false
            referencedRelation: "enquiries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_enquiry"
            columns: ["enquiry_id"]
            isOneToOne: false
            referencedRelation: "enquiries"
            referencedColumns: ["id"]
          },
        ]
      }
      enquiry_documents: {
        Row: {
          content_type: string | null
          created_at: string | null
          draft_id: string | null
          enquiry_id: string | null
          file_name: string
          file_path: string
          id: string
          size: number | null
          uploaded_by: string | null,
          document_type?: string | null
        }
        Insert: {
          content_type?: string | null
          created_at?: string | null
          draft_id?: string | null
          enquiry_id?: string | null
          file_name: string
          file_path: string
          id?: string
          size?: number | null
          uploaded_by?: string | null
          document_type?: string | null
        }
        Update: {
          content_type?: string | null
          created_at?: string | null
          draft_id?: string | null
          enquiry_id?: string | null
          file_name?: string
          file_path?: string
          id?: string
          size?: number | null
          uploaded_by?: string | null
          document_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "enquiry_documents_enquiry_id_fkey"
            columns: ["enquiry_id"]
            isOneToOne: false
            referencedRelation: "enquiries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "enquiry_documents_uploaded_by_fkey"
            columns: ["uploaded_by"]
            isOneToOne: false
            referencedRelation: "user_list"
            referencedColumns: ["id"]
          },
        ]
      }
      enquiry_quotations: {
        Row: {
          created_at: string | null
          enquiry_id: string
          id: string
          is_current: boolean | null
          quotation_id: string
        }
        Insert: {
          created_at?: string | null
          enquiry_id: string
          id?: string
          is_current?: boolean | null
          quotation_id: string
        }
        Update: {
          created_at?: string | null
          enquiry_id?: string
          id?: string
          is_current?: boolean | null
          quotation_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "enquiry_quotations_enquiry_id_fkey"
            columns: ["enquiry_id"]
            isOneToOne: false
            referencedRelation: "enquiries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "enquiry_quotations_quotation_id_fkey"
            columns: ["quotation_id"]
            isOneToOne: false
            referencedRelation: "customer_quotations"
            referencedColumns: ["id"]
          },
        ]
      }
      enquiry_status_history: {
        Row: {
          changed_by: string
          created_at: string | null
          enquiry_id: string
          id: string
          notes: string | null
          procurement_poc: string | null
          sales_agent_email: string
          status: Database["public"]["Enums"]["enquiry_lifecycle_status"]
        }
        Insert: {
          changed_by: string
          created_at?: string | null
          enquiry_id: string
          id?: string
          notes?: string | null
          procurement_poc?: string | null
          sales_agent_email: string
          status: Database["public"]["Enums"]["enquiry_lifecycle_status"]
        }
        Update: {
          changed_by?: string
          created_at?: string | null
          enquiry_id?: string
          id?: string
          notes?: string | null
          procurement_poc?: string | null
          sales_agent_email?: string
          status?: Database["public"]["Enums"]["enquiry_lifecycle_status"]
        }
        Relationships: [
          {
            foreignKeyName: "enquiry_status_history_enquiry_id_fkey"
            columns: ["enquiry_id"]
            isOneToOne: false
            referencedRelation: "enquiries"
            referencedColumns: ["id"]
          },
        ]
      }
      purchase_order_attachments: {
        Row: {
          content_type: string | null
          created_at: string | null
          doc_type: string | null
          file_name: string
          file_path: string
          id: string
          purchase_order_id: string
          size: number | null
        }
        Insert: {
          content_type?: string | null
          created_at?: string | null
          doc_type?: string | null
          file_name: string
          file_path: string
          id?: string
          purchase_order_id: string
          size?: number | null
        }
        Update: {
          content_type?: string | null
          created_at?: string | null
          doc_type?: string | null
          file_name?: string
          file_path?: string
          id?: string
          purchase_order_id?: string
          size?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "purchase_order_attachments_purchase_order_id_fkey"
            columns: ["purchase_order_id"]
            isOneToOne: false
            referencedRelation: "purchase_orders"
            referencedColumns: ["id"]
          },
        ]
      }
      purchase_orders: {
        Row: {
          created_at: string | null
          created_by: string
          enquiry_id: string
          id: string
          notes: string | null
          po_number: string
          po_value: string | null
          po_currency: string | null
          frequency: string | null
          status: string | null
        }
        Insert: {
          created_at?: string | null
          created_by: string
          enquiry_id: string
          id?: string
          notes?: string | null
          po_number?: string
          po_value?: string | null
          po_currency?: string | null
          frequency?: string | null
          status?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string
          enquiry_id?: string
          id?: string
          notes?: string | null
          po_number?: string
          po_value?: string | null
          po_currency?: string | null
          frequency?: string | null
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "purchase_orders_enquiry_id_fkey"
            columns: ["enquiry_id"]
            isOneToOne: false
            referencedRelation: "enquiries"
            referencedColumns: ["id"]
          },
        ]
      }
      quotation_feedback: {
        Row: {
          created_at: string | null
          enquiry_id: string
          id: string
          pricing_quote_id: string | null
          reason: string
          response: string
          submitted_by: string | null
        }
        Insert: {
          created_at?: string | null
          enquiry_id: string
          id?: string
          pricing_quote_id?: string | null
          reason: string
          response: string
          submitted_by?: string | null
        }
        Update: {
          created_at?: string | null
          enquiry_id?: string
          id?: string
          pricing_quote_id?: string | null
          reason?: string
          response?: string
          submitted_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "quotation_feedback_enquiry_id_fkey"
            columns: ["enquiry_id"]
            isOneToOne: false
            referencedRelation: "enquiries"
            referencedColumns: ["id"]
          },
        ]
      }
      quotation_feedback_attachments: {
        Row: {
          id: string
          feedback_id: string
          file_name: string
          file_path: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          feedback_id: string
          file_name: string
          file_path: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          feedback_id?: string
          file_name?: string
          file_path?: string
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "quotation_feedback_attachments_feedback_id_fkey"
            columns: ["feedback_id"]
            referencedRelation: "quotation_feedback"
            referencedColumns: ["id"]
          }
        ]
      }
      sample_feedback: {
        Row: {
          created_at: string | null
          enquiry_id: string
          feedback_history_id: string | null
          sample_request_id: string | null
          id: string
          reason: string
          response: string
          submitted_by: string | null
          type: string | null
          remarks: string | null
        }
        Insert: {
          created_at?: string | null
          enquiry_id: string
          feedback_history_id?: string | null
          sample_request_id?: string | null
          id?: string
          reason: string
          response: string
          submitted_by?: string | null
          type?: string | null
          remarks: string | null
        }
        Update: {
          created_at?: string | null
          enquiry_id?: string
          feedback_history_id?: string | null
          sample_request_id?: string | null
          id?: string
          reason?: string
          response?: string
          submitted_by?: string | null
          type?: string | null
          remarks: string | null
        }
        Relationships: [
          {
            foreignKeyName: "sample_feedback_enquiry_id_fkey"
            columns: ["enquiry_id"]
            isOneToOne: false
            referencedRelation: "enquiries"
            referencedColumns: ["id"]
          },
        ]
      }
      sample_feedback_followup: {
        Row: {
          created_at: string | null
          feedback_date: string | null
          remarks: string | null
          created_by: string | null
          sample_feedback_id: string | null
        }
        Insert: {
          created_at: string | null
          feedback_date: string | null
          remarks: string | null
          created_by: string | null
          sample_feedback_id: string | null
        }
        Update: {
          created_at: string | null
          feedback_date: string | null
          remarks: string | null
          created_by: string | null
          sample_feedback_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "sample_feedback_followup_sample_feedback_id_fkey",
            columns: ["sample_feedback_id"],
            isOneToOne: false,
            referencedRelation: "sample_feedback",
            referencedColumns: ["id"]
          }
        ]
      }
      sample_feedback_snooze: {
        Row: {
          process_stage: string | null,
          snooze_started_at: string | null,
          sample_request_id: string | null,
          snooze_ended_at: string | null,
        }
        Insert: {
          process_stage: string | null,
          snooze_started_at: string | null,
          sample_request_id: string | null,
          snooze_ended_at: string | null,
        }
        Update: {
          process_stage: string | null,
          snooze_started_at: string | null,
          sample_request_id: string | null,
          snooze_ended_at: string | null,
        }
      }
      sample_feedback_attachments: {
        Row: {
          id: string
          feedback_id: string
          file_name: string
          file_path: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          feedback_id: string
          file_name: string
          file_path: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          feedback_id?: string
          file_name?: string
          file_path?: string
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "sample_feedback_attachments_feedback_id_fkey"
            columns: ["feedback_id"]
            referencedRelation: "sample_feedback"
            referencedColumns: ["id"]
          }
        ]
      }
      sample_requests: {
        Row: {
          created_at: string | null
          created_by: string
          delivery_address: string
          contact_email: string | null
          contact_phone: string | null
          sample_poc: string | null
          delivery_city: string
          delivery_country: string
          delivery_postal_code: string
          enquiry_id: string
          id: string
          quantity: number
          quantity_unit: string
          remarks: string | null
          sales_team_member: string | null
          status: string | null
          status_history_id: string | null
        }
        Insert: {
          created_at?: string | null
          created_by: string
          delivery_address: string
          contact_email?: string | null
          contact_phone?: string | null
          sample_poc?: string | null
          delivery_city: string
          delivery_country: string
          delivery_postal_code: string
          enquiry_id: string
          id?: string
          quantity: number
          quantity_unit: string
          remarks?: string | null
          sales_team_member?: string | null
          status?: string | null
          status_history_id?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string
          delivery_address?: string
          contact_email?: string | null
          contact_phone?: string | null
          sample_poc?: string | null
          delivery_city?: string
          delivery_country?: string
          delivery_postal_code?: string
          enquiry_id?: string
          id?: string
          quantity?: number
          quantity_unit?: string
          remarks?: string | null
          sales_team_member?: string | null
          status?: string | null
          status_history_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "sample_requests_enquiry_id_fkey"
            columns: ["enquiry_id"]
            isOneToOne: false
            referencedRelation: "enquiries"
            referencedColumns: ["id"]
          },
        ]
      }
      sample_status_history: {
        Row: {
          id: string
          sample_request_id: string
          sample_status: string
          changed_at: string
        }
        Insert: {
          id?: string
          sample_request_id: string
          sample_status: string
          changed_at?: string
        }
        Update: {
          id?: string
          sample_request_id?: string
          sample_status?: string
          changed_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "sample_status_history_sample_request_id_fkey"
            columns: ["sample_request_id"]
            referencedRelation: "sample_requests"
            referencedColumns: ["id"]
          }
        ]
      }
      sample_requests_attachments: {
        Row: {
          id: string
          sample_request_id: string
          file_name: string
          file_path: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          sample_request_id: string
          file_name: string
          file_path: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          sample_request_id?: string
          file_name?: string
          file_path?: string
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "sample_requests_attachments_sample_request_id_fkey"
            columns: ["sample_request_id"]
            referencedRelation: "sample_requests"
            referencedColumns: ["id"]
          }
        ]
      }
      user_roles: {
        Row: {
          created_at: string | null
          id: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
          category: string | null
          country: string | null
          is_crm_enabled?: boolean | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
          category?: string | null
          country?: string | null
          is_crm_enabled?: boolean | null
        }
        Update: {
          created_at?: string | null
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id?: string
          category?: string | null
          country?: string | null
          is_crm_enabled?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "user_roles_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_list"
            referencedColumns: ["id"]
          },
        ]
      }
      quote_generation_details: {
        Row: {
          id: string
          enquiry_id: string
          generated_by: string
          generated_at: string | null
          pdf_file_path: string
          notes: string | null
          status_history_id: string | null
        }
        Insert: {
          id?: string
          enquiry_id: string
          generated_by: string
          generated_at?: string | null
          pdf_file_path: string
          notes?: string | null
          status_history_id?: string | null
        }
        Update: {
          id?: string
          enquiry_id?: string
          generated_by?: string
          generated_at?: string | null
          pdf_file_path?: string
          notes?: string | null
          status_history_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "quote_generation_details_enquiry_id_fkey"
            columns: ["enquiry_id"]
            isOneToOne: false
            referencedRelation: "enquiries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quote_generation_details_status_history_id_fkey"
            columns: ["status_history_id"]
            isOneToOne: false
            referencedRelation: "enquiry_status_history"
            referencedColumns: ["id"]
          }
        ]
      }
      quote_generation_options: {
        Row: {
          id: string
          quote_generation_id: string
          price: number
          quantity: number
          amount: number | null
          currency: string | null
          created_at: string | null
        }
        Insert: {
          id?: string
          quote_generation_id: string
          price: number
          quantity: number
          amount?: number | null
          currency?: string | null
          created_at?: string | null
        }
        Update: {
          id?: string
          quote_generation_id?: string
          price?: number
          quantity?: number
          amount?: number | null
          currency?: string | null
          created_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "quote_generation_options_quote_generation_id_fkey"
            columns: ["quote_generation_id"]
            isOneToOne: false
            referencedRelation: "quote_generation_details"
            referencedColumns: ["id"]
          }
        ]
      }
      'quote-generation-details': {
        Row: {
          id: string
          status_history_id: string
          pdf_file_path: string
          created_at?: string
        }
        Insert: {
          id?: string
          status_history_id: string
          pdf_file_path: string
          created_at?: string
        }
        Update: {
          id?: string
          status_history_id?: string
          pdf_file_path?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "quote_generation_details_status_history_id_fkey"
            columns: ["status_history_id"]
            referencedRelation: "enquiry_status_history"
            referencedColumns: ["id"]
          }
        ]
      }
      quote_generation_attachments: {
        Row: {
          id: string
          quote_generation_id: string
          file_name: string
          file_path: string
          content_type: string
          created_at?: string | null
          updated_at?: string | null
        }
        Insert: {
          id?: string
          quote_generation_id: string
          file_name: string
          file_path: string
          content_type: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          quote_generation_id?: string
          file_name?: string
          file_path?: string
          content_type?: string
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "quote_generation_attachments_quote_generation_id_fkey"
            columns: ["quote_generation_id"]
            referencedRelation: "quote_generation_details"
            referencedColumns: ["id"]
          }
        ]
      }
      customer_meeting: {
        Row: {
          id: string
          title: string | null
          customer_id: string | null
          account_owner: string | null
          date: string | null
          summary: string | null
          follow_up_steps: Json[] | null
          meeting_type: string | null
          created_at: string
        }
        Insert: {
          id?: string
          title?: string | null
          customer_id?: string | null
          account_owner?: string | null
          date?: string | null
          summary?: string | null
          follow_up_steps?: Json[] | null
          meeting_type?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          title?: string | null
          customer_id?: string | null
          account_owner?: string | null
          date?: string | null
          summary?: string | null
          follow_up_steps?: Json[] | null
          meeting_type?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "customer_meeting_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer"
            referencedColumns: ["id"]
          }
        ]
      }
      customer_contact: {
        Row: {
          id: string
          customer_id: string
          name: string | null
          email: string | null
          role: string | null
          is_primary: boolean | null
          phone: string | null
        }
        Insert: {
          id?: string
          customer_id: string
          name?: string | null
          email?: string | null
          role?: string | null
          is_primary?: boolean | null
          phone?: string | null
        }
        Update: {
          id?: string
          customer_id?: string
          name?: string | null
          email?: string | null
          role?: string | null
          is_primary?: boolean | null
          phone?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_contact_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer"
            referencedColumns: ["id"]
          }
        ]
      }
      customer_activity_history: {
        Row: {
          id: string
          customer_id: string
          remarks: string
          created_at: string
        }
        Insert: {
          id?: string
          customer_id: string
          remarks: string
          created_at?: string
        }
        Update: {
          id?: string
          customer_id?: string
          remarks?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "customer_activity_history_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      enquiry_status_analytics: {
        Row: {
          count: number | null
          current_status:
            | Database["public"]["Enums"]["enquiry_lifecycle_status"]
            | null
          percentage: number | null
        }
        Relationships: []
      }
      get_enquiry_board: {
        Row: {
          id: string
          chemical_name: string | null
          brand: string | null
          product: string | null
          cas_number: string | null
          country: string | null
          target_price: number | null
          quantity: number | null
          created_at: string | null
          sales_team_member: string | null
          notes: string | null
          sales_agent_id: string | null
          description: string | null
          draft_id: string | null
          sample_requested: boolean | null
          sample_requested_at: string | null
          current_status: string
          last_status_change: string | null
          target_quotation_date: string | null
          confidence: string | null
          application: string | null
          category: string | null
          incoterms: string | null
          remarks: string | null
          annual_procurement_scale: number | null
          sampling_required: boolean | null
          expected_procurement_volume: number | null
          industries: string[] | null
          procurement_volume: number | null
          city: string | null
          expected_procurement_unit: string | null
          procurement_unit: string | null
          quantity_unit: string | null
          customer_id: string | null
          customer_full_name: string | null
          is_new: boolean | null
          target_price_currency: string | null
          destination: string | null
          enquiry_id: string | null
          is_expired: boolean | null
        }
        Relationships: []
      }
      user_list: {
        Row: {
          email: string | null
          id: string | null
          role: Database["public"]["Enums"]["app_role"] | null
        }
        Relationships: []
      }
      user_role_view: {
        Row: {
          email: string | null
          role: Database["public"]["Enums"]["app_role"] | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_roles_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_list"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      create_purchase_order: {
        Args: {
          p_enquiry_id: string
          p_notes: string
          p_created_by: string
          p_po_number?: string
        }
        Returns: Json
      }
      create_purchase_order_attachment:
        | {
            Args: {
              p_purchase_order_id: string
              p_file_name: string
              p_file_path: string
              p_content_type: string
              p_size: number
            }
            Returns: boolean
          }
        | {
            Args: {
              p_purchase_order_id: string
              p_file_name: string
              p_file_path: string
              p_content_type: string
              p_size: number
              p_doc_type: string
            }
            Returns: boolean
          }
      generate_sample_request_number: {
        Args: {
          p_enquiry_id: string
        }
        Returns: string
      }
      get_user_list: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          email: string
          role: string
        }[]
      }
      has_role: {
        Args: {
          _user_id: string
          _role: Database["public"]["Enums"]["app_role"]
        }
        Returns: boolean
      }
    }
    Enums: {
      app_role: "sales" | "procurement" | "admin" | "bu_head"
      enquiry_lifecycle_status:
        | "cancelled"
        | "enquiry_created"
        | "enquiry_assigned"
        | "regret"
        | "clarification_needed"
        | "quote_accepted"
        | "quote_redo"
        | "quote_rejected"
        | "sample_requested"
        | "sample_available"
        | "sample_in_transit"
        | "sample_delivered"
        | "sample_accepted"
        | "sample_redo"
        | "sample_rejected"
        | "po_raised"
        | "pricing_quotation_generated"
        | "quote_revision_needed"
      unit_type:
        | "kg"
        | "g"
        | "lbs"
        | "pcs"
        | "Metric Ton (mt)"
        | "Pound (lb)"
        | "Gallon (gal)"
        | "Litre (L)"
        | "Kilolitre (Kl)"
        | "Kilogram (Kg)"
      project_type_enum: 'ENQUIRY' | 'SAMPLE' | 'PURCHASE_ORDER' | 'NO_ACTIVITY'
      conversion_probability_enum: 'high' | 'medium' | 'low'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
