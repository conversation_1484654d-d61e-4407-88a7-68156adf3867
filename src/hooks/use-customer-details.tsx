import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { usePurchaseOrderDetails } from "@/hooks/use-purchase-order-details";
import axios from "axios";
import { API_CONFIG } from "@/config/api";
import {convertToUSD} from "@/utils/currencyConversion";

async function fetchMargins(enquiryIds: string[]): Promise<Record<string, number>> {
  try {
    const response = await axios.post(
      `${API_CONFIG.baseUrl}/${API_CONFIG.endpoints.margins}`,
      {
        parameters: [
          {
            type: "category",
            target: ["variable", ["template-tag", "enquiry_ids"]],
            value: enquiryIds.join(', ')
          }
        ]
      },
      {
        headers: {
          'Authorization': `Bearer ${API_CONFIG.accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.data) {
      throw new Error('Failed to fetch margins');
    }

    const data = await response.data;
    return data.reduce((acc: Record<string, number>, item: { enquiry_id: string; average_margin: number }) => {
      acc[item.enquiry_id] = item.average_margin;
      return acc;
    }, {});
  } catch (error) {
    console.error('Error fetching margins:', error);
    return {};
  }
}

interface AchievementValue {
  chemical_name: string;
  achievement_value_current: number;
  achievement_value_previous: number;
}

async function fetchAchievementValues(customerId: string, date: string): Promise<AchievementValue[]> {
  try {
    const response = await axios.post(
      `${API_CONFIG.baseUrl}/${API_CONFIG.endpoints.achievement}`,
      {
        parameters: [
          {
            type: "category",
            target: ["variable", ["template-tag", "input_date"]],
            value: date
          },
          {
            type: "category",
            target: ["variable", ["template-tag", "customer_id"]],
            value: customerId
          }
        ]
      },
      {
        headers: {
          'Authorization': `Bearer ${API_CONFIG.accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.data) {
      throw new Error('Failed to fetch achievement values');
    }

    const data = await response.data;
    return data;
  } catch (error) {
    console.error('Error fetching achievement values:', error);
    return [];
  }
}

export function useCustomerDetails(customerId?: any, commonCustomerId?: any) {
  const [customer, setCustomer] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [enquiries, setEnquiries] = useState<any[]>([]);
  const [samples, setSamples] = useState<any[]>([]);
  const [purchaseOrders, setPurchaseOrders] = useState<any[]>([]);
  const [poNumbers, setPoNumbers] = useState<string[]>([]);
  const [rawPurchaseOrders, setRawPurchaseOrders] = useState<any[]>([]);
  
  // Fetch PO details using the hook only when we have PO numbers
  const { details: poDetails, loading: poDetailsLoading } = usePurchaseOrderDetails(
    poNumbers.length > 0 ? poNumbers : []
  );

  // Process purchase orders when PO details are loaded
  useEffect(() => {
    if (!poDetailsLoading && rawPurchaseOrders.length > 0) {
      const processedPOs = rawPurchaseOrders.map((po, poIndex) => {
        const poDetail = poDetails[po.po_number];
        const chemicalNameMatch = poDetail?.chemical_name === po.chemical_name;

        return {
          id: poIndex + 1,
          ...po,
          margin: chemicalNameMatch && poDetail?.margin_percentage 
            ? `${poDetail.margin_percentage.toFixed(2)}%` 
            : "",
          salesOrderValue: chemicalNameMatch && poDetail?.total_sales_order_value
            ? `${poDetail.total_sales_order_value}`
            : "",
          deliveryDate: chemicalNameMatch && poDetail?.delivery_date 
            ? new Date(poDetail.delivery_date).toISOString().split('T')[0]
            : "",
          paymentTerms: chemicalNameMatch ? poDetail?.payment_terms || "" : "",
        };
      });
      setPurchaseOrders(processedPOs);
    }
  }, [poDetails, poDetailsLoading, rawPurchaseOrders]);

  useEffect(() => {
    async function fetchData() {
      if (!customerId) return;

      try {
        setLoading(true);

        // Fetch customer data
        const { data: customerData, error: customerError } = await supabase
          .from("customer")
          .select(
            `
            *,
            contacts:customer_contact(*),
            planning:account_planning(*),
            customer_enquiries:enquiries(*,
              sample_requests(*,
                sample_feedback(*)
              ),
              quotation_feedback(*),
              purchase_orders(*),
              quote_generation_details(id, status_history_id),
              quote_generation_options:quote_generation_details(
              quote_generation_options(price, quantity, amount, currency)
            )
            )
          `
          )
          .eq("id", customerId.toString());

        if (customerError) throw customerError;

        // Fetch achievement values
        const currentDate = new Date().toISOString().split('T')[0];
        const achievementValues = await fetchAchievementValues(commonCustomerId?.toString(), currentDate);

        // Create a map of chemical names to achievement values
        const achievementMap = achievementValues.reduce((acc, item) => {
          acc[item.chemical_name.trim()] = {
            achievement_value_current: item.achievement_value_current,
            achievement_value_previous: item.achievement_value_previous
          };
          return acc;
        }, {} as Record<string, { achievement_value_current: number; achievement_value_previous: number }>);
        // Transform customer data
        const transformedData = customerData?.map((customer) => {
          // Map achievement values to account planning data
          if (Array.isArray(customer.planning)) {
            customer.planning = customer.planning.map(plan => {
              const achievementData = achievementMap[plan.chemical_name.trim()] || {
                achievement_value_current: 0,
                achievement_value_previous: 0
              };
              return {
                ...plan,
                achievement_value_current: achievementData.achievement_value_current,
                achievement_value_previous: achievementData.achievement_value_previous
              };
            });
          }

          return customer;
        }) || [];

        setCustomer(transformedData[0]);

        // Create a mapping of chemical names to their account planning data
        const chemicalToAccountPlanningMap = {};

        // Check if planning data exists and is an array
        if (Array.isArray(customerData[0].planning)) {
          // Build the mapping
          customerData[0].planning.forEach((planItem) => {
            if (planItem.chemical_name) {
              chemicalToAccountPlanningMap[
                planItem.chemical_name.toLowerCase()
              ] = planItem;
            }
          });
        }

        // Collect PO numbers first before processing purchase orders
        const allPoNumbers: string[] = [];
        const allPurchaseOrders: any[] = [];
        
        if (Array.isArray(customerData) && customerData.length > 0 && 
            Array.isArray(customerData[0].customer_enquiries)) {
          
          customerData[0].customer_enquiries.forEach(enquiry => {
            if (Array.isArray(enquiry.purchase_orders)) {
              enquiry.purchase_orders.forEach(po => {
                if (po.po_number) {
                  allPoNumbers.push(po.po_number);
                  
                  // Look up account planning data for this chemical
                  const chemicalName = enquiry.chemical_name || "";
                  const accountPlanningData = chemicalToAccountPlanningMap[chemicalName.toLowerCase()];

                  // Get expected quarterly procurement from account planning if available
                  let expectedQuarterlyProcurement = "N/A";
                  if (accountPlanningData && accountPlanningData.quarterly_value) {
                    expectedQuarterlyProcurement = `${accountPlanningData.quarterly_value.toLocaleString()}`;
                  }

                  allPurchaseOrders.push({
                    ...po,
                    chemical_name: enquiry.chemical_name,
                    requestDate: enquiry.created_at
                      ? new Date(enquiry.created_at).toISOString().split("T")[0]
                      : new Date().toISOString().split("T")[0],
                    conversionProbability: enquiry.confidence,
                    expectedQuarterlyProcurement,
                    orderValue: po.po_value ? convertToUSD(po.po_value,po.po_currency) : "N/A",
                    orderDate: po.created_at
                      ? new Date(po.created_at).toISOString().split("T")[0]
                      : new Date().toISOString().split("T")[0],
                    repeatFrequency: po.frequency || "N/A",
                    current_status: "-",
                    eta: "N/A", 
                  });
                }
              });
            }
          });
          
          setPoNumbers(allPoNumbers);
          setRawPurchaseOrders(allPurchaseOrders);
        }

        // Process enquiries and samples
        if (Array.isArray(customerData) && customerData.length > 0) {
          // Process enquiries and samples
          if (Array.isArray(customerData[0].customer_enquiries)) {
            // Collect enquiry IDs for margin fetching
            const enquiryIds = customerData[0].customer_enquiries
              .filter(enquiry => enquiry.id)
              .map(enquiry => enquiry.id);

            // Fetch margins for all enquiries
            const margins = await fetchMargins(enquiryIds);

            // Process samples
            const allSamples = [];
            const processedEnquiries = [];

            // Loop through each enquiry to extract its samples
            customerData[0].customer_enquiries.forEach((enquiry) => {
              // Calculate potential order value for the enquiry
              let potentialOrderValue = "N/A";
              let potentialOrderCurrency = "USD";

              // First try to get it from quote options
              if (
                Array.isArray(enquiry.quote_generation_options) &&
                enquiry.quote_generation_options.length > 0 &&
                Array.isArray(
                  enquiry.quote_generation_options[0]?.quote_generation_options
                )
              ) {
                const quoteOptions =
                  enquiry.quote_generation_options[0].quote_generation_options;

                if (quoteOptions.length > 0) {
                  // Calculate price*quantity for each option
                  const calculatedValues = quoteOptions
                    .filter(
                      (option) =>
                        option.price !== null &&
                        option.quantity !== null &&
                        option.price !== undefined &&
                        option.quantity !== undefined
                    )
                    .map((option) => {
                      // If amount is already calculated, use it
                      if (
                        option.amount !== null &&
                        option.amount !== undefined
                      ) {
                        return {
                          value: option.amount,
                          currency: option.currency || "USD",
                        };
                      }
                      // Otherwise calculate price * quantity
                      return {
                        value: option.price * option.quantity,
                        currency: option.currency || "USD",
                      };
                    });

                  if (calculatedValues.length > 0) {
                    // Find the minimum value
                    const minValue = calculatedValues.reduce(
                      (min, curr) => (curr.value < min.value ? curr : min),
                      calculatedValues[0]
                    );
                    potentialOrderValue = minValue.value.toFixed(2);
                    potentialOrderCurrency = minValue.currency;
                  }
                }
              }

              // If no quote options or couldn't calculate, try target price * quantity
              if (
                potentialOrderValue === "N/A" &&
                enquiry.target_price !== null &&
                enquiry.target_price !== undefined &&
                enquiry.quantity !== null &&
                enquiry.quantity !== undefined
              ) {
                const calculatedValue = enquiry.target_price * enquiry.quantity;
                const currency = enquiry.target_price_currency || "USD";
                
                // Convert to USD if not already in USD
                const valueInUSD = convertToUSD(calculatedValue, currency);
                
                potentialOrderValue = valueInUSD.toFixed(2);
                potentialOrderCurrency = "USD"; // Always store in USD
              }

               // Convert to USD if not already in USD
              const valueInUSD = convertToUSD(
                potentialOrderValue === "N/A" ? 0 : parseFloat(potentialOrderValue), 
                potentialOrderCurrency
              );

              potentialOrderValue = valueInUSD.toFixed(2);

              // Look up account planning data for this chemical
              const chemicalName = enquiry.chemical_name || "";
              const accountPlanningData =
                chemicalToAccountPlanningMap[chemicalName.toLowerCase()];

              // Get expected quarterly procurement from account planning if available
              let expectedQuarterlyProcurement = "N/A";
              if (accountPlanningData && accountPlanningData.quarterly_value) {
                expectedQuarterlyProcurement = `${accountPlanningData.quarterly_value.toLocaleString()}`;
              }

              

              // Process the enquiry itself with margin data
              processedEnquiries.push({
                ...enquiry,
                potentialOrderValue: potentialOrderValue !== "N/A" ? `${parseFloat(potentialOrderValue).toLocaleString()}`
                  : "N/A",
                potentialOrderCurrency,
                requestDate: enquiry.created_at
                  ? new Date(enquiry.created_at).toISOString().split("T")[0]
                  : new Date().toISOString().split("T")[0],
                expectedQuarterlyProcurement,
                conversionProbability: enquiry.confidence,
                closureDate: enquiry.quotation_feedback.length > 0
                  ? new Date(enquiry.quotation_feedback.sort((a, b) => 
                      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                    )[0]?.created_at).toISOString().split("T")[0]
                  : "",
                quotationFeedback: enquiry.quotation_feedback.length > 0
                  ? enquiry.quotation_feedback.sort((a, b) => 
                      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                    )[0]?.response || ""
                  : "",
                margin: margins[enquiry.id] ? `${margins[enquiry.id].toFixed(2)}%` : "N/A",
                current_status: enquiry.current_status || ""
              });

              // Get common fields from parent enquiry
              const commonFields = {
                chemical_name: enquiry.chemical_name || "Unknown",
                requestDate: enquiry.created_at
                  ? new Date(enquiry.created_at).toISOString().split("T")[0]
                  : new Date().toISOString().split("T")[0],
                conversionProbability: enquiry.confidence,
                expectedQuarterlyProcurement: expectedQuarterlyProcurement,
                potentialOrderValue: potentialOrderValue,
                potentialOrderCurrency: potentialOrderCurrency,
              };

              // Extract samples with margin data
              if (Array.isArray(enquiry.sample_requests)) {
                const samplesWithAdditionalFields = enquiry.sample_requests.map(
                  (sample) => ({
                    ...sample,
                    ...commonFields,
                    margin: margins[enquiry.id] ? `${margins[enquiry.id].toFixed(2)}%` : "N/A",
                    closureDate: sample?.sample_feedback?.length > 0
                      ? new Date(sample.sample_feedback.sort((a, b) => 
                          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                        )[0]?.created_at).toISOString().split("T")[0]
                      : "",
                    sampleFeedback: sample?.sample_feedback?.length > 0
                      ? sample.sample_feedback.sort((a, b) => 
                          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                        )[0]?.response || ""
                      : "",
                    potentialOrderValue: potentialOrderValue,
                    potentialOrderCurrency: potentialOrderCurrency,
                    enquiryId: enquiry.enquiry_id,
                    confidence: enquiry.confidence,
                    eta: sample.expected_delivery_date ? new Date(sample.expected_delivery_date).toISOString().split("T")[0] : "",
                    current_status: sample.status || "",
                    tracking_url: sample.tracking_url || ""
                  })
                );
                allSamples.push(...samplesWithAdditionalFields);
              }
            });

            // Set all data at once
            setEnquiries(processedEnquiries);
            setSamples(allSamples);
          }
        }
      } catch (err: any) {
        setError(err);
        toast({
          title: "Error fetching customer data",
          description: err.message,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [customerId]);

  return {
    customer,
    enquiries,
    samples,
    purchaseOrders,
    poDetails,
    poDetailsLoading,
    loading,
    error,
  };
}
