import { useState, useEffect, useCallback, useRef } from 'react';

export function useLocalStorage<T>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void] {
  // Get from local storage then parse stored json or return initialValue
  const readValue = (): T => {
    // Prevent build error "window is undefined" but keep working
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      if (item) {
        try {
          // Try to parse the JSON
          return JSON.parse(item) as T;
        } catch (parseError) {
          console.warn(`Error parsing localStorage key "${key}":`, parseError);
          // If parsing fails, clear the corrupted data
          window.localStorage.removeItem(key);
          return initialValue;
        }
      }
      return initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  };

  // State to store our value
  // Pass initial state function to useState so logic is only executed once
  const [storedValue, setStoredValue] = useState<T>(readValue);

  // Use a ref to track if this is the first render
  const isFirstRender = useRef(true);

  // Use a ref to track the current value to avoid stale closures
  const latestValue = useRef(storedValue);

  // Update the ref when storedValue changes
  useEffect(() => {
    latestValue.current = storedValue;
  }, [storedValue]);

  // Debounce timer ref
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Function to save to localStorage
  const saveToLocalStorage = useCallback((value: T) => {
    if (typeof window !== 'undefined') {
      try {
        const valueToStore = JSON.stringify(value);
        window.localStorage.setItem(key, valueToStore);
      } catch (error) {
        console.warn(`Error saving to localStorage key "${key}":`, error);
      }
    }
  }, [key]);

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  const setValue = useCallback((value: T | ((val: T) => T)) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(latestValue.current) : value;

      // Save to state immediately (this updates the UI)
      setStoredValue(valueToStore);

      // Update the ref
      latestValue.current = valueToStore;

      // Clear any existing debounce timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // Save to localStorage with debounce
      debounceTimerRef.current = setTimeout(() => {
        saveToLocalStorage(valueToStore);
      }, 300);
    } catch (error) {
      console.warn(`Error in setValue for key "${key}":`, error);
    }
  }, [key, saveToLocalStorage]);

  // Save to localStorage when the component mounts and whenever storedValue changes
  useEffect(() => {
    // Skip the first render
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // Save to localStorage
    saveToLocalStorage(storedValue);
  }, [storedValue, saveToLocalStorage]);

  useEffect(() => {
    // Update state if localStorage value changes in another tab/window
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === key && event.newValue) {
        try {
          setStoredValue(JSON.parse(event.newValue));
        } catch (error) {
          console.warn(`Error parsing storage event for key "${key}":`, error);
        }
      }
    };

    // Listen for changes to this localStorage key in other tabs/windows
    window.addEventListener('storage', handleStorageChange);

    return () => {
      // Clean up debounce timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [key]);

  return [storedValue, setValue];
}
