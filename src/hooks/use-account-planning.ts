import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface AccountPlanningItem {
  id: string;
  customer_id: string;
  offset_chemical_id?: string | null;
  offset_chemical_name?: string | null;
  chemical_name: string;
  grade: string;
  quarterly_volume: number | null;
  quarterly_volume_unit?: string | null;
  status: string;
  quarterly_value: number | null;
  previous_quarter_value: number | null;
  target_this_quarter: number | null;
  conversion_probability: number | null;
  comments: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export const useAccountPlanning = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const updateAccountPlanningItem = async (
    item: AccountPlanningItem,
    originalItem: AccountPlanningItem
  ) => {
    setIsSubmitting(true);
    setError(null);

    try {

      // First update the account planning item
      const { error: updateError } = await supabase
        .from('account_planning')
        .update({
          chemical_name: item.chemical_name,
          grade: item.grade,
          offset_chemical_id: item.offset_chemical_id,
          offset_chemical_name: item.offset_chemical_name,
          quarterly_volume: item.quarterly_volume,
          quarterly_volume_unit:item.quarterly_volume_unit,
          status: item.status,
          quarterly_value: item.quarterly_value,
          previous_quarter_value: item.previous_quarter_value,
          target_this_quarter: item.target_this_quarter,
          conversion_probability: item.conversion_probability,
          comments: item.comments,
          updated_at: new Date().toISOString()
        })
        .eq('id', item.id);

      if (updateError) {
        throw new Error(`Failed to update account planning: ${updateError.message}`);
      }

      toast.success('Account planning updated successfully');
      return { success: true };
    } catch (err: any) {
      console.error('Error in updateAccountPlanningItem:', err);
      setError(err);
      toast.error(err.message || 'Failed to update account planning');
      return { success: false, error: err };
    } finally {
      setIsSubmitting(false);
    }
  };

  const deleteAccountPlanningItem = async (item: AccountPlanningItem) => {
    setIsSubmitting(true);
    setError(null);

    try {
      // First delete related rows in the child table
      const { error: historyDeleteError } = await supabase
        .from('account_planning_history')
        .delete()
        .eq('account_planning_id', item.id);

      if (historyDeleteError) {
        return;
      }

      // Then delete the account planning item
      const { error: deleteError } = await supabase
        .from('account_planning')
        .delete()
        .eq('id', item.id);

      if (deleteError) {
        throw new Error(`Failed to delete account planning: ${deleteError.message}`);
      }

      toast.success('Account planning item deleted successfully');
      return { success: true };
    } catch (err: any) {
      console.error('Error in deleteAccountPlanningItem:', err);
      setError(err);
      toast.error(err.message || 'Failed to delete account planning item');
      return { success: false, error: err };
    } finally {
      setIsSubmitting(false);
    }
  };

  const addAccountPlanningItem = async (
    item: Omit<AccountPlanningItem, 'id' | 'created_at' | 'updated_at'>
  ) => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Insert new account planning item
      const { data, error: insertError } = await supabase
        .from('account_planning')
        .insert({
          customer_id: item.customer_id,
          offset_chemical_id: item.offset_chemical_id || null,
          offset_chemical_name: item.offset_chemical_name || null,
          chemical_name: item.chemical_name,
          grade: item.grade,
          quarterly_volume: item.quarterly_volume || 0,
          quarterly_volume_unit: item?.quarterly_volume_unit || "",
          status: item.status,
          quarterly_value: item.quarterly_value || 0,
          previous_quarter_value: item.previous_quarter_value || 0,
          target_this_quarter: item.target_this_quarter || 0,
          conversion_probability: item.conversion_probability,
          comments: item.comments || '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (insertError) {
        throw new Error(`Failed to add account planning: ${insertError.message}`);
      }

      // Create a history record for the new item
      if (data) {
        // const { error: historyError } = await supabase
        //   .from('account_planning_history')
        //   .insert({
        //     account_planning_id: data.id,
        //     customer_id: data.customer_id,
        //     offset_chemical_id: data.offset_chemical_id,
        //     offset_chemical_name: data.offset_chemical_name,
        //     chemical_name: data.chemical_name,
        //     grade: data.grade,
        //     quarterly_volume: data.quarterly_volume,
        //     status: data.status,
        //     quarterly_value: data.quarterly_value,
        //     previous_quarter_value: data.previous_quarter_value,
        //     target_this_quarter: data.target_this_quarter,
        //     conversion_probability: data.conversion_probability,
        //     comments: data.comments,
        //     created_at: new Date().toISOString(),
        //     updated_at: new Date().toISOString()
        //   });

        // if (historyError) {
        //   console.error('Error creating history record for new item:', historyError);
        //   // Don't throw here, just log the error since the item was already created
        // }
      }

      toast.success('Account planning item added successfully');
      return { success: true, data };
    } catch (err: any) {
      console.error('Error in addAccountPlanningItem:', err);
      setError(err);
      toast.error(err.message || 'Failed to add account planning item');
      return { success: false, error: err };
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    updateAccountPlanningItem,
    deleteAccountPlanningItem,
    addAccountPlanningItem,
    isSubmitting,
    error
  };
};
