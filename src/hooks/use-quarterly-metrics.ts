import { useState, useEffect } from 'react';
import axios from 'axios';
import { API_CONFIG } from '@/config/api';

export type QuarterlyMetrics = {
  repeated_rate: number;
  order_count: number | null;
  end_date: string;
  share_wallet: number | null;
  procurement_percentage: number;
  total_revenue: number | null;
  sum_target: number;
  avg_annual_turnover: number;
  to_be_invoiced_revenue: number | null;
  sales_stack_customer_id: string;
  common_customer_id: string;
  invoiced_revenue: number | null;
  start_date: string;
  customer_full_name: string;
};

export type QuarterFilter = 'current' | 'previous';

export function useQuarterlyMetrics(quarter: QuarterFilter = 'current') {
  const [metrics, setMetrics] = useState<QuarterlyMetrics[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function fetchMetrics() {
      try {
        setLoading(true);
        const response = await axios.post(
          `${API_CONFIG.baseUrl}/${API_CONFIG.endpoints.quarterlyMetrics}`,
          {
            parameters: [
              {
                type: "category",
                target: ["variable", ["template-tag", "period"]],
                value: quarter === 'current' ? "current_quarter" : "previous_quarter"
              }
            ]
          },
          {
            headers: {
              'Authorization': `Bearer ${API_CONFIG.accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (response && response.data && Array.isArray(response.data)) {
          setMetrics(response.data);
        } else {
          setMetrics([]);
        }
      } catch (err) {
        setError(err instanceof Error ? err : new Error('An error occurred'));
        console.error('Error fetching quarterly metrics:', err);
      } finally {
        setLoading(false);
      }
    }

    fetchMetrics();
  }, [quarter]);

  return { metrics, loading, error };
}