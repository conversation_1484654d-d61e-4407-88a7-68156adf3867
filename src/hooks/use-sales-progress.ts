import { useState, useEffect } from "react";
import axios from 'axios';
import { supabase } from "@/integrations/supabase/client";
import { API_CONFIG } from "@/config/api";

export type ChemicalSales = {
  name: string;
  value: number | null;
  target: number | null;
  variance: number | null;
  performance: number | null;
  achievement_percent: number | null;
};

export type QuarterlySales = {
  quarter: string;
  year: string;
  quarterNumber: string;
  actual: number;
  target: number;
  variance: number;
  performance: number;
  customerId: string;
  chemicals: ChemicalSales[];
};

interface SalesProgressParams {
  year?: string;
  quarter?: string;
  customerId?: string;
  accountOwner?: string;
}

interface SalesProgressResponse {
  year: number;
  quarter: number;
  chemical_name: string;
  achievement_value: number | null;
  achievement_value_total: number | null;
  achievement_percent: number | null;
  total_target: number | null;
  total_variance: number | null;
  total_performance: number | null;
  target: number | null;
  variance: number | null;
  performance: number | null;
}

export function useSalesProgress({
  year = 'allYears',
  quarter = 'All',
  customerId = 'All',
  accountOwner,
}: SalesProgressParams = {}) {
  const [quarterlyData, setQuarterlyData] = useState<QuarterlySales[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [requestId, setRequestId] = useState(0);

  useEffect(() => {
    let isCurrentRequest = true;
    const currentRequestId = requestId + 1;
    setRequestId(currentRequestId);

    async function fetchSalesProgress() {
      try {
        if(!accountOwner) return;
        setLoading(true);

        // Get current user from localStorage
        const user = {
          email: localStorage.getItem('userEmail'),
          role: localStorage.getItem('userRole'),
          category: localStorage.getItem('userCategory')
        };

        let response;
        
        // If user is BU head, use a different API endpoint
        if (user?.role === 'bu_head') {
          // Fetch all account owners in the category
          const { data: categoryUsers, error: usersError } = await supabase
            .from('user_list')
            .select('email, user_roles!inner(*)')
            .eq('user_roles.category', user?.category);

          if (usersError) throw usersError;

          // Get the list of emails from the users
          const accountOwners = categoryUsers?.map(user => user.email) || [];
          const accountOwnersString = accountOwners.join(',');

          // Fetch all customers for these account owners
          const { data: customers, error: customersError } = await supabase
            .from('customer')
            .select('id,customer_id')
            .in('account_owner', accountOwners);

          if (customersError) throw customersError;

          // Get the list of customer IDs
          const customerIds = customers?.map(customer => customer.customer_id) || [];
          const customerIdsString = customerIds.join(',');

          response = await axios.post<SalesProgressResponse[]>(
            `${API_CONFIG.baseUrl}/${API_CONFIG.endpoints.salesProgressBuHead}`,
            {
              parameters: [
                {
                  type: "category",
                  target: ["variable", ["template-tag", "year"]],
                  value: year
                },
                {
                  type: "category",
                  target: ["variable", ["template-tag", "quarter"]],
                  value: quarter
                },
                {
                  type: "category",
                  target: ["variable", ["template-tag", "customer_ids"]],
                  value: customerId === 'All' && accountOwner === 'All' 
                    ? customerIdsString 
                    : customerId === 'All' 
                      ? customerIdsString
                      : customerId
                },
                {
                  type: "category",
                  target: ["variable", ["template-tag", "account_owners"]],
                  value: customerId === 'All' && accountOwner === 'All'
                    ? accountOwnersString
                    : accountOwner === 'All'
                      ? accountOwnersString
                      : accountOwner
                }
              ]
            },
            {
              headers: {
                'Authorization': `Bearer ${API_CONFIG.accessToken}`,
                'Content-Type': 'application/json'
              }
            }
          );
        } else {
          // Regular API call for non-BU head users
          response = await axios.post<SalesProgressResponse[]>(
            `${API_CONFIG.baseUrl}/${API_CONFIG.endpoints.salesProgress}`,
            {
              parameters: [
                {
                  type: "category",
                  target: ["variable", ["template-tag", "year"]],
                  value: year
                },
                {
                  type: "category",
                  target: ["variable", ["template-tag", "quarter"]],
                  value: quarter
                },
                {
                  type: "category",
                  target: ["variable", ["template-tag", "customer_id"]],
                  value: customerId
                },
                {
                  type: "category",
                  target: ["variable", ["template-tag", "account_owner"]],
                  value: customerId === "All" ? accountOwner : "All"
                }
              ]
            },
            {
              headers: {
                'Authorization': `Bearer ${API_CONFIG.accessToken}`,
                'Content-Type': 'application/json'
              }
            }
          );
        }

        // Only update state if this is still the current request
        if (isCurrentRequest && currentRequestId === requestId + 1) {
          if (response && response.data && Array.isArray(response.data)) {
            // Create a map to group data by year and quarter
            const quarterlyMap = new Map<string, {
              actual: number;
              target: number;
              variance: number;
              performance: number;
              chemicals: Map<string, ChemicalSales>;
            }>();

            // Process each item in the response
            response.data.forEach(item => {
              const quarterKey = `Q${item.quarter} ${item.year}`;
              
              // Initialize quarter data if it doesn't exist
              if (!quarterlyMap.has(quarterKey)) {
                quarterlyMap.set(quarterKey, {
                  actual: 0,
                  target: 0,
                  variance: 0,
                  performance: 0,
                  chemicals: new Map()
                });
              }

              const quarterData = quarterlyMap.get(quarterKey)!;
              
              // Add chemical data
              quarterData.chemicals.set(item.chemical_name, {
                name: item.chemical_name,
                value: item.achievement_value,
                target: item.target,
                achievement_percent: item.achievement_percent,
                variance: item.variance,
                performance: item.performance
              });

              // Update quarter totals
              if (item?.achievement_value_total) {
                quarterData.actual = item.achievement_value_total;
              }
              if (item?.total_target) {
                quarterData.target = item.total_target;
              }
              if(item?.total_variance) {
                quarterData.variance = item.total_variance;
              }
              if(item?.total_performance) {
                quarterData.performance = item.total_performance;
              }
            });

            // Convert map to array and sort by year and quarter
            const processedData: QuarterlySales[] = Array.from(quarterlyMap.entries())
              .map(([quarter, data]) => {
                const [quarterNumber, year] = quarter.split(' ');
                
                return {
                  quarter,
                  year,
                  quarterNumber,
                  actual: data.actual,
                  target: data.target,
                  variance: data.variance,
                  performance: data.performance,
                  customerId,
                  chemicals: Array.from(data.chemicals.values())
                };
              })
              .sort((a, b) => {
                // Sort by year (descending) and then by quarter (ascending)
                if (a.year !== b.year) {
                  return parseInt(b.year) - parseInt(a.year);
                }
                return parseInt(a.quarterNumber.substring(1)) - parseInt(b.quarterNumber.substring(1));
              });

            setQuarterlyData(processedData);
          } else {
            setQuarterlyData([]);
          }
        }
      } catch (err) {
        if (isCurrentRequest && currentRequestId === requestId + 1) {
          setError(err instanceof Error ? err : new Error('An error occurred'));
          console.error('Error fetching sales progress:', err);
        }
      } finally {
        if (isCurrentRequest && currentRequestId === requestId + 1) {
          setLoading(false);
        }
      }
    }

    fetchSalesProgress();

    return () => {
      isCurrentRequest = false;
    };
  }, [year, quarter, customerId, accountOwner]);

  return { quarterlyData, loading, error };
}