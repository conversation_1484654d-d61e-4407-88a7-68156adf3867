import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import axios from "axios";
import { API_CONFIG } from "@/config/api";
import {convertToUSD} from "@/utils/currencyConversion";

const BATCH_SIZE = 1000;

export type ProjectItem = {
  id: number;
  chemicalName: string;
  customerId: string;
  [key: string]: any;
};

export type EnquirySample = ProjectItem & {
  potentialOrderValue: string;
  expectedQuarterlyProcurement: string;
  margin: string;
  requestDate: string;
  conversionProbability: string;
  accountOwner: string;
};

export type Order = ProjectItem & {
  orderValue: string;
  expectedQuarterlyProcurement: string;
  margin: string;
  orderDate: string;
  repeatFrequency: string;
  salesOrderValue: string;
  deliveryDate: string;
  paymentTerms: string;
  accountOwner: string;
};

export type ProjectsData = {
  enquiries: EnquirySample[];
  samples: EnquirySample[];
  orders: Order[];
};

export type AccountOwner = {
  id: string;
  name: string;
};

export function useProjectsData() {
  const [projectsData, setProjectsData] = useState<ProjectsData>({
    enquiries: [],
    samples: [],
    orders: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);

        // Get current user from localStorage
        const user = {
          email: localStorage.getItem('userEmail'),
          role: localStorage.getItem('userRole'),
          category: localStorage.getItem('userCategory')
        };

        // First get total count
        const { count } = await supabase
          .from("customer")
          .select("*", { count: 'exact', head: true });

        if (!count) {
          setProjectsData({ enquiries: [], samples: [], orders: [] });
          setLoading(false);
          return;
        }

        // Calculate number of batches needed
        const numBatches = Math.ceil(count / BATCH_SIZE);
        let allCustomersData: any[] = [];

        // Fetch each batch
        for (let i = 0; i < numBatches; i++) {
          const start = i * BATCH_SIZE;
          const end = start + BATCH_SIZE - 1;

          let query = supabase
            .from("customer")
            .select(`
              id,
              customer_full_name,
              account_owner,
              customer_enquiries:enquiries(*,
                sample_requests(*,
                  sample_feedback(*)
                ),
                quotation_feedback(*),
                purchase_orders(*),
                quote_generation_details(id, status_history_id),
                quote_generation_options:quote_generation_details(
                  quote_generation_options(price, quantity, amount, currency)
                )
              )
            `)
            .range(start, end);

          // Apply filters based on user role
          if (user?.role === 'sales') {
            query = query.eq('account_owner', user.email);
          } else if (user?.role === 'bu_head') {
            // First get all users of the category from user_roles
            const { data: categoryUsers, error: usersError } = await supabase
              .from('user_list')
              .select('email, user_roles!inner(*)')
              .eq('user_roles.category', user?.category);

            if (usersError) throw usersError;

            // Get the list of emails from the users
            const accountOwners = categoryUsers?.map(user => user.email) || [];

            // Filter customers by these account owners
            query = query.in('account_owner', accountOwners);
          }

          const { data: batchCustomers, error: batchError } = await query;

          if (batchError) throw batchError;
          if (batchCustomers) {
            allCustomersData = [...allCustomersData, ...batchCustomers];
          }
        }

        const allPoNumbers: string[] = [];

        allCustomersData.forEach(customer => {
          if (Array.isArray(customer.customer_enquiries)) {
            customer.customer_enquiries.forEach(enquiry => {
              if (Array.isArray(enquiry.purchase_orders)) {
                enquiry.purchase_orders.forEach(po => {
                  if (po.po_number) {
                    allPoNumbers.push(po.po_number);
                  }
                });
              }
            });
          }
        });

        // Fetch PO details
        const poDetails = await fetchPoDetails(allPoNumbers);

        // Collect all enquiry IDs for margin fetching
        const enquiryIds: string[] = [];
        allCustomersData.forEach(customer => {
          if (Array.isArray(customer.customer_enquiries)) {
            customer.customer_enquiries.forEach(enquiry => {
              if (enquiry.id) {
                enquiryIds.push(enquiry.id);
              }
            });
          }
        });

        // Fetch margins for all enquiries
        const margins = await fetchMargins(enquiryIds);

        const processedEnquiries: EnquirySample[] = [];
        const processedSamples: EnquirySample[] = [];
        const processedOrders: Order[] = [];

        allCustomersData.forEach((customer) => {
          if (Array.isArray(customer.customer_enquiries)) {
            customer.customer_enquiries.forEach((enquiry, index) => {
              let potentialOrderValue = "N/A";
              let potentialOrderCurrency = "USD";

              if (
                Array.isArray(enquiry.quote_generation_options) &&
                enquiry.quote_generation_options.length > 0 &&
                Array.isArray(
                  enquiry.quote_generation_options[0]?.quote_generation_options
                )
              ) {
                const quoteOptions =
                  enquiry.quote_generation_options[0].quote_generation_options;

                if (quoteOptions.length > 0) {
                  const calculatedValues = quoteOptions
                    .filter(
                      (option) =>
                        option.price !== null &&
                        option.quantity !== null
                    )
                    .map((option) => {
                      if (option.amount !== null && option.amount !== undefined) {
                        return {
                          value: option.amount,
                          currency: option.currency || "USD",
                        };
                      }
                      return {
                        value: option.price * option.quantity,
                        currency: option.currency || "USD",
                      };
                    });

                  if (calculatedValues.length > 0) {
                    const minValue = calculatedValues.reduce(
                      (min, curr) => (curr.value < min.value ? curr : min),
                      calculatedValues[0]
                    );

                    potentialOrderValue = minValue.value.toFixed(2);
                    potentialOrderCurrency = minValue.currency;
                  }
                }
              }

              if (
                potentialOrderValue === "N/A" &&
                enquiry.target_price !== null &&
                enquiry.quantity !== null
              ) {
                potentialOrderValue = (enquiry.target_price * enquiry.quantity).toFixed(2);
                potentialOrderCurrency = enquiry.target_price_currency || "USD";
              }

              // Convert to USD if not already in USD
              const valueInUSD = convertToUSD(
              potentialOrderValue === "N/A" ? 0 : parseFloat(potentialOrderValue), 
              potentialOrderCurrency
            );

              potentialOrderValue = valueInUSD.toFixed(2);


              const expectedQuarterlyProcurement = enquiry.expected_procurement_volume
                ? `$${enquiry.expected_procurement_volume.toLocaleString()}`
                : "N/A";

              const enquiryObj: EnquirySample = {
                id: enquiry.enquiry_id,
                chemicalName: enquiry.chemical_name || "",
                potentialOrderValue: potentialOrderValue,
                expectedQuarterlyProcurement,
                margin: enquiry.id && margins[enquiry.id] ? `${margins[enquiry.id].toFixed(2)}%` : "N/A",
                requestDate: enquiry.created_at
                  ? new Date(enquiry.created_at).toISOString().split("T")[0]
                  : new Date().toISOString().split("T")[0],
                conversionProbability: enquiry.confidence ? `${enquiry.confidence}` : "N/A",
                customerId: customer.id.toString(),
                customerName: customer.customer_full_name || "",
                accountOwner: customer.account_owner || "",
                closureDate: enquiry.quotation_feedback.length > 0
                ? new Date(enquiry.quotation_feedback.sort((a, b) => 
                    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                  )[0]?.created_at).toISOString().split("T")[0]
                : "",
               quotationFeedback: enquiry.quotation_feedback.length > 0
                ? enquiry.quotation_feedback.sort((a, b) => 
                    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                  )[0]?.response || ""
                : "",
              };

                processedEnquiries.push(enquiryObj);

              if (Array.isArray(enquiry.sample_requests) && enquiry.sample_requests.length > 0) {
                enquiry.sample_requests.forEach((sample, sampleIndex) => {
                  processedSamples.push({
                    ...enquiryObj,
                    id: enquiry.enquiry_id,
                    requestDate: sample.created_at
                      ? new Date(sample.created_at).toISOString().split("T")[0]
                      : enquiryObj.requestDate,
                    closureDate: sample?.sample_feedback?.length > 0
                    ? new Date(sample.sample_feedback.sort((a, b) => 
                        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                      )[0]?.created_at).toISOString().split("T")[0]
                    : "",
                    customerName: customer.customer_full_name || "",
                    accountOwner: customer.account_owner || "",
                    sampleFeedback: sample?.sample_feedback?.length > 0
                    ? sample.sample_feedback.sort((a, b) => 
                        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                      )[0]?.response || ""
                    : "",
                    current_status: sample.status || "",
                    eta: sample.expected_delivery_date ? new Date(sample.expected_delivery_date).toISOString().split("T")[0] : "",
                    tracking_url: sample.tracking_url || ""
                  });
                });
              }

              if (Array.isArray(enquiry.purchase_orders) && enquiry.purchase_orders.length > 0) {
                enquiry.purchase_orders.forEach((po, poIndex) => {
                  const poDetail = poDetails[po.po_number];
                  const chemicalNameMatch = poDetail?.chemical_name === enquiry.chemical_name;
                  
                  processedOrders.push({
                    id: poIndex + 1,
                    chemicalName: enquiry.chemical_name || "Unknown",
                    orderValue: po.po_value
                      ? `${convertToUSD(po.po_value,po.po_currency).toLocaleString()}`
                      : "N/A",
                    expectedQuarterlyProcurement,
                    margin: chemicalNameMatch && poDetail?.margin_percentage 
                      ? `${poDetail.margin_percentage.toFixed(2)}%` 
                      : "N/A",
                    orderDate: po.created_at
                      ? new Date(po.created_at).toISOString().split("T")[0]
                      : new Date().toISOString().split("T")[0],
                    repeatFrequency: po.frequency || "N/A",
                    poNumber: po.po_number,
                    customerId: customer.id.toString(),
                    accountOwner: customer.account_owner || "",
                    customerName: customer.customer_full_name || "",
                    salesOrderValue: chemicalNameMatch && poDetail?.total_sales_order_value
                      ? `${poDetail.total_sales_order_value}`
                      : "N/A",
                    deliveryDate: chemicalNameMatch && poDetail?.delivery_date 
                      ? new Date(poDetail.delivery_date).toISOString().split('T')[0]
                      : "N/A",
                    paymentTerms: chemicalNameMatch ? poDetail?.payment_terms || "N/A" : "N/A"
                  });
                });
              }
            });
          }
        });

        setProjectsData({
          enquiries: processedEnquiries,
          samples: processedSamples,
          orders: processedOrders
        });
      } catch (err: any) {
        setError(err);
        toast({
          title: "Error fetching projects data",
          description: err.message,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  return { projectsData, loading, error };
}

// Helper function to fetch PO details
async function fetchPoDetails(poNumbers: string[]): Promise<Record<string, any>> {
  if (!poNumbers.length) {
    return {};
  }

  try {
    const response = await axios.post(
      `${API_CONFIG.baseUrl}/${API_CONFIG.endpoints.purchaseOrderDetails}`,
      {
        parameters: [
          {
            type: "category",
            target: ["variable", ["template-tag", "po_numbers_csv"]],
            value: poNumbers.join(","),
          },
        ],
      },
      {
        headers: {
          'Authorization': `Bearer ${API_CONFIG.accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.data) {
      throw new Error('Failed to fetch PO details');
    }

    const data = await response.data;
    if (Array.isArray(data)) {
      return data.reduce((acc, item) => {
        acc[item.purchase_order_number] = item;
        return acc;
      }, {});
    }
    return {};
  } catch (error) {
    console.error("Error fetching PO details:", error);
    return {};
  }
}

// Helper function to fetch margins
async function fetchMargins(enquiryIds: string[]): Promise<Record<string, number>> {
  try {
    const response = await axios.post(
      `${API_CONFIG.baseUrl}/${API_CONFIG.endpoints.margins}`,
      {
        parameters: [
          {
            type: "category",
            target: ["variable", ["template-tag", "enquiry_ids"]],
            value: enquiryIds.join(', ')
          }
        ]
      },
      {
        headers: {
          'Authorization': `Bearer ${API_CONFIG.accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.data) {
      throw new Error('Failed to fetch margins');
    }

    const data = await response.data;
    return data.reduce((acc: Record<string, number>, item: { enquiry_id: string; average_margin: number }) => {
      acc[item.enquiry_id] = item.average_margin;
      return acc;
    }, {});
  } catch (error) {
    console.error('Error fetching margins:', error);
    return {};
  }
}