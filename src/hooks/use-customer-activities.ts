import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

const BATCH_SIZE = 1000;

// Helper function to format date
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).replace(/\//g, '/');
};

export type CustomerActivity = {
  id: string;
  type: string;
  customerId: string;
  customerName: string;
  description: string;
  activityDate: string;
  enquiryId: string;
  date: string;
  reviewRemarksCurrent: string;
  reviewRemarksPrevious: string;
  remarks_history: Array<{
    remarks: string;
    created_at: string;
  }>;
};

type Activity = {
  id: string;
  activity_type: string;
  description: string;
  created_at: string;
  enquiries: any;
  remarks_history: Array<{
    remarks: string;
    created_at: string;
  }>;
};

type CustomerWithActivities = {
  id: string;
  customer_full_name: string;
  activities: Activity[];
  remarks_history: Array<{
    remarks: string;
    created_at: string;
  }>;
};

export type CustomerWithoutActivity = {
  id: string;
  customer_full_name: string;
  account_owner: string;
  last_activity_date: string | null;
  reviewRemarksCurrent: string;
  reviewRemarksPrevious: string;
  remarks_history: Array<{
    remarks: string;
    created_at: string;
  }>;
};

const getDateRange = (period: string) => {
  const now = new Date();
  const startDate = new Date();
  const endDate = new Date();

  // Convert to UTC
  const utcNow = new Date(Date.UTC(
    now.getUTCFullYear(),
    now.getUTCMonth(),
    now.getUTCDate(),
    now.getUTCHours(),
    now.getUTCMinutes(),
    now.getUTCSeconds()
  ));

  switch (period) {
    case "last7days":
      startDate.setUTCDate(utcNow.getUTCDate() - 7);
      startDate.setUTCHours(0, 0, 0, 0);
      endDate.setUTCHours(23, 59, 59, 999);
      break;
    case "currentWeek":
      // Set to the beginning of current week (Sunday)
      startDate.setUTCDate(utcNow.getUTCDate() - utcNow.getUTCDay());
      startDate.setUTCHours(0, 0, 0, 0);
      endDate.setUTCHours(23, 59, 59, 999);
      break;
    case "lastWeek":
      // Set to the beginning of last week
      startDate.setUTCDate(utcNow.getUTCDate() - utcNow.getUTCDay() - 7);
      startDate.setUTCHours(0, 0, 0, 0);
      endDate.setUTCDate(utcNow.getUTCDate() - utcNow.getUTCDay() - 1);
      endDate.setUTCHours(23, 59, 59, 999);
      break;
    case "currentMonth":
      startDate.setUTCDate(1);
      startDate.setUTCHours(0, 0, 0, 0);
      endDate.setUTCHours(23, 59, 59, 999);
      break;
    case "lastMonth":
      startDate.setUTCMonth(utcNow.getUTCMonth() - 1);
      startDate.setUTCDate(1);
      startDate.setUTCHours(0, 0, 0, 0);
      endDate.setUTCMonth(utcNow.getUTCMonth());
      endDate.setUTCDate(0);
      endDate.setUTCHours(23, 59, 59, 999);
      break;
    default:
      // Default to last 7 days
      startDate.setUTCDate(utcNow.getUTCDate() - 7);
      startDate.setUTCHours(0, 0, 0, 0);
      endDate.setUTCHours(23, 59, 59, 999);
  }

  return {
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
  };
};

export function useCustomerActivities(
  selectedCustomer: string = 'all',
  selectedAccountOwner: string = 'all',
  timeFilter: string = 'last7days'
) {
  const [activities, setActivities] = useState<CustomerActivity[]>([]);
  const [customersWithoutActivity, setCustomersWithoutActivity] = useState<CustomerWithoutActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchActivities = async () => {
    try {
      setLoading(true);

      // Get current user from localStorage
      const user = {
        email: localStorage.getItem('userEmail'),
        role: localStorage.getItem('userRole'),
        category: localStorage.getItem('userCategory')
      };

      // Calculate date range based on timeFilter
      const { startDate, endDate } = getDateRange(timeFilter);

      // First get total count
      const { count } = await supabase
        .from("customer")
        .select("*", { count: 'exact', head: true });

      if (!count) {
        setActivities([]);
        setLoading(false);
        return;
      }

      // Calculate number of batches needed
      const numBatches = Math.ceil(count / BATCH_SIZE);
      let allCustomersData: any[] = [];

      // Fetch each batch
      for (let i = 0; i < numBatches; i++) {
        const start = i * BATCH_SIZE;
        const end = start + BATCH_SIZE - 1;

        // Build the query
        let query = supabase
          .from('customer')
          .select(`
            id,
            customer_full_name,
            activities:customer_activity!inner(
              id,
              activity_type,
              description,
              created_at,
              enquiries:enquiries(*)
            ),
            remarks_history:customer_activity_history(
              remarks,
              created_at
            )
          `)
          .gte('activities.created_at', startDate)
          .lte('activities.created_at', endDate)
          .range(start, end);

        // Apply filters based on user role
        if (user?.role === 'sales') {
          query = query.eq('account_owner', user.email);
        } else if (user?.role === 'bu_head') {
          // First get all users of the category from user_roles
          const { data: categoryUsers, error: usersError } = await supabase
            .from('user_list')
            .select('email, user_roles!inner(*)')
            .eq('user_roles.category', user?.category);

          if (usersError) throw usersError;

          // Get the list of emails from the users
          const accountOwners = categoryUsers?.map(user => user.email) || [];

          // Filter customers by these account owners
          query = query.in('account_owner', accountOwners);
        }

        // Add customer filter if specified
        if (selectedCustomer !== 'All') {
          query = query.eq('id', selectedCustomer);
        }

        // Add account owner filter if specified
        if (selectedAccountOwner !== 'All') {
          query = query.eq('account_owner', selectedAccountOwner);
        }

        const { data: batchCustomers, error: batchError } = await query;

        if (batchError) throw batchError;
        if (batchCustomers) {
          allCustomersData = [...allCustomersData, ...batchCustomers];
        }
      }

      // Transform the data to match our component's expected format
      const transformedActivities: CustomerActivity[] = [];
      
      // First sort activities within each customer, filter out customers with no activities, and add latest activity date
      const customersWithSortedActivities = (allCustomersData as CustomerWithActivities[])
        .map(customer => {
          const sortedActivities = customer.activities ? 
            [...customer.activities].sort((a, b) => 
              new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
            ) : [];
          return {
            ...customer,
            activities: sortedActivities,
            latestActivityDate: sortedActivities.length > 0 ? new Date(sortedActivities[0].created_at).getTime() : 0
          };
        })
        .filter(customer => customer.activities && customer.activities.length > 0);

      // Then sort customers by their latest activity date
      const sortedCustomers = customersWithSortedActivities.sort((a, b) => b.latestActivityDate - a.latestActivityDate);
      
      sortedCustomers.forEach(customer => {
        // Map each activity for this customer
        customer.activities.forEach(activity => {
          transformedActivities.push({
            id: activity.id,
            type: activity.activity_type || '',
            customerId: customer.id,
            customerName: customer.customer_full_name || '',
            description: activity.description || '',
            activityDate: formatDate(activity.created_at) || '',
            enquiryId: activity.enquiries?.id || '',
            date: formatDate(activity.created_at) || '',
            reviewRemarksCurrent: customer.remarks_history[customer.remarks_history.length - 1]?.remarks || '',
            reviewRemarksPrevious: customer.remarks_history[customer.remarks_history.length - 2]?.remarks || '',
            remarks_history: customer.remarks_history || []
          });
        });
      });

      setActivities(transformedActivities);
    } catch (err: any) {
      setError(err);
      toast({
        title: "Error fetching activities",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // New function to fetch customers without recent activity
  const fetchCustomersWithoutActivity = async () => {
    try {
      // Get current user from localStorage
      const user = {
        email: localStorage.getItem('userEmail'),
        role: localStorage.getItem('userRole'),
        category: localStorage.getItem('userCategory')
      };

      // Calculate date range based on timeFilter
      const { startDate, endDate } = getDateRange(timeFilter);

      // First get total count
      const { count } = await supabase
        .from("customer")
        .select("*", { count: 'exact', head: true });

      if (!count) {
        setCustomersWithoutActivity([]);
        return;
      }

      // Calculate number of batches needed
      const numBatches = Math.ceil(count / BATCH_SIZE);
      let allCustomersData: any[] = [];

      // Fetch each batch
      for (let i = 0; i < numBatches; i++) {
        const start = i * BATCH_SIZE;
        const end = start + BATCH_SIZE - 1;

        // Build the query to get all customers with their activities
        let query = supabase
          .from('customer')
          .select(`
            id,
            customer_full_name,
            account_owner,
            activities:customer_activity(
              id,
              created_at
            ),
            remarks_history:customer_activity_history(
              remarks,
              created_at
            )
          `)
          .range(start, end);

        // Apply filters based on user role
        if (user?.role === 'sales') {
          query = query.eq('account_owner', user.email);
        } else if (user?.role === 'bu_head') {
          const { data: categoryUsers, error: usersError } = await supabase
            .from('user_list')
            .select('email, user_roles!inner(*)')
            .eq('user_roles.category', user?.category);

          if (usersError) throw usersError;

          const accountOwners = categoryUsers?.map(user => user.email) || [];
          query = query.in('account_owner', accountOwners);
        }

        // Add customer filter if specified
        if (selectedCustomer !== 'All') {
          query = query.eq('id', selectedCustomer);
        }

        // Add account owner filter if specified
        if (selectedAccountOwner !== 'All') {
          query = query.eq('account_owner', selectedAccountOwner);
        }

        const { data: batchCustomers, error: batchError } = await query;

        if (batchError) throw batchError;
        if (batchCustomers) {
          allCustomersData = [...allCustomersData, ...batchCustomers];
        }
      }

      // Filter customers without recent activity
      const customersWithoutRecentActivity = allCustomersData
        .filter(customer => {
          // Check if customer has any activities within the time range
          const hasRecentActivity = customer.activities?.some(activity => 
            new Date(activity.created_at) >= new Date(startDate) && 
            new Date(activity.created_at) <= new Date(endDate)
          );
          return !hasRecentActivity;
        })
        .map(customer => ({
          id: customer.id,
          customer_full_name: customer.customer_full_name || '',
          account_owner: customer.account_owner || '',
          last_activity_date: customer.activities?.[0]?.created_at || null,
          reviewRemarksCurrent: customer.remarks_history[customer.remarks_history.length - 1]?.remarks || '',
          reviewRemarksPrevious: customer.remarks_history[customer.remarks_history.length - 2]?.remarks || '',
          remarks_history: customer.remarks_history || []
        }))
        .sort((a, b) => {
          if (!a.last_activity_date) return 1;
          if (!b.last_activity_date) return -1;
          return new Date(b.last_activity_date).getTime() - new Date(a.last_activity_date).getTime();
        });

      setCustomersWithoutActivity(customersWithoutRecentActivity);
    } catch (err: any) {
      toast({
        title: "Error fetching customers without activity",
        description: err.message,
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if(selectedAccountOwner && selectedAccountOwner && timeFilter) {
      fetchActivities();
      fetchCustomersWithoutActivity();
    }
  }, [selectedCustomer, selectedAccountOwner, timeFilter]);

  // Function to update activity remarks
  const updateActivityRemarks = async (
    customerId: string, 
    currentRemarks: string, 
    previousRemarks: string
  ) => {
    try {
      // Get the current user ID
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id || '';

      if (previousRemarks) {
        // Update existing remarks
        const { error: updateError } = await supabase
          .from('customer_activity_history')
          .update({ remarks: currentRemarks })
          .eq('customer_id', customerId)
          .order('created_at', { ascending: false })
          .limit(1);

        if (updateError) throw updateError;
      } else {
        // Add new remarks
        const { error: insertError } = await supabase
          .from('customer_activity_history')
          .insert({
            customer_id: customerId,
            remarks: currentRemarks,
            created_at: new Date().toISOString()
          });

        if (insertError) throw insertError;
      }

      // Refresh activities data
      await fetchActivities();

      return true;
    } catch (err: any) {
      toast({
        title: "Error updating remarks",
        description: err.message,
        variant: "destructive",
      });
      return false;
    }
  };

  return {
    activities,
    customersWithoutActivity,
    loading,
    error,
    updateActivityRemarks,
    refreshActivities: () => {
      fetchActivities();
      fetchCustomersWithoutActivity();
    }
  };
}
