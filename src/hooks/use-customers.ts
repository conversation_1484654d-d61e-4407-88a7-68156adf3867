import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

const BATCH_SIZE = 1000;

export const useCustomers = () => {
  const {
    data,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['customers'],
    queryFn: async () => {
      const user = {
        email: localStorage.getItem('userEmail'),
        role: localStorage.getItem('userRole'),
        category: localStorage.getItem('userCategory')
      };

      // First get total count
      const { count } = await supabase
        .from("customer")
        .select("*", { count: 'exact', head: true });

      if (!count) return { customers: [] };

      // Calculate number of batches needed
      const numBatches = Math.ceil(count / BATCH_SIZE);
      console.log("here",numBatches)
      let allCustomers: any[] = [];

      // Fetch each batch
      for (let i = 0; i < numBatches; i++) {
        const start = i * BATCH_SIZE;
        const end = start + BATCH_SIZE - 1;
        
        let query = supabase
          .from('customer')
          .select(`
            *,
            planning:account_planning(*),
            customer_enquiries:enquiries(*,
              sample_requests(*,
                sample_feedback(*)
              ),
              purchase_orders(*)
            )
          `)
          .order("customer_full_name")
          .range(start, end);

        // If sales, only fetch customers for the current user
        if (user?.role === 'sales' && user?.email) {
          query = query.eq('account_owner', user?.email);
        } else if (user?.role === 'bu_head') {
          // First get all users of the category from user_roles
          const { data: categoryUsers, error: usersError } = await supabase
            .from('user_list')
            .select('email, user_roles!inner(*)')
            .eq('user_roles.category', user?.category);

          if (usersError) throw usersError;

          // Get the list of emails from the users
          const accountOwners = categoryUsers?.map(user => user.email) || [];

          // Filter customers by these account owners
          query = query.in('account_owner', accountOwners);
        }

        const { data: batchCustomers, error: batchError } = await query;

        if (batchError) throw batchError;
        if (batchCustomers) {
          allCustomers = [...allCustomers, ...batchCustomers];
        }
      }

      return {
        customers: allCustomers,
        totalCount: count
      };
    }
  });

  return {
    customers: data?.customers || [],
    totalCount: data?.totalCount || 0,
    loading: isLoading,
    error,
    refetch
  };
};
