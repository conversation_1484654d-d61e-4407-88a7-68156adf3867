import { useState, useEffect } from "react";
import axios from "axios";
import { API_CONFIG } from "@/config/api";

export type PurchaseOrderDetails = {
  purchase_order_number: string;
  po_value: number | null;
  customer_id: string;
  chemical_name: string;
  delivery_date: string | null;
  payment_terms: string | null;
  total_sales_order_value: number;
  margin_percentage: number;
};

export function usePurchaseOrderDetails(poNumbers: string[]) {
  const [details, setDetails] = useState<Record<string, PurchaseOrderDetails>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function fetchDetails() {
      if (!poNumbers.length) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const poNumbersCSV = poNumbers.join(",");

        const response = await axios.post(
          `${API_CONFIG.baseUrl}/${API_CONFIG.endpoints.purchaseOrderDetails}`,
          {
            parameters: [
              {
                type: "category",
                target: ["variable", ["template-tag", "po_numbers_csv"]],
                value: poNumbersCSV
              }
            ]
          },
          {
            headers: {
              'Authorization': `Bearer ${API_CONFIG.accessToken}`,
              'Content-Type': 'application/json'
            }
          }

        );

        if (response.data && Array.isArray(response.data)) {
          // Convert array to object with po_number as key
          const detailsMap = response.data.reduce((acc, item) => {
            acc[item.purchase_order_number] = item;
            return acc;
          }, {});

          setDetails(detailsMap);
        }
      } catch (err) {
        setError(err instanceof Error ? err : new Error("An error occurred"));
        console.error("Error fetching purchase order details:", err);
      } finally {
        setLoading(false);
      }
    }

    fetchDetails();
  }, [poNumbers]);

  return { details, loading, error };
}
