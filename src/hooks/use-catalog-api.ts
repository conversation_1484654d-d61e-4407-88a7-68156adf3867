import { API_CONFIG } from '@/config/api';
import axios from 'axios';
import { useState, useCallback } from 'react';
import { toast } from 'sonner';

interface Compound {
  id: string;
  chemical_name: string;
  // Add other fields as needed
}

interface CatalogResponse {
  data: Compound[];
  total: number;
  page: number;
  page_size: number;
}

export const useCatalogApi = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [compounds, setCompounds] = useState<Compound[]>([]);

  const fetchCompounds = useCallback(async (page: number = 1, pageSize: number = 20) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await axios.get(
        `${API_CONFIG.catalogBaseUrl}catalog`,
        {
          headers: {
            'accept': 'application/json',
            'Authorization': `Bearer ${API_CONFIG.accessToken}`
          }
        }
      );

      if (!response.data) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: CatalogResponse = await response.data;
      setCompounds(data.data);
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch compounds';
      setError(errorMessage);
      toast.error('Failed to load chemical suggestions');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    compounds,
    isLoading,
    error,
    fetchCompounds
  };
}; 