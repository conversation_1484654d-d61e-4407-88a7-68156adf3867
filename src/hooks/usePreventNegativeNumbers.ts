import { useEffect, useRef } from 'react';

/**
 * A custom hook that prevents negative numbers in input fields
 * 
 * @param inputRef - Reference to the input element
 * 
 * Usage:
 * ```
 * const inputRef = useRef<HTMLInputElement>(null);
 * usePreventNegativeNumbers(inputRef);
 * 
 * return <input ref={inputRef} type="number"
min="0" />;
 * ```
 */
export const usePreventNegativeNumbers = (
  inputRef: React.RefObject<HTMLInputElement>
) => {
  // Keep track of whether we've set up the input
  const isSetupRef = useRef(false);
  
  useEffect(() => {
    const input = inputRef.current;
    if (!input || isSetupRef.current) return;
    
    // Set min attribute if not already set
    if (!input.hasAttribute('min')) {
      input.setAttribute('min', '0');
    }
    
    // Handle keydown to prevent typing negative sign
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === '-' || e.keyCode === 189) {
        e.preventDefault();
      }
    };
    
    // Handle input to prevent negative values
    const handleInput = () => {
      const value = input.value;
      const numValue = parseFloat(value);
      
      if (value.startsWith('-') || (!isNaN(numValue) && numValue < 0)) {
        input.value = '0';
      }
    };
    
    // Add event listeners
    input.addEventListener('keydown', handleKeyDown);
    input.addEventListener('input', handleInput);
    
    // Mark as set up
    isSetupRef.current = true;
    
    // Clean up event listeners on unmount
    return () => {
      input.removeEventListener('keydown', handleKeyDown);
      input.removeEventListener('input', handleInput);
    };
  }, [inputRef]);
};

export default usePreventNegativeNumbers;
