import { useState, useEffect } from 'react';
import axios from 'axios';
import { API_CONFIG } from '@/config/api';

export type CustomerMetrics = {
  repeated_rate: number;
  margin_percentage: number;
  order_count: number | null;
  sales_stack_customer_id: string;
  end_date: string;
  share_wallet: number | null;
  total_duty_amount_value: number;
  procurement_percentage: number | null;
  total_revenue: number | null;
  total_revenue_booked: number | null;
  sum_target: number | null;
  avg_annual_turnover: number | null;
  to_be_invoiced_revenue: number | null;
  customer_id: string;
  invoiced_revenue: number | null;
  total_sales_order_value: number;
  start_date: string;
  total_purchase_order_value: number;
  total_logistics_cost_value: number;
  customer_full_name: string;
};

export type MetricsPeriod = 'current_quarter' | 'previous_quarter' | 'ytd';

export function useCustomerMetrics(commonCustomerId: string, period: MetricsPeriod = 'current_quarter',customerId?: string) {
  const [metrics, setMetrics] = useState<CustomerMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function fetchMetrics() {
      if (!commonCustomerId) {
        setLoading(false);
        return;
      }
      
      try {
        setLoading(true);
        const response = await axios.post(
          `${API_CONFIG.baseUrl}/${API_CONFIG.endpoints.customerMetrics}`,
          {
            parameters: [
              {
                type: "category",
                target: ["variable", ["template-tag", "period"]],
                value: period
              },
              {
                type: "category",
                target: ["variable", ["template-tag", "customer_id"]],
                value: commonCustomerId
              }
            ]
          },
          {
            headers: {
              'Authorization': `Bearer ${API_CONFIG.accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
          const filteredData = customerId 
          ? response.data.filter((item: any) => item.sales_stack_customer_id === customerId)
          : response.data;
          setMetrics(filteredData[0]);
        } else {
          setMetrics(null);
        }
      } catch (err) {
        setError(err instanceof Error ? err : new Error('An error occurred'));
        console.error('Error fetching customer metrics:', err);
      } finally {
        setLoading(false);
      }
    }

    fetchMetrics();
  }, [commonCustomerId, period]);

  return { metrics, loading, error };
}