
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }

  html {
    @apply scroll-smooth;
  }
}

@layer utilities {
  .glass-morphism {
    @apply backdrop-blur-xl bg-white/70 border border-white/20 shadow-[0_4px_12px_-2px_rgba(0,0,0,0.1)];
  }
  
  .text-gradient {
    @apply bg-gradient-to-br from-[#294d48] via-[#294d48] to-[#294d48]/80 bg-clip-text text-transparent;
  }
  
  .bg-gradient-subtle {
    @apply bg-gradient-to-br from-[#E6EAE9] via-white to-[#E6EAE9];
  }

  .text-sm {
    font-size: 16px;
  } 

  .text-xs {
    font-size: 13px;
  }

  .text-12x {
    font-size: 12px;
  }

  .text-14x {
    font-size: 14px;
  }

  .text-18x {
    font-size: 18px;
  }

  .font-semibold {
    font-weight: 550;
  }
}

@layer components {
  .data-row {
    @apply flex items-center px-2 py-1 border-b last:border-b-0 transition-colors hover:bg-gray-50; /* Reduced padding */
  }
  
  .data-cell {
    @apply px-1; /* Tighter cell padding */
  }

  .badge {
    @apply text-xs inline-flex items-center rounded-full px-1.5 py-0.5 font-medium; /* Compact badge */
  }

  .badge-primary {
    @apply bg-primary/20 text-primary-foreground;
  }
  
  .badge-secondary {
    @apply bg-secondary/20 text-secondary-foreground;
  }
  
  .badge-accent {
    @apply bg-accent/20 text-accent-foreground;
  }

  .mini-list {
    @apply flex gap-1 flex-wrap text-xs; /* Tighter list */
  }

  .mini-list-item {
    @apply bg-gray-100 px-1.5 py-0.5 rounded-md hover:bg-primary/10 transition-colors cursor-pointer; /* Compact list item */
  }
}
