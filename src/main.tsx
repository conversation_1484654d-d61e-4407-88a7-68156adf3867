import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import 'antd/dist/reset.css';
import ReactGA from 'react-ga4'
import { PostHogProvider } from 'posthog-js/react'
import { EnvKeys } from './config/constants.ts'

if (EnvKeys.googleAnalyticsMeasurementId) {
  ReactGA.initialize(EnvKeys.googleAnalyticsMeasurementId)
} else {
  console.warn('Google Analytics Measurement ID is not defined. GA will not be initialized.')
}

const root = createRoot(document.getElementById('root')!)
root.render(
    <PostHogProvider
      apiKey={EnvKeys.postHogKey}
      options={{
        api_host: EnvKeys.postHogHost,
        defaults: '2025-05-24',
        capture_exceptions: true,
        autocapture:false,
      }}
    >
      <App />
    </PostHogProvider>
)
