import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format a date in human-readable format
 * @param date - Date string or Date object
 * @param format - Format type: 'short', 'long', or 'numeric'
 * @returns Formatted date string
 */
export function formatDate(date: string | Date | null | undefined, format: 'short' | 'long' | 'numeric' | 'pending' = 'numeric'): string {
  if (!date) return 'N/A';

  let dateObj: Date;
  
  try {
    // If it's already a Date object
    if (date instanceof Date) {
      dateObj = date;
    } else {
      // Handle ISO string with timezone
      dateObj = new Date(date);
    }

    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return 'Invalid Date';
    }

    switch (format) {
      case 'short':
        return dateObj.toLocaleDateString("en-US", {
          weekday: "short",
          month: "short",
          day: "numeric",
          year: "numeric",
        });
      case 'long':
        return dateObj.toLocaleDateString("en-US", {
          weekday: "long",
          month: "long",
          day: "numeric",
          year: "numeric",
        });
      case 'numeric':
        return dateObj.toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
        });
      case "pending":
        return dateObj.toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "short",
          year: "numeric",
        });
      default:
        return dateObj.toLocaleDateString("en-US", {
          weekday: "short",
          month: "short",
          day: "numeric",
          year: "numeric",
        });
    }
  } catch (error) {
    console.error('Date parsing error:', error);
    return 'Invalid Date';
  }
}
export function setCrmEnabledEverywhere(value: string) {
  localStorage.setItem("crmEnabled", value);
  // Optionally, you can dispatch a custom event if you want to notify other components
  window.dispatchEvent(new Event("crmEnabledChanged"));
}

export function getDaysDifference(date1: string | Date, date2: string | Date): number {
  const d1 = typeof date1 === "string" ? new Date(date1) : date1;
  const d2 = typeof date2 === "string" ? new Date(date2) : date2;
  // Get the difference in milliseconds
  const diffMs = d2.getTime() - d1.getTime();
  // Convert ms to days
  return Math.round(diffMs / (1000 * 60 * 60 * 24));
}

