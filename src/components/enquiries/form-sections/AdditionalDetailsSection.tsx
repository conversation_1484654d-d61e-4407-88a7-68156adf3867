
import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { ChemicalData } from "../types/formTypes";
import ValidationMessage from "../components/ValidationMessage";
import { categoryOptions } from "@/config/constants";

interface AdditionalDetailsSectionProps {
  chemical: ChemicalData;
  updateChemicalData: (data: Partial<ChemicalData>) => void;
  showValidationErrors?: boolean;
}


const incotermsOptions = [
  "EXW (Ex Works)",
  "FOB (Free on board)",
  "DDP (Delivery Duty Paid)",
  "CIF (Cost, Insurance, & Freight)",
  "CFR (Cost & Freight)",
  "FOR (Free on road)"
];

const AdditionalDetailsSection: React.FC<AdditionalDetailsSectionProps> = ({
  chemical,
  updateChemicalData,
  showValidationErrors = false
}) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="category" className="text-[#294d48]">Category/BU <span className="text-red-500">*</span></Label>
          <Select
            value={chemical.category || ""}
            onValueChange={(value) => updateChemicalData({ category: value })}
          >
            <SelectTrigger
              id="category"
              className={`bg-white ${!showValidationErrors || chemical.category ? 'border-gray-200' : 'border-red-300'}`}
            >
              <SelectValue placeholder="Select a category" />
            </SelectTrigger>
            <ValidationMessage show={showValidationErrors && !chemical.category} />
            <SelectContent className="bg-white">
              {categoryOptions.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="incoterms" className="text-[#294d48]">Incoterms <span className="text-red-500">*</span></Label>
          <Select
            value={chemical.incoterms || ""}
            onValueChange={(value) => updateChemicalData({ incoterms: value })}
          >
            <SelectTrigger
              id="incoterms"
              className={`bg-white ${!showValidationErrors || chemical.incoterms ? 'border-gray-200' : 'border-red-300'}`}
            >
              <SelectValue placeholder="Select incoterms" />
            </SelectTrigger>
            <ValidationMessage show={showValidationErrors && !chemical.incoterms} />
            <SelectContent className="bg-white">
              {incotermsOptions.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="remarks" className="text-[#294d48]">Remarks</Label>
        <Textarea
          id="remarks"
          value={chemical.remarks || ""}
          onChange={(e) => updateChemicalData({ remarks: e.target.value })}
          placeholder="Enter specific details like Grade etc."
          className="bg-white border-gray-200 focus:border-[#294d48]"
          rows={3}
        />
      </div>
    </div>
  );
};

export default AdditionalDetailsSection;
