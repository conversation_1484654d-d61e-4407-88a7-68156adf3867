import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select as MultipleSelect } from "antd";
import { COUNTRY_LIST } from "../../../config/constants";
import { useState, useEffect, useRef, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { CustomerData } from "../types/formTypes";
import ValidationMessage from "../components/ValidationMessage";

interface LocationInfoSectionProps {
  customerData: CustomerData;
  updateCustomerData: (data: Partial<CustomerData>) => void;
  selectedCustomerId: string | null;
  setSelectedCustomerId: (id: string | null) => void;
  showValidationErrors?: boolean;
}

const industriesOptions = [
  { label: "Industrial / OEM coatings", value: "industrialOEMCoatings" },
  { label: "Architechtural", value: "architechtural" },
  { label: "Wood", value: "wood" },
  { label: "Floor", value: "floor" },
  { label: "Powder Coating", value: "powderCoating" },
  { label: "Marine", value: "marine" },
  { label: "Traffic", value: "traffic" },
];

const LocationInfoSection = ({
  customerData,
  updateCustomerData,
  selectedCustomerId,
  setSelectedCustomerId,
  showValidationErrors = false,
}: LocationInfoSectionProps) => {
  const [customerSuggestions, setCustomerSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const dropdownRef = useRef<HTMLUListElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Debounce timer for customer search
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Create memoized handlers for all input fields to prevent unnecessary re-renders

  // Handler for customer name input with debounced search
  const handleCustomerInputChange = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSelectedCustomerId(null); // Reset selected customer ID when typing
    updateCustomerData({ customerFullName: value });

    // Clear previous debounce timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Only search if value is long enough
    if (value.length > 2) {
      // Debounce the search to prevent too many requests
      debounceTimerRef.current = setTimeout(async () => {
        try {
          // First get matching customers
          const { data: customers, error } = await supabase
            .from('customer')
            .select(`
              customer_full_name,
              city,
              country
            `)
            .ilike('customer_full_name', `%${value}%`)
            .order('customer_full_name')
            .limit(10);

          if (error) throw error;

          // Just show names in suggestions
          const suggestions = customers.map(customer => customer.customer_full_name);
          setCustomerSuggestions(suggestions);
          setShowSuggestions(true);
        } catch (error) {
          console.error('Error fetching customer suggestions:', error);
          setCustomerSuggestions([]);
        }
      }, 300); // 300ms debounce
    } else {
      setCustomerSuggestions([]);
      setShowSuggestions(false);
    }
  }, [updateCustomerData, setSelectedCustomerId]);

  // Handler for customer POC input
  const handleCustomerPocChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    updateCustomerData({ customerPoc: e.target.value });
  }, [updateCustomerData]);

  // Handler for customer phone input
  const handleCustomerPhoneChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow numbers, +, -, and spaces in phone numbers
    const value = e.target.value.replace(/[^0-9+\-\s]/g, '');
    updateCustomerData({ customerPhone: value });
  }, [updateCustomerData]);

  // Handler for customer email input
  const handleCustomerEmailChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    updateCustomerData({ customerEmail: e.target.value });
  }, [updateCustomerData]);

  // Handler for city input
  const handleCityChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    updateCustomerData({ city: e.target.value });
  }, [updateCustomerData]);

  // Handler for country dropdown
  const handleCountryChange = useCallback((value: string) => {
    updateCustomerData({ country: value });
    // Force save to localStorage
    setTimeout(() => {
      const customerDataFromStorage = localStorage.getItem('enquiry-customer');
      if (customerDataFromStorage) {
        try {
          const parsedData = JSON.parse(customerDataFromStorage);
          parsedData.country = value;
          localStorage.setItem('enquiry-customer', JSON.stringify(parsedData));
        } catch (error) {
          console.error('Error manually saving country to localStorage:', error);
        }
      }
    }, 100);
  }, [updateCustomerData]);

  // Handler for industries dropdown
  const handleIndustriesChange = useCallback((value: string[]) => {
    updateCustomerData({ industries: value });
    // Force save to localStorage
    setTimeout(() => {
      const customerDataFromStorage = localStorage.getItem('enquiry-customer');
      if (customerDataFromStorage) {
        try {
          const parsedData = JSON.parse(customerDataFromStorage);
          parsedData.industries = value;
          localStorage.setItem('enquiry-customer', JSON.stringify(parsedData));
        } catch (error) {
          console.error('Error manually saving industries to localStorage:', error);
        }
      }
    }, 100);
  }, [updateCustomerData]);

  const handleSuggestionClick = async (suggestion: string) => {
    try {
      const { data: customer, error } = await supabase
        .from('customer')
        .select(`
          id,
          customer_full_name,
          customer_poc,
          customer_phone,
          customer_email,
          city,
          country,
          industries
        `)
        .eq('customer_full_name', suggestion)
        .single();

      if (error) throw error;

      if (customer) {
        setSelectedCustomerId(customer.id);
        updateCustomerData({
          customerFullName: customer.customer_full_name,
          customerPoc: customer.customer_poc || '',
          customerPhone: customer.customer_phone || '',
          customerEmail: customer.customer_email || '',
          city: customer.city || '',
          country: customer.country || '',
          industries: customer.industries,
        });
      }

      setShowSuggestions(false);
    } catch (error) {
      console.error('Error fetching customer details:', error);
      setSelectedCustomerId(null);
      updateCustomerData({ customerFullName: suggestion });
      setShowSuggestions(false);
    }
  };

  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200 space-y-4">
      <div className="relative">
        <Label htmlFor="customerFullName" className="text-[#294d48]">
          Customer Name (Optional)
        </Label>
        <Input
          id="customerFullName"
          value={customerData.customerFullName}
          onChange={handleCustomerInputChange}
          placeholder="Enter customer's full name"
          className="mt-1 focus:border-[#294d48] border-gray-200"
        />
        {showSuggestions && customerSuggestions.length > 0 && (
          <ul
            ref={dropdownRef}
            className="absolute bg-[#f8f9fa] border border-gray-200 w-full mt-1 max-h-40 overflow-y-auto z-10 rounded-md shadow-lg"
          >
            {customerSuggestions.map((suggestion, index) => (
              <li
                key={index}
                className={`p-2 cursor-pointer ${
                  index % 2 === 0
                    ? 'bg-[#f7faff]' // Very light blue
                    : 'bg-[#f8f9fa]' // Light gray
                } hover:bg-[#f0f7ff] text-gray-700 border-b border-gray-100 last:border-b-0`}
                onClick={() => handleSuggestionClick(suggestion)}
              >
                {suggestion}
              </li>
            ))}
          </ul>
        )}
      </div>
      <div>
        <Label htmlFor="customerPoc" className="text-[#294d48]">
          Customer POC (Optional)
        </Label>
        <Input
          id="customerPoc"
          value={customerData.customerPoc}
          onChange={handleCustomerPocChange}
          placeholder="Enter customer poc name"
          className="mt-1 border-gray-200 focus:border-[#294d48]"
        />
        {/* No validation message for optional field */}
      </div>
      <div>
        <Label htmlFor="customerPhone" className="text-[#294d48]">
          Phone Number <span className="text-red-500">*</span>
        </Label>
        <Input
          id="customerPhone"
          value={customerData.customerPhone}
          onChange={handleCustomerPhoneChange}
          placeholder="****** 567 8900"
          required
          className={`mt-1 focus:border-[#294d48] ${!showValidationErrors || customerData.customerPhone ? 'border-gray-200' : 'border-red-300'}`}
        />
        <ValidationMessage show={showValidationErrors && !customerData.customerPhone} />
      </div>
      <div>
        <Label htmlFor="customerEmail" className="text-[#294d48]">
          Email Address <span className="text-red-500">*</span>
        </Label>
        <Input
          id="customerEmail"
          type="email"
          value={customerData.customerEmail}
          onChange={handleCustomerEmailChange}
          placeholder="<EMAIL>"
          required
          className={`mt-1 focus:border-[#294d48] ${!showValidationErrors || customerData.customerEmail ? 'border-gray-200' : 'border-red-300'}`}
        />
        <ValidationMessage show={showValidationErrors && !customerData.customerEmail} />
      </div>
      <div>
        <Label htmlFor="city" className="text-[#294d48]">
          City <span className="text-red-500">*</span>
        </Label>
        <Input
          id="city"
          value={customerData.city}
          onChange={handleCityChange}
          required
          className={`mt-1 focus:border-[#294d48] ${!showValidationErrors || customerData.city ? 'border-gray-200' : 'border-red-300'}`}
        />
        <ValidationMessage show={showValidationErrors && !customerData.city} />
      </div>
      <div>
        <Label htmlFor="country" className="text-[#294d48]">
          Country <span className="text-red-500">*</span>
        </Label>
        <MultipleSelect
          style={{
            width: "100%",
            border: !showValidationErrors || customerData.country ? '1px solid #e5e7eb' : '1px solid #fca5a5',
          }}
          allowClear
          showSearch
          optionLabelProp="value"
          placeholder="Please select"
          value={customerData.country}
          onChange={handleCountryChange}
        >
          {COUNTRY_LIST.map((country, index) => (
            <MultipleSelect.Option
              key={index}
              value={country.name}
              label={country.name}
            >
              {`${country.flag} ${country.name}`}
            </MultipleSelect.Option>
          ))}
        </MultipleSelect>
        <ValidationMessage show={showValidationErrors && !customerData.country} />
      </div>

      <div>
        <Label htmlFor="industries" className="text-[#294d48]">
          Industries <span className="text-red-500">*</span>
        </Label>
        <MultipleSelect
          mode="multiple"
          allowClear
          style={{
            width: "100%",
            border: !showValidationErrors || (customerData.industries && customerData.industries.length > 0) ? '1px solid #e5e7eb' : '1px solid #fca5a5',
          }}
          placeholder="Please select"
          value={customerData.industries}
          onChange={handleIndustriesChange}
        >
          {industriesOptions.map((option) => (
            <MultipleSelect.Option key={option.value} value={option.value}>
              {option.label}
            </MultipleSelect.Option>
          ))}
        </MultipleSelect>
        <ValidationMessage show={showValidationErrors && (!customerData.industries || customerData.industries.length === 0)} />
      </div>

      {/* <div>
        <Label htmlFor="quotationLocation" className="text-[#294d48]">
          Quotation Location
        </Label>
        <Input
          id="quotationLocation"
          value={formData.quotationLocation}
          onChange={(e) =>
            updateFormData({ quotationLocation: e.target.value })
          }
          required
          className="mt-1 border-gray-200 focus:border-[#294d48]"
        />
        <ValidationMessage show={!customerData.industries || customerData.industries.length === 0} />
      </div> */}
    </div>
  );
};

export default LocationInfoSection;
