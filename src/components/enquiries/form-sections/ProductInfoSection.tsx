import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { ChemicalData } from "../types/formTypes";
import ValidationMessage from "../components/ValidationMessage";

interface ProductInfoSectionProps {
  useChemicalName: boolean;
  setUseChemicalName: (value: boolean) => void;
  chemical: ChemicalData;
  updateChemicalData: (data: Partial<ChemicalData>) => void;
  showValidationErrors?: boolean;
}

const ProductInfoSection = ({
  useChemicalName,
  setUseChemicalName,
  chemical,
  updateChemicalData,
  showValidationErrors = false,
}: ProductInfoSectionProps) => {
  return (
    <div className="space-y-6">
      <div className="flex gap-4 items-center mb-6">
        <Button
          type="button"
          variant={useChemicalName ? "default" : "outline"}
          onClick={() => setUseChemicalName(true)}
          className={`flex-1 py-6 ${
            useChemicalName
              ? "bg-[#294d48] hover:bg-[#294d48]/90 text-white"
              : "border-[#294d48]/20 text-[#294d48] hover:bg-[#294d48]/10"
          }`}
        >
          Chemical Name
        </Button>
        <Button
          type="button"
          variant={!useChemicalName ? "default" : "outline"}
          onClick={() => setUseChemicalName(false)}
          className={`flex-1 py-6 ${
            !useChemicalName
              ? "bg-[#294d48] hover:bg-[#294d48]/90 text-white"
              : "border-[#294d48]/20 text-[#294d48] hover:bg-[#294d48]/10"
          }`}
        >
          Brand & Product
        </Button>
      </div>

      <div className="bg-white p-4 rounded-lg border border-[#294d48]/20 space-y-4">
        {useChemicalName ? (
          <div>
            <Label htmlFor="chemicalName" className="text-[#294d48]">
              Chemical Name (or Offset) <span className="text-red-500">*</span>
            </Label>
            <Input
              id="chemicalName"
              value={chemical.chemicalName}
              onChange={(e) => updateChemicalData({ chemicalName: e.target.value })}
              // required - using custom validation instead
              className={`mt-1 focus:border-[#294d48]/50 ${!showValidationErrors || chemical.chemicalName ? 'border-[#294d48]/20' : 'border-red-300'}`}
            />
            <ValidationMessage show={showValidationErrors && !chemical.chemicalName} />
          </div>
        ) : (
          <>
            <div>
              <Label htmlFor="brand" className="text-[#294d48]">
                Brand <span className="text-red-500">*</span>
              </Label>
              <Input
                id="brand"
                value={chemical.brand}
                onChange={(e) => updateChemicalData({ brand: e.target.value })}
                // required - using custom validation instead
                className={`mt-1 focus:border-[#294d48]/50 ${!showValidationErrors || chemical.brand ? 'border-[#294d48]/20' : 'border-red-300'}`}
              />
              <ValidationMessage show={showValidationErrors && !chemical.brand} />
            </div>
            <div>
              <Label htmlFor="product" className="text-[#294d48]">
                Product <span className="text-red-500">*</span>
              </Label>
              <Input
                id="product"
                value={chemical.product}
                onChange={(e) => updateChemicalData({ product: e.target.value })}
                // required - using custom validation instead
                className={`mt-1 focus:border-[#294d48]/50 ${!showValidationErrors || chemical.product ? 'border-[#294d48]/20' : 'border-red-300'}`}
              />
              <ValidationMessage show={showValidationErrors && !chemical.product} />
            </div>
          </>
        )}

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="destination" className="text-[#294d48]">
              Destination <span className="text-red-500">*</span>
            </Label>
            <Input
              id="destination"
              value={chemical.destination || ''}
              onChange={(e) => updateChemicalData({ destination: e.target.value })}
              className={`mt-1 focus:border-[#294d48]/50 ${!showValidationErrors || chemical.destination ? 'border-[#294d48]/20' : 'border-red-300'}`}
              placeholder="Enter destination"
            />
            <ValidationMessage show={showValidationErrors && !chemical.destination} />
          </div>
          <div>
            <Label htmlFor="casNumber" className="text-[#294d48]">
              CAS Number
            </Label>
            <Input
              id="casNumber"
              value={chemical.casNumber}
              onChange={(e) => updateChemicalData({ casNumber: e.target.value })}
              className="mt-1 border-[#294d48]/20 focus:border-[#294d48]/50"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 gap-4">
          <div>
            <Label htmlFor="application" className="text-[#294d48]">
              Application
            </Label>
            <Input
              id="application"
              value={chemical.application}
              onChange={(e) => updateChemicalData({ application: e.target.value })}
              className="mt-1 border-[#294d48]/20 focus:border-[#294d48]/50"
              placeholder="e.g., Pharmaceuticals, Agriculture"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductInfoSection;
