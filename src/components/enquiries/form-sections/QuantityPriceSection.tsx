import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ChemicalData, QuantityUnit } from "../types/formTypes";
import ValidationMessage from "../components/ValidationMessage";

const unitsOptions: QuantityUnit[] = [
  "Metric Ton (mt)",
  "Pound (lb)",
  "Gallon (gal)",
  "Litre (L)",
  "Kilolitre (Kl)",
  "Kilogram (Kg)",
];

const currencies: { value: any; label: string }[] = [
  { label: "US DOLLARS (USD $)", value: "USD" },
  { label: "INDIAN INRS (INR ₹)", value: "INR" },
  { label: "EURO (EUR €)", value: "EURO" },
  { label: "YUAN (CNY ¥)", value: "YUAN" },
  { label: "YEN (JPY ¥)", value: "YEN" },
  { label: "AED (AED د.إ)", value: "AED" },
];

interface QuantityPriceSectionProps {
  chemical: ChemicalData;
  updateChemicalData: (data: Partial<ChemicalData>) => void;
  showValidationErrors?: boolean;
}

const QuantityPriceSection = ({
  chemical,
  updateChemicalData,
  showValidationErrors = false,
}: QuantityPriceSectionProps) => {
  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200 space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="flex flex-col">
          <Label htmlFor="quantity" className="text-[#294d48] h-6 mb-2">
            Quantity <span className="text-red-500">*</span>
          </Label>
          <Input
            id="quantity"
            type="number"
min="0"
            value={chemical.quantity}
            onChange={(e) => {
              // Handle empty string and 0 values correctly
              const value = e.target.value;

              // Allow the field to be empty in the UI
              // This lets users clear the field
              if (value === '') {
                updateChemicalData({
                  quantity: null
                });
                console.log('Quantity cleared to null');
                return;
              }

              // Convert to number for non-empty values
              const numValue = parseFloat(value);

              // Ensure it's a valid number (including 0)
              const finalValue = isNaN(numValue) ? 0 : numValue;

              updateChemicalData({
                quantity: finalValue
              });

              console.log('Quantity updated:', finalValue, 'Type:', typeof finalValue);
            }}
            // required
            className={`border-gray-200 focus:border-[#294d48] ${!showValidationErrors || (chemical.quantity !== null && chemical.quantity !== undefined) ? '' : 'border-red-300'}`}
          />
          <ValidationMessage show={showValidationErrors && (chemical.quantity === null || chemical.quantity === undefined)} />
        </div>

        <div className="flex flex-col">
          <Label htmlFor="quantityUnit" className="text-[#294d48] h-6 mb-2">
            Units <span className="text-red-500">*</span>
          </Label>
          <Select
            value={chemical.quantityUnit || ""}
            onValueChange={(value: QuantityUnit) =>
              updateChemicalData({ quantityUnit: value })
            }
          >
            <SelectTrigger
              id="quantityUnit"
              className={`bg-white ${!showValidationErrors || chemical.quantityUnit ? 'border-gray-200' : 'border-red-300'}`}
            >
              <SelectValue placeholder="Select Units" />
            </SelectTrigger>
            <ValidationMessage show={showValidationErrors && !chemical.quantityUnit} />
            <SelectContent className="bg-white">
              {unitsOptions.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="flex flex-col">
          <Label
            htmlFor="procurementVolume"
            className="text-[#294d48] h-6 mb-2"
          >
            Procurement Volume (Annually)
          </Label>
          <Input
            id="procurementVolume"
            type="number"
min="0"
            value={chemical.procurementVolume}
            onChange={(e) => {
              const value = e.target.value;

              // Allow the field to be empty in the UI
              if (value === '') {
                updateChemicalData({
                  procurementVolume: null
                });
                return;
              }

              // Convert to number for non-empty values
              const numValue = parseFloat(value);

              // Ensure it's a valid number (including 0)
              const finalValue = isNaN(numValue) ? 0 : numValue;

              updateChemicalData({
                procurementVolume: finalValue
              });
            }}
            // Not required
            className="border-gray-200 focus:border-[#294d48]"
          />
        </div>

        <div className="flex flex-col">
          <Label htmlFor="procurementUnit" className="text-[#294d48] h-6 mb-2">
            Units
          </Label>
          <Select
            value={chemical.procurementUnit || ""}
            onValueChange={(value: QuantityUnit) =>
              updateChemicalData({ procurementUnit: value })
            }
          >
            <SelectTrigger
              id="procurementUnit"
              className="bg-white border-gray-200"
            >
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent className="bg-white">
              {unitsOptions.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="flex flex-col">
          <Label
            htmlFor="expectedProcurementVolume"
            className="text-[#294d48] h-6 mb-2"
          >
            Expected Procurement Volume (Annually)
          </Label>
          <Input
            id="expectedProcurementVolume"
            type="number"
min="0"
            value={chemical.expectedprocurementVolume}
            onChange={(e) => {
              const value = e.target.value;

              // Allow the field to be empty in the UI
              if (value === '') {
                updateChemicalData({
                  expectedprocurementVolume: null
                });
                return;
              }

              // Convert to number for non-empty values
              const numValue = parseFloat(value);

              // Ensure it's a valid number (including 0)
              const finalValue = isNaN(numValue) ? 0 : numValue;

              updateChemicalData({
                expectedprocurementVolume: finalValue
              });
            }}
            // Not required
            className="border-gray-200 focus:border-[#294d48]"
          />
        </div>

        <div className="flex flex-col">
          <Label
            htmlFor="expectedProcurementUnit"
            className="text-[#294d48] h-6 mb-2"
          >
            Units
          </Label>
          <Select
            value={chemical.expectedprocurementUnit || ""}
            onValueChange={(value: QuantityUnit) =>
              updateChemicalData({ expectedprocurementUnit: value })
            }
          >
            <SelectTrigger
              id="expectedProcurementUnit"
              className="bg-white border-gray-200"
            >
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent className="bg-white">
              {unitsOptions.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="flex flex-col">
          <Label
            htmlFor="targetPrice"
            className="text-[#294d48] h-6 mb-2 block"
          >
            Target Price (Optional)
          </Label>
          <Input
            id="targetPrice"
            type="number"
min="0"
            value={chemical.targetPrice || ""}
            onChange={(e) =>
              updateChemicalData({ targetPrice: parseFloat(e.target.value) })
            }
            className="border-gray-200 focus:border-[#294d48]"
          />
        </div>
        <div className="flex flex-col">
          <Label
            htmlFor="expectedProcurementUnit"
            className="text-[#294d48] h-6 mb-2"
          >
            Currency
          </Label>
          <Select
            value={chemical.targetPriceCurrency || ""}
            onValueChange={(value: any) =>
              updateChemicalData({ targetPriceCurrency: value })
            }
          >
            <SelectTrigger
              id="expectedProcurementUnit"
              className="bg-white border-gray-200"
            >
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent className="bg-white">
              {currencies.map((currency) => (
                <SelectItem key={currency.value} value={currency.value}>
                  {currency.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default QuantityPriceSection;
