import { ChemicalData } from "../types/formTypes";

// Function to validate a chemical's required fields
export const validateChemical = (chemical: ChemicalData) => {
  // Check if chemical name or brand/product is provided
  const hasChemicalName = chemical.chemicalName !== undefined && chemical.chemicalName !== "";
  const hasBrandAndProduct =
    chemical.brand !== undefined && chemical.brand !== "" &&
    chemical.product !== undefined && chemical.product !== "";

  // Check for other required fields
  const hasQuantity = chemical.quantity !== undefined && chemical.quantity !== null;
  const hasQuantityUnit = chemical.quantityUnit !== undefined && chemical.quantityUnit !== null;
  // Category/BU validation removed as requested
  const hasIncoterms = chemical.incoterms !== undefined && chemical.incoterms !== "";

  // Return true only if all required fields are filled
  // Note: procurement volume and expected procurement volume are optional
  return (hasChemicalName || hasBrandAndProduct) &&
         hasQuantity &&
         hasQuantityUnit &&
         hasIncoterms;
};
