import { useState, useEffect } from "react";
import ProductInfoSection from "./form-sections/ProductInfoSection";
import LocationInfoSection from "./form-sections/LocationInfoSection";
import QuantityPriceSection from "./form-sections/QuantityPriceSection";
import AdditionalDetailsSection from "./form-sections/AdditionalDetailsSection";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import EnquiryFormActions from "./components/EnquiryFormActions";
import EnquirySuccessDialog from "./components/EnquirySuccessDialog";
import DocumentUpload from "./components/DocumentUpload";
import { useEnquiryForm } from "./hooks/useEnquiryForm";

import { FilePlus, Plus, X } from "lucide-react";
import { ChemicalData } from "./types/formTypes";
import { toast } from "sonner";
import "@/styles/custom-scrollbar.css";

interface ChemicalFormSectionProps {
  chemical: ChemicalData;
  index: number;
  updateChemicalData: (data: Partial<ChemicalData>, index: number) => void;
  updateChemicalFiles: (files: File[], index: number) => void;
  useChemicalName: boolean;
  setUseChemicalName: (value: boolean) => void;
  showValidationErrors?: boolean;
}

const ChemicalFormSection: React.FC<ChemicalFormSectionProps> = ({
  chemical,
  index,
  updateChemicalData,
  updateChemicalFiles,
  useChemicalName,
  setUseChemicalName,
  showValidationErrors = false,
}) => {
  return (
    <div className="space-y-8">
      {/* We don't need a separate remove button here anymore since it's in the tab */}

      <ProductInfoSection
        useChemicalName={useChemicalName}
        setUseChemicalName={setUseChemicalName}
        chemical={chemical}
        updateChemicalData={(data) => updateChemicalData(data, index)}
        showValidationErrors={showValidationErrors}
      />

      <AdditionalDetailsSection
        chemical={chemical}
        updateChemicalData={(data) => updateChemicalData(data, index)}
        showValidationErrors={showValidationErrors}
      />

      <QuantityPriceSection
        chemical={chemical}
        updateChemicalData={(data) => updateChemicalData(data, index)}
        showValidationErrors={showValidationErrors}
      />

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-[#294d48]">Documents</h3>
        <p className="text-sm text-gray-500">Attach relevant documents for this chemical</p>
        <DocumentUpload
          files={chemical.attachedFiles}
          onFilesChange={(files) => updateChemicalFiles(files, index)}
        />
      </div>
    </div>
  );
};



// Function to validate a chemical's required fields
const validateChemical = (chemical: ChemicalData) => {
  // Check if chemical name or brand/product is provided
  const hasChemicalName = chemical.chemicalName !== undefined && chemical.chemicalName !== "";
  const hasBrandAndProduct =
    chemical.brand !== undefined && chemical.brand !== "" &&
    chemical.product !== undefined && chemical.product !== "";

  // Check for other required fields
  const hasQuantity = chemical.quantity !== undefined && chemical.quantity !== null;
  const hasQuantityUnit = chemical.quantityUnit !== undefined && chemical.quantityUnit !== null;
  const hasCategory = chemical.category !== undefined && chemical.category !== "";
  const hasIncoterms = chemical.incoterms !== undefined && chemical.incoterms !== "";

  // Return true only if all required fields are filled
  // Note: procurement volume and expected procurement volume are optional
  return (hasChemicalName || hasBrandAndProduct) &&
         hasQuantity &&
         hasQuantityUnit &&
         hasCategory &&
         hasIncoterms;
};

const CreateEnquiryForm = () => {
  const {
    chemicals,
    customerData,
    isSubmitting,
    useChemicalName,
    showSuccessDialog,
    setShowSuccessDialog,
    setUseChemicalName,
    updateChemicalData,
    updateChemicalFiles,
    updateCustomerData,
    isFormComplete,
    handleSubmit,
    selectedCustomerId,
    setSelectedCustomerId,
    addNewChemical,
    removeChemical,
    showValidationErrors,
    setShowValidationErrors,
    clearForm,
  } = useEnquiryForm();

  const [expandedIndex, setExpandedIndex] = useState<number>(0);

  // Log when expandedIndex changes
  useEffect(() => {
    console.log('Expanded index changed to:', expandedIndex);
  }, [expandedIndex]);

  // Modified to be async and return a Promise
  const handleFormSubmit = async (): Promise<void> => {
    console.log('Form submission started');

    // Note: Validation is now handled in EnquiryFormActions component
    // This function is only called if validation passes

    const allFiles = chemicals.flatMap(chemical => chemical.attachedFiles);
    await handleSubmit(allFiles);
    // Reset expanded index to 0 after submission
    setExpandedIndex(0);
  };

  return (
    <div className="space-y-8">
      <div className="max-w-[1200px] mx-auto">
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6 border-b border-gray-100 pb-4">
            <div className="flex items-center gap-2">
              <FilePlus className="h-5 w-5 text-[#294d48]" />
              <h2 className="text-xl font-semibold text-[#294d48]">Create New Enquiry</h2>
            </div>
          </div>

          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleFormSubmit();
            }}
            className="bg-white p-6 rounded-lg border border-gray-200"
          >
            <div className="grid grid-cols-2 gap-10">
              <div className="space-y-4">
                {/* Tab-like interface for chemicals */}
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  {/* Chemical Tabs - with horizontal scrolling */}
                  <div className="flex bg-gray-50 border-b border-gray-200 overflow-x-auto hide-scrollbar">
                    {/* Map through chemicals to create tabs */}
                    {chemicals.map((chemical, index) => (
                      <div
                        key={index}
                        className={`flex items-center whitespace-nowrap ${expandedIndex === index
                          ? 'bg-white text-[#294d48] border-b-2 border-[#294d48]'
                          : 'text-gray-600 hover:text-[#294d48] hover:bg-gray-100'
                          }`}
                      >
                        {/* Tab button - make it a div with onClick instead of a button */}
                        <div
                          onClick={() => {
                            console.log('Clicked on tab:', index);
                            setExpandedIndex(index);
                          }}
                          className="px-3 py-2 text-sm font-medium flex-grow text-left cursor-pointer truncate max-w-[150px]"
                        >
                          {chemical.chemicalName || chemical.product || `Chemical ${index + 1}`}
                        </div>

                        {/* Remove button */}
                        {chemicals.length > 1 && (
                          <div
                            onClick={(e) => {
                              e.stopPropagation();
                              // If removing the current tab, focus on the previous one
                              if (index === expandedIndex) {
                                // Focus on previous tab or the first tab if removing the first one
                                const newIndex = index > 0 ? index - 1 : 0;
                                setExpandedIndex(newIndex);
                              } else if (index < expandedIndex) {
                                // If removing a tab before the current one, adjust the current index
                                setExpandedIndex(expandedIndex - 1);
                              }
                              // Remove the chemical
                              removeChemical(index);
                            }}
                            className="px-2 text-gray-400 hover:text-red-500 cursor-pointer"
                            title="Remove chemical"
                          >
                            <X className="h-3.5 w-3.5" />
                          </div>
                        )}
                      </div>
                    ))}

                    {/* Add Chemical Tab Button */}
                    <div
                      className="px-3 py-2 text-sm font-medium text-[#294d48] hover:bg-gray-100 flex items-center cursor-pointer whitespace-nowrap"
                      onClick={() => {
                        // Validate current chemical before adding a new one
                        // Only validate if there are chemicals already
                        if (chemicals.length > 0) {
                          const currentChemical = chemicals[expandedIndex];
                          const isValid = validateChemical(currentChemical);

                          if (isValid) {
                            addNewChemical();
                            console.log('Adding new chemical, current length:', chemicals.length);
                            // Set expanded index to the new chemical (which will be at the end of the array)
                            setExpandedIndex(chemicals.length);
                            // Reset validation errors when adding a new chemical successfully
                            setShowValidationErrors(false);
                          } else {
                            // Show validation errors
                            setShowValidationErrors(true);
                            toast.error('Please fill in the required fields (chemical name/brand/product, quantity, quantity unit, Category/BU, and Incoterms) for the current chemical before adding a new one.');
                          }
                        } else {
                          // If no chemicals yet, just add one without validation
                          addNewChemical();
                          console.log('Adding first chemical');
                          setExpandedIndex(0);
                        }
                        // Note: Customer details are not validated when adding a new chemical
                        // They will be validated only when submitting the form
                      }}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add
                    </div>
                  </div>

                  {/* Chemical Content */}
                  <div className="p-4">
                    {chemicals.map((chemical, index) => (
                      <div key={index} className={expandedIndex === index ? 'block' : 'hidden'}>
                        <ChemicalFormSection
                          chemical={chemical}
                          index={index}
                          updateChemicalData={updateChemicalData}
                          updateChemicalFiles={updateChemicalFiles}
                          useChemicalName={useChemicalName}
                          setUseChemicalName={setUseChemicalName}
                          showValidationErrors={showValidationErrors}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="space-y-8">
                {/* Customer Details */}
                <div className="bg-white p-6 rounded-lg border border-gray-200">
                  <h3 className="text-lg font-semibold text-[#294d48] mb-1">Customer Details</h3>
                  <p className="text-sm text-gray-500 mb-4">Specify the delivery and quotation locations</p>
                  <LocationInfoSection
                    customerData={customerData}
                    updateCustomerData={updateCustomerData}
                    selectedCustomerId={selectedCustomerId}
                    setSelectedCustomerId={setSelectedCustomerId}
                    showValidationErrors={showValidationErrors}
                  />
                </div>

                {/* Sales Information */}
                <div className="bg-white p-6 rounded-lg border border-gray-200">
                  <h3 className="text-lg font-semibold text-[#294d48] mb-1">Sales Information</h3>
                  <p className="text-sm text-gray-500 mb-4">Sales team member and customer details</p>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="salesTeamMember" className="text-[#294d48]">
                        Sales Team Member Email
                      </Label>
                      <Input
                        id="salesTeamMember"
                        value={customerData.salesTeamMember}
                        readOnly
                        className="bg-gray-50 border-gray-200"
                      />
                    </div>
                  </div>
                </div>

                {/* Form Actions */}
                <EnquiryFormActions
                  isSubmitting={isSubmitting}
                  isFormComplete={isFormComplete}
                  onSubmit={handleFormSubmit}
                  disabled={isSubmitting || !customerData.salesTeamMember}
                  customerData={customerData}
                  updateCustomerData={updateCustomerData}
                  setShowValidationErrors={setShowValidationErrors}
                  onClearForm={clearForm}
                />
              </div>
            </div>

            <EnquirySuccessDialog
              showDialog={showSuccessDialog}
              setShowDialog={setShowSuccessDialog}
            />
          </form>
        </div>
      </div>
    </div>
  );
};

export default CreateEnquiryForm;
