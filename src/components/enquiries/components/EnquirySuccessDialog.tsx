
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { formatStatus } from "@/components/stats/enquiries/utils/statusUtils";

interface EnquirySuccessDialogProps {
  showDialog: boolean;
  setShowDialog: (show: boolean) => void;
}

const EnquirySuccessDialog = ({ showDialog, setShowDialog }: EnquirySuccessDialogProps) => {
  const navigate = useNavigate();
  const initialStatus = "enquiry_created";

  return (
    <Dialog open={showDialog} onOpenChange={setShowDialog}>
      <DialogContent className="sm:max-w-md bg-white border-2 border-[#E6EAE9] rounded-lg shadow-sm">
        <DialogHeader>
          <DialogTitle className="text-center text-[#294d48] text-xl font-semibold">
            Enquiry Created Successfully!
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center py-6">
          <CheckCircle className="h-16 w-16 text-[#294d48] opacity-90 animate-scale-in mb-4" />
          <p className="text-[#555555] text-lg text-center mb-3">
            Your enquiry has been submitted successfully
          </p>
          <div className="bg-[#F6FAFA] rounded-lg p-3 border border-[#E6EAE9] w-full mb-4">
            <div className="flex items-center text-sm text-[#294d48]">
              <div className="w-3 h-3 rounded-full bg-[#294d48] mr-2"></div>
              <span className="font-medium">Status: {formatStatus(initialStatus)}</span>
            </div>
          </div>
          <Button 
            onClick={() => {
              setShowDialog(false);
              navigate("/crm");
            }}
            className="bg-[#294d48] hover:bg-[#294d48]/90 text-white px-4 py-2 rounded-md"
          >
            Okay
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EnquirySuccessDialog;
