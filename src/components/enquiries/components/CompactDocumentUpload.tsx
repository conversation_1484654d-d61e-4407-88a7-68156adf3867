import { useRef, useState } from "react";
import { Upload, File, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface CompactDocumentUploadProps {
  onFilesChange: (files: File[]) => void;
  files: File[];
  maxSize?: number;
  multiple?: boolean;
  className?: string;
}

const CompactDocumentUpload = ({
  onFilesChange,
  files,
  maxSize = 10,
  multiple = true,
  className = "",
}: CompactDocumentUploadProps) => {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>(files || []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      validateAndAddFiles(Array.from(e.target.files));
      e.target.value = ""; // Reset input to allow re-uploading same file
    }
  };

  const validateAndAddFiles = (newFiles: File[]) => {
    const maxSizeBytes = maxSize * 1024 * 1024;
    const validFiles = newFiles.filter((file) => {
      const isValidSize = file.size <= maxSizeBytes;
      if (!isValidSize) {
        toast.error(`File ${file.name} is too large. Maximum size is ${maxSize}MB`);
      }
      return isValidSize;
    });

    // Update state correctly
    const updatedFiles = [...uploadedFiles, ...validFiles];
    setUploadedFiles(updatedFiles);
    onFilesChange(updatedFiles);
  };

  const removeFile = (index: number) => {
    const updatedFiles = uploadedFiles.filter((_, i) => i !== index);
    setUploadedFiles(updatedFiles);
    onFilesChange(updatedFiles);
  };

  const triggerFileSelect = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className={`${className} w-full`}>
      <div className="flex items-center w-full">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={triggerFileSelect}
          className="flex items-center text-sm w-full justify-center"
        >
          <Upload className="h-4 w-4 mr-1" />
          Upload
        </Button>
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.jpg,.jpeg,.png,.gif,.zip,.rar"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>

      {uploadedFiles.length > 0 && (
        <div className="mt-2 max-h-[80px] overflow-y-auto pr-1 border border-gray-100 rounded-md">
          <div className="space-y-1 p-1">
            {uploadedFiles.map((file, index) => (
              <div key={index} className="flex items-center justify-between py-1 text-xs bg-gray-50 px-2 rounded">
                <div className="flex items-center space-x-2 truncate max-w-[85%]">
                  <File className="h-3 w-3 text-blue-500 flex-shrink-0" />
                  <span className="truncate">{file.name}</span>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFile(index)}
                  className="h-5 w-5 p-0 text-gray-500 hover:text-red-500 flex-shrink-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CompactDocumentUpload;
