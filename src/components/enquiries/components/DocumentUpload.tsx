import { useEffect, useState, useRef } from "react";
import { Upload, File, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface DocumentUploadProps {
  onFilesChange: (files: File[]) => void;
  files: File[];
  maxSize?: number;
  accept?: string;
  multiple?: boolean;
  label?: React.ReactNode;
  helpText?: string;
}

const DocumentUpload = ({
  onFilesChange,
  files,
  maxSize = 10,
  accept,
  multiple = true,
  label = "Drag and drop files here, or click to select files",
  helpText = `Maximum file size: ${maxSize}MB`,
}: DocumentUploadProps) => {
  const [isDragging, setIsDragging] = useState(false);
  const [uploadedfiles, setUploadingFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  // Add reset handler
  // useEffect(() => {
  //   const handleReset = () => {
  //     setUploadingFiles([]);
  //     onFilesChange([]);
  //     if (fileInputRef.current) {
  //       fileInputRef.current.value = '';
  //     }
  //   };

  //   const element = fileInputRef.current?.closest('.document-upload');
  //   element?.addEventListener('reset-files', handleReset);

  //   return () => {
  //     element?.removeEventListener('reset-files', handleReset);
  //   };
  // }, [onFilesChange]);


  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    validateAndAddFiles(Array.from(e.dataTransfer.files));
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      validateAndAddFiles(Array.from(e.target.files));
      e.target.value = ""; // Reset input to allow re-uploading same file
    }
  };

  const validateAndAddFiles = (newFiles: File[]) => {
    const maxSizeBytes = maxSize * 1024 * 1024;
    const validFiles = newFiles.filter((file) => {
      const isValidSize = file.size <= maxSizeBytes;
      if (!isValidSize) {
        toast.error(`File ${file.name} is too large. Maximum size is ${maxSize}MB`);
      }
      return isValidSize;
    });

    // Update state correctly
    setUploadingFiles((prev) => [...prev, ...validFiles]);
    onFilesChange([...files, ...validFiles]);
  };

  const removeFile = (index: number) => {
    const updatedFiles = uploadedfiles.filter((_, i) => i !== index);
    setUploadingFiles(updatedFiles);
    onFilesChange(updatedFiles);
  };

  useEffect(() => {
    console.log("Updated files:", uploadedfiles);
  }, [uploadedfiles]);


  const triggerFileSelect = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  

  return (
    <div className="space-y-4">
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors duration-200 ${
          isDragging ? "border-[#7E69AB] bg-[#F8F6FF]" : "border-gray-300 hover:border-[#7E69AB]"
        }`}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept={accept}
          onChange={handleFileSelect}
          className="hidden"
          id="file-upload"
        />
        <label  className="cursor-pointer" onClick={triggerFileSelect}>
          <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-sm text-gray-600">{label}</p>
          <p className="text-xs text-gray-500 mt-2">{helpText}</p>
        </label>
      </div>

      {uploadedfiles.length > 0 && (
        <div className="space-y-2">
          {uploadedfiles.map((file, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200">
              <div className="flex items-center space-x-3">
                <File className="h-5 w-5 text-[#7E69AB]" />
                <div>
                  <p className="text-sm font-medium text-gray-700">{file.name}</p>
                  <p className="text-xs text-gray-500">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeFile(index)}
                className="text-gray-500 hover:text-red-500"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DocumentUpload;
