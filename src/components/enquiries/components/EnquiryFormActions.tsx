import React, { useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { HelpCircle } from "lucide-react";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip";
import { CustomerData } from "../types/formTypes";

interface EnquiryFormActionsProps {
  isSubmitting: boolean;
  isFormComplete: () => boolean;
  onSubmit: () => Promise<void>;
  disabled: boolean;
  customerData: CustomerData;
  updateCustomerData: (data: Partial<CustomerData>) => void;
  setShowValidationErrors?: (value: boolean) => void;
  onClearForm?: () => void;
}

const EnquiryFormActions = ({
  isSubmitting,
  isFormComplete,
  onSubmit,
  disabled,
  customerData,
  updateCustomerData,
  setShowValidationErrors,
  onClearForm
}: EnquiryFormActionsProps) => {
  // Memoize the criticality change handler to prevent unnecessary re-renders
  const handleCriticalityChange = useCallback((criticality: 'high' | 'medium' | 'low') => {
    updateCustomerData({ criticality });
  }, [updateCustomerData]);
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center gap-2">
              <Label htmlFor="criticality" className="text-[#294d48] flex items-center">
                Criticality
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-4 w-4 ml-2 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Select the criticality level for this enquiry
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
            </div>
            <div className="flex gap-2">
              <Label
                htmlFor="criticality-high"
                className={`cursor-pointer px-3 py-1.5 rounded ${customerData.criticality === 'high' ? 'bg-red-500 text-white' : 'bg-gray-100 text-gray-700'}`}
              >
                <input
                  type="radio"
                  id="criticality-high"
                  name="criticality"
                  className="sr-only"
                  checked={customerData.criticality === 'high'}
                  onChange={() => handleCriticalityChange('high')}
                />
                High
              </Label>
              <Label
                htmlFor="criticality-medium"
                className={`cursor-pointer px-3 py-1.5 rounded ${customerData.criticality === 'medium' ? 'bg-yellow-500 text-white' : 'bg-gray-100 text-gray-700'}`}
              >
                <input
                  type="radio"
                  id="criticality-medium"
                  name="criticality"
                  className="sr-only"
                  checked={customerData.criticality === 'medium'}
                  onChange={() => handleCriticalityChange('medium')}
                />
                Medium
              </Label>
              <Label
                htmlFor="criticality-low"
                className={`cursor-pointer px-3 py-1.5 rounded ${customerData.criticality === 'low' ? 'bg-green-500 text-white' : 'bg-gray-100 text-gray-700'}`}
              >
                <input
                  type="radio"
                  id="criticality-low"
                  name="criticality"
                  className="sr-only"
                  checked={customerData.criticality === 'low'}
                  onChange={() => handleCriticalityChange('low')}
                />
                Low
              </Label>
            </div>
          </div>
        </div>
      </div>

      <div className="flex gap-4">
        {/* Clear Form Button */}
        {onClearForm && (
          <Button
            type="button"
            onClick={() => {
              if (window.confirm('Are you sure you want to clear the form? All unsaved data will be lost.')) {
                onClearForm();
                toast.success('Form cleared successfully');
              }
            }}
            className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 py-6 text-base font-medium transition-all"
            disabled={isSubmitting}
          >
            Clear Form
          </Button>
        )}

        {/* Submit Button */}
        <Button
          type="button"
          onClick={() => {
            console.log('Submit button clicked');
            // Always show validation errors when submitting
            if (setShowValidationErrors) {
              setShowValidationErrors(true);
            }

            // Check if the form is complete
            const formComplete = isFormComplete();
            console.log('Form complete?', formComplete);

            if (!formComplete) {
              console.log('Form validation failed in EnquiryFormActions');
              toast.error("Please fill in all required fields before creating the enquiry");
              return;
            }

            console.log('Form validation passed, submitting...');
            onSubmit();
          }}
          className="flex-1 bg-[#294d48] hover:bg-[#294d48]/90 py-6 text-base font-medium transition-all"
          disabled={disabled}
        >
          {isSubmitting ? "Creating..." : "Create Enquiry"}
        </Button>
      </div>
    </div>
  );
};

export default EnquiryFormActions;
