
import { useState, useEffect, useCallback } from "react";
import { ChemicalData, CustomerData } from "../types/formTypes";
import { useEnquiryAuth } from "./useEnquiryAuth";
import { useEnquiryValidation } from "./useEnquiryValidation";
import { useEnquirySubmission } from "./useEnquirySubmission";
import { useLocalStorage } from "@/hooks/useLocalStorage";
import { supabase } from "@/integrations/supabase/client";
import { categoryOptions as CATEGORY_OPTIONS } from "@/config/constants";

// Function to determine default quantity unit based on user's country
const getDefaultQuantityUnit = (countryCode?: string): "Pound (lb)" | "Kilogram (Kg)" => {
  // If country code is provided, use it to determine the unit
  if (countryCode) {
    // US uses imperial system (pounds)
    const usesImperial = countryCode === 'US' || countryCode === 'USA';
    return usesImperial ? "Pound (lb)" : "Kilogram (Kg)";
  }

  // Fallback to browser language if country code is not available
  const isUS = navigator.language === 'en-US' ||
               navigator.language.startsWith('en-US') ||
               navigator.language === 'en-us';

  // Return Pound for US users, Kg for everyone else
  return isUS ? "Pound (lb)" : "Kilogram (Kg)";
};

// Function to determine default currency based on user's country
const getDefaultCurrency = (countryCode?: string): string => {
  if (!countryCode) {
    return "USD"; // Default to USD if no country code
  }

  // Map country codes to their respective currencies
  const countryCurrencyMap: Record<string, string> = {
    'US': 'USD',  // United States - USD
    'USA': 'USD', // United States - USD
    'IN': 'INR',     // India - INR
    'IND': 'INR',    // India - INR
    'EU': 'EUR',     // European Union - EUR
    'DE': 'EUR',     // Germany - EUR
    'FR': 'EUR',     // France - EUR
    'IT': 'EUR',     // Italy - EUR
    'ES': 'EUR',     // Spain - EUR
    'CN': 'CNY',     // China - CN
    'CHN': 'CNY',    // China - CNY
    'JP': 'JPY',     // Japan - JPY
    'JPN': 'JPY',    // Japan - JPY
    'AE': 'AED',     // United Arab Emirates - AED
    'ARE': 'AED'     // United Arab Emirates - AED
  };

  console.log('Country code:', countryCode, 'Currency:', countryCurrencyMap[countryCode] || "USD");

  // Return the currency for the country code, or default to USD
  return countryCurrencyMap[countryCode] || "USD";
};

// Function to determine default Incoterms based on user's country
const getDefaultIncoterms = (countryCode?: string): string => {
  // If country code is provided, use it to determine the Incoterms
  if (countryCode) {
    // US uses DDP, others use CIF
    const isUS = countryCode === 'US' || countryCode === 'USA';
    return isUS ? "DDP (Delivery Duty Paid)" : "CIF (Cost, Insurance, & Freight)";
  }

  // Fallback to browser language if country code is not available
  const isUS = navigator.language === 'en-US' ||
               navigator.language.startsWith('en-US') ||
               navigator.language === 'en-us';

  // Return DDP for US users, CIF for everyone else
  return isUS ? "DDP (Delivery Duty Paid)" : "CIF (Cost, Insurance, & Freight)";
};

export const useEnquiryForm = () => {
  // State to store the user's category and country
  const [userCategory, setUserCategory] = useState<string>("");
  const [userCountry, setUserCountry] = useState<string | null>(null);
  const [selectedSalesPOC, setSelectedSalesPOC] = useState<string>("");
  const [salesTeamMembers, setSalesTeamMembers] = useState<{ value: string; label: string; category: string, categories: any }[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [categoryOptions, setCategoryOptions] = useState<{ value: string; label: string }[]>([]);

  // Initially use browser locale for defaults (will be updated later with user's country)
  // We can't use userCountry here because it's not available yet - it will be fetched from the database
  const browserDefaultUnit = getDefaultQuantityUnit();
  const browserDefaultCurrency = getDefaultCurrency();
  const browserDefaultIncoterms = getDefaultIncoterms();

  // Initialize with empty data, but will be overridden by localStorage if available
  const defaultChemicalData: ChemicalData = {
    chemicalName: "",
    brand: "",
    product: "",
    casNumber: "",
    application: "",
    criticality:"medium",
    targetPrice: null,
    packagingType: "",
    qtyPerPackaging: null,
    qtyPerPackagingUnit: browserDefaultUnit,
    targetPriceCurrency: browserDefaultCurrency, // Set default currency based on browser locale
    quantity: null,
    quantityUnit: browserDefaultUnit, // Will be updated based on user's country
    category: "", // Will be updated with the user's category
    incoterms: browserDefaultIncoterms, // Set default incoterms based on browser locale
    destination: "", // Added destination field
    destination_country: null,
    remarks: "",
    expectedprocurementVolume: null,
    expectedprocurementUnit: browserDefaultUnit, // Will be updated based on user's country
    procurementVolume: null,
    procurementUnit: browserDefaultUnit, // Will be updated based on user's country
    attachedFiles: [],
    offset_chemical_id: null,
    offset_chemical_name: null,
    type: null
  };

  const defaultCustomerData: CustomerData = {
    city: "",
    country: "",
    industries: [],
    salesTeamMember: "",
    customerFullName: "", // Optional but included in default
    customerPoc: "", // Optional
    customerPhone: "",
    customerEmail: ""
  };

  // Use localStorage to persist form data
  const [chemicals, setChemicalsState] = useLocalStorage<ChemicalData[]>('enquiry-chemicals', [defaultChemicalData]);
  const [customerData, setCustomerDataState] = useLocalStorage<CustomerData>('enquiry-customer', defaultCustomerData);

  // Wrapper functions to update both state and localStorage
  const setChemicals = useCallback((value: ChemicalData[] | ((prev: ChemicalData[]) => ChemicalData[])) => {
    // Update the state with the full data including files
    setChemicalsState(value);

    // We don't need to manually save to localStorage here as the saveFormToLocalStorage
    // function will handle that with the file exclusion logic
  }, [setChemicalsState]);

  const setCustomerData = useCallback((value: CustomerData | ((prev: CustomerData) => CustomerData)) => {
    setCustomerDataState(value);
  }, [setCustomerDataState]);

  // Memoize the updateCustomerData function to prevent unnecessary re-renders
  const updateCustomerData = useCallback((newData: Partial<CustomerData>) => {
    setCustomerData(prev => ({ ...prev, ...newData }));
  }, [setCustomerData]);

  const [useChemicalName, setUseChemicalName] = useState(true);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);

  // Use the auth hook with a callback that properly updates localStorage
  // We use useCallback to memoize the callback to prevent unnecessary re-renders
  const authCallback = useCallback(async (salesTeamMember: string, userId?: string) => {
    console.log('Auth callback triggered with salesTeamMember:', salesTeamMember, 'userId:', userId);

    // Update the customerData with the salesTeamMember
    setCustomerData(prev => ({
      ...prev,
      salesTeamMember
    }));

    // First check localStorage for user data
    const storedCategory = localStorage.getItem('userCategory') || '';
    const storedCountry = localStorage.getItem('userCountry') || null;
    const userRole = localStorage.getItem("userRole");
    const storedCategoryArray = localStorage.getItem('categoryOptions') || '[]';
    const parsedCategoryArray = JSON.parse(storedCategoryArray);
    const categoryOptions = parsedCategoryArray?.map((category: string) => ({ value: category, label: category })) || [];
    setCategoryOptions(categoryOptions);
    if(userRole === "admin"){
      setCategoryOptions(CATEGORY_OPTIONS)
    }
    

    // If we have data in localStorage, use it
    if (storedCategory && storedCountry && storedCategoryArray) {
      console.log('Using user data from localStorage:', { category: storedCategory, country: storedCountry });
      
      setUserCategory(categoryOptions[0]?.value || storedCategory);
      setUserCountry(storedCountry);
      if(categoryOptions?.length === 1 && userRole !== "admin"){
        setSelectedCategory(categoryOptions[0]?.value || storedCategory);
      } else {
        setSelectedCategory("")
      }

      // Determine defaults based on stored country
      let defaultUnit: "Pound (lb)" | "Kilogram (Kg)";
      let defaultCurrency: string;
      let defaultIncoterms: string;

      if (storedCountry) {
        defaultUnit = getDefaultQuantityUnit(storedCountry);
        defaultCurrency = getDefaultCurrency(storedCountry);
        defaultIncoterms = getDefaultIncoterms(storedCountry);
      } else {
        defaultUnit = getDefaultQuantityUnit();
        defaultCurrency = getDefaultCurrency();
        defaultIncoterms = getDefaultIncoterms();
      }

      // Update chemicals if category is available
      if (categoryOptions.length > 0) {
        setChemicals(prev => prev.map(chemical => {
          if (chemical.category !== categoryOptions[0]?.value) {
            return {
              ...chemical,
              category: categoryOptions[0]?.value,
              quantityUnit: defaultUnit,
              qtyPerPackagingUnit: defaultUnit,
              expectedprocurementUnit: defaultUnit,
              procurementUnit: defaultUnit,
              targetPriceCurrency: defaultCurrency,
              incoterms: defaultIncoterms
            };
          }
          return chemical;
        }));
      }
    } 
    // If no data in localStorage, fetch from database
    else if (userId) {
      try {
        const { data: userData, error: userError } = await supabase
          .from("user_roles")
          .select("category, country, role, is_crm_enabled, categories")
          .eq("user_id", userId)
          .single();

        if (userError) {
          console.error('Error fetching user data:', userError);
        } else if (userData) {
          console.log('Fetched user data from database:', userData);
          
          const userCategory = userData.category || '';
          const userCountry = userData.country || null;
          const categories = userData.categories || null

          setUserCountry(userCountry);
          setUserCategory(categories[0]);
          if(categories?.length === 1){
            setSelectedCategory(categories[0]);
          }

          let defaultUnit: "Pound (lb)" | "Kilogram (Kg)";
          let defaultCurrency: string;
          let defaultIncoterms: string;

          if (userCountry) {
            defaultUnit = getDefaultQuantityUnit(userCountry);
            defaultCurrency = getDefaultCurrency(userCountry);
            defaultIncoterms = getDefaultIncoterms(userCountry);
          } else {
            defaultUnit = getDefaultQuantityUnit();
            defaultCurrency = getDefaultCurrency();
            defaultIncoterms = getDefaultIncoterms();
          }

          if (userCategory) {
            setChemicals(prev => prev.map(chemical => {
              if (chemical.category !== userCategory) {
                return {
                  ...chemical,
                  category: userCategory,
                  quantityUnit: defaultUnit,
                  qtyPerPackagingUnit: defaultUnit,
                  expectedprocurementUnit: defaultUnit,
                  procurementUnit: defaultUnit,
                  targetPriceCurrency: defaultCurrency,
                  incoterms: defaultIncoterms
                };
              }
              return chemical;
            }));
          }
        }
      } catch (error) {
        console.error('Error in auth callback:', error);
      }
    }
  }, [setCustomerData, setUserCategory, setUserCountry, setChemicals]);

  // Use the auth hook with our memoized callback
  useEnquiryAuth(authCallback);

  // Use the validation hook
  const { isFormComplete } = useEnquiryValidation(chemicals, customerData);

  // State to track if validation errors should be shown
  const [showValidationErrors, setShowValidationErrors] = useState(false);

  // Debug logging has been removed to clean up the console

  // Use the submission hook
  const { isSubmitting, handleSubmit: submitEnquiry } = useEnquirySubmission(
    chemicals, // Send the entire chemicals array instead of just chemicals[0]
    customerData,
    setChemicals,
    setCustomerData,
    isFormComplete,
    setShowSuccessDialog,
    selectedCustomerId,
    selectedSalesPOC, // Replace with the correct state or value
    salesTeamMembers // Ensure this is fetched or passed correctly
  );

  // Memoize the updateChemicalData function to prevent unnecessary re-renders
  const updateChemicalData = useCallback((newData: Partial<ChemicalData>, index: number = 0) => {
    setChemicals(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], ...newData };
      return updated;
    });
  }, [setChemicals]);

  // Note: updateCustomerData is now defined above with useCallback

  // Memoize the addNewChemical function to prevent unnecessary re-renders
  const addNewChemical = useCallback(() => {
    // Get default unit and currency based on user's country from the database
    // userCountry is fetched from user_roles table and will be more accurate than browser locale
    const defaultUnit = getDefaultQuantityUnit(userCountry);
    const defaultCurrency = getDefaultCurrency(userCountry);
    const defaultIncoterms = getDefaultIncoterms(userCountry);

    setChemicals(prev => [...prev, {
      chemicalName: "",
      brand: "",
      product: "",
      casNumber: "",
      application: "",
      criticality:"medium",
      targetPrice: null,
      packagingType: "",
      qtyPerPackaging: null,
      qtyPerPackagingUnit: defaultUnit,
      targetPriceCurrency: defaultCurrency, // Set default currency based on user's country
      quantity: null,
      quantityUnit: defaultUnit, // Set default unit based on user's country
      category: selectedCategory, // Use the current selected category
      incoterms: defaultIncoterms,
      destination: "", // Added destination field
      destination_country: null,
      remarks: "",
      expectedprocurementVolume: null,
      expectedprocurementUnit: defaultUnit, // Set default unit based on user's country
      procurementVolume: null,
      procurementUnit: defaultUnit, // Set default unit based on user's country
      attachedFiles: [],
      offset_chemical_id: null,
      offset_chemical_name: null,
      type: null
    }]);
  }, [setChemicals, userCategory, userCountry, selectedCategory]);

  const handleSubmit = async (attachedFiles: File[]) => {

    console.log('Handling form submission with attached files:', attachedFiles);

    // Show validation errors if the form is not complete
    if (!isFormComplete()) {

      console.log('Form validation failed, showing validation errors');
      setShowValidationErrors(true);
      return;
    }

    const result = await submitEnquiry(attachedFiles);

    console.log('Enquiry submission result:', result);

    // If submission was successful, clear localStorage
    if (result && typeof result === 'object' && 'success' in result && result.success) {
      try {
        localStorage.removeItem('enquiry-chemicals');
        localStorage.removeItem('enquiry-customer');

        // Reset form state to defaults
        setChemicals([defaultChemicalData]);
        setCustomerData(defaultCustomerData);
      } catch (error) {
        console.error('Error clearing localStorage:', error);
      }
    }

    return result;
  };

  // Memoize the removeChemical function to prevent unnecessary re-renders
  const removeChemical = useCallback((index: number) => {
    setChemicals(prev => {
      // Don't remove if it's the last chemical
      if (prev.length === 1) return prev;
      return prev.filter((_, i) => i !== index);
    });
  }, [setChemicals]);

  // Handle category change
  const handleCategoryChange = useCallback((value: string) => {
    setSelectedCategory(value);  // Updates dropdown state
    // Update ALL chemicals with the new category
    setChemicals(prev => prev.map(chemical => ({
      ...chemical,
      category: value  // Sets category for EVERY chemical
    })));
  }, [setChemicals]);

  // Add a helper function to update files for a specific chemical
  // Memoize the updateChemicalFiles function to prevent unnecessary re-renders
  // Note: Files are only stored in memory and not saved to localStorage
const updateChemicalFiles = useCallback((files: File[], index: number, fileType?: string) => {
  console.log(files,"files",fileType,"fileType",index,"index");
  setChemicals(prev => {
    const updated = [...prev];
    if (fileType) {
      // If fileType is provided, organize files by type
      const currentFiles = updated[index].attachedFiles || [];
      
      // Create a new array with fileType property added to each file
      const filesWithType = files.map(file => {
        // Create a new object that preserves the File object and adds fileType
        const fileWithType = file as File & { fileType: string };
        // Add the fileType property directly to the file object
        Object.defineProperty(fileWithType, 'fileType', {
          value: fileType,
          writable: true,
          enumerable: true,
          configurable: true
        });
        return fileWithType;
      });
      
      // Remove existing files of this type and add new ones
      const filteredFiles = currentFiles.filter(f => (f as any).fileType !== fileType);
      updated[index] = { 
        ...updated[index], 
        attachedFiles: [...filteredFiles, ...filesWithType] 
      };
    } else {
      // Fallback to original behavior
      updated[index] = { ...updated[index], attachedFiles: files };
    }
    console.log("Updated chemicals:", updated);
    return updated;
  });
}, [setChemicals]);


  // Function to manually save form data to localStorage
  const saveFormToLocalStorage = useCallback(() => {
    try {
      // Create a copy of chemicals without the attachedFiles
      const chemicalsWithoutFiles = chemicals.map(chemical => {
        const { attachedFiles, ...chemicalWithoutFiles } = chemical;
        return chemicalWithoutFiles;
      });

      localStorage.setItem('enquiry-chemicals', JSON.stringify(chemicalsWithoutFiles));
      localStorage.setItem('enquiry-customer', JSON.stringify(customerData));
    } catch (error) {
      console.error('Error manually saving form data to localStorage:', error);
    }
  }, [chemicals, customerData]);

  // Add an effect to periodically save form data to localStorage
  useEffect(() => {
    const intervalId = setInterval(saveFormToLocalStorage, 10000); // Save every 10 seconds
    return () => clearInterval(intervalId);
  }, [saveFormToLocalStorage]);

  // Function to clear the form and localStorage
  const clearForm = useCallback(() => {
    // Clear localStorage
    try {
      localStorage.removeItem('enquiry-chemicals');
      localStorage.removeItem('enquiry-customer');

      // Reset form state to defaults
      setChemicals([defaultChemicalData]);
      setCustomerData(defaultCustomerData);
      setUseChemicalName(true);
      setShowValidationErrors(false);
    } catch (error) {
      console.error('Error clearing form data:', error);
    }
  }, [defaultChemicalData, defaultCustomerData, setChemicals, setCustomerData, setUseChemicalName, setShowValidationErrors]);

  return {
    chemicals,
    customerData,
    isSubmitting,
    useChemicalName,
    showSuccessDialog,
    setShowSuccessDialog,
    setUseChemicalName,
    updateChemicalData,
    updateCustomerData,
    isFormComplete,
    handleSubmit,
    selectedCustomerId,
    setSelectedCustomerId,
    addNewChemical,
    removeChemical,
    updateChemicalFiles,
    showValidationErrors,
    setShowValidationErrors,
    clearForm,
    saveFormToLocalStorage,
    setSelectedSalesPOC,
    selectedSalesPOC,
    salesTeamMembers,
    setSalesTeamMembers,
    selectedCategory,
    handleCategoryChange,
    categoryOptions
  };
};

export type { ChemicalData, CustomerData };
