
import { ChemicalData, CustomerData } from "../types/formTypes";

export const useEnquiryValidation = (chemicals: ChemicalData[], customer: CustomerData) => {
  const validateForm = () => {
    // Customer name is now optional
    const requiredCustomerFields = {

    };

    // Validate customer data
    console.log('Validating customer data:', customer);
    console.log('Required customer fields:', requiredCustomerFields);

    const isValidCustomerField = (key: string, value: unknown) => {
      if (Array.isArray(value)) {
        const isValid = value.length > 0;
        console.log(`Validating customer ${key} (array):`, value, isValid);
        return isValid;
      }
      const isValid = value !== undefined && value !== null && value !== "" && value !== 0;
      console.log(`Validating customer ${key}:`, value, isValid);
      return isValid;
    };

    const isCustomerValid = Object.entries(requiredCustomerFields)
      .every(([key, value]) => isValidCustomerField(key, value));

    console.log('Overall customer validation result:', isCustomerValid);

    // Validate each chemical
    const isChemicalValid = (chemical: ChemicalData) => {
      console.log('Validating chemical:', chemical);

      const requiredChemicalFields = {
        quantity: chemical.quantity,
        quantityUnit: chemical.quantityUnit,
        // category is now optional/removed
        incoterms: chemical.incoterms,
        destination: chemical.destination, // Added destination as mandatory
      };

      console.log('Required fields:', requiredChemicalFields);

      const hasChemicalName = chemical.chemicalName !== undefined && chemical.chemicalName !== "";
      const hasBrandAndProduct =
        chemical.brand !== undefined && chemical.brand !== "" &&
        chemical.product !== undefined && chemical.product !== "";

      console.log('Has chemical name:', hasChemicalName);
      console.log('Has brand and product:', hasBrandAndProduct);

      const isValidChemicalField = (key: string, value: unknown) => {
        // Allow 0 as a valid value for quantity
        if (key === 'quantity') {
          const isValid = value !== undefined && value !== null && value !== "";
          console.log(`Validating ${key}:`, value, isValid);
          return isValid;
        }
        // For other fields, maintain the original validation
        const isValid = value !== undefined && value !== null && value !== "" && value !== 0;
        console.log(`Validating ${key}:`, value, isValid);
        return isValid;
      };

      const fieldsValid = Object.entries(requiredChemicalFields)
        .every(([key, value]) => isValidChemicalField(key, value));

      console.log('All required fields valid:', fieldsValid);

      const nameValid = (hasChemicalName || hasBrandAndProduct);
      console.log('Name validation valid:', nameValid);

      const isValid = fieldsValid && nameValid;
      console.log('Overall chemical validation result:', isValid);

      return isValid;
    };

    // Log validation results for debugging
    console.log('Customer validation result:', isCustomerValid);
    console.log('Required customer fields:', requiredCustomerFields);

    const chemicalsValid = chemicals.every((chemical, index) => {
      const isValid = isChemicalValid(chemical);
      console.log(`Chemical ${index} validation result:`, isValid, chemical);
      return isValid;
    });

    // Set criticality to Low if customer name is not provided
    if (!customer.customerFullName && customer.criticality === 'medium') {
      // Update criticality to low
      customer.criticality = 'low';
    }

    // Validate both customer data and chemicals when submitting the form
    const formValid =  chemicalsValid;
    console.log('Overall form validation result:', formValid, isCustomerValid, chemicalsValid);

    return formValid;
  };

  // For backward compatibility
  const isFormComplete = validateForm;

  return { isFormComplete, validateForm };
};
