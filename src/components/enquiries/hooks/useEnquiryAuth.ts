
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { setCrmEnabledEverywhere } from "@/lib/utils";

export const useEnquiryAuth = (
  onAuthChange: (salesTeamMember: string, userId?: string) => void
) => {
  const navigate = useNavigate();

  useEffect(() => {
    console.log('useEnquiryAuth effect running');
    let isMounted = true;

    const checkAuth = async () => {
      if (!isMounted) return;

      console.log('Checking auth status');
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        toast.error("Please sign in to create enquiries");
        navigate("/auth");
        return;
      }

      // Check if user data already exists in localStorage
      const existingCategory = localStorage.getItem('userCategory');
      const existingCountry = localStorage.getItem('userCountry');

      if (existingCategory && existingCountry) {
        console.log('User data already in localStorage, skipping fetch');
        // Call the callback with the salesTeamMember and userId
        onAuthChange(session.user.email || "", session.user.id);
        return;
      }

      // Fetch user's category and country from user_roles table
      const userId = session.user.id;
      console.log('Fetching user data for userId:', userId);

      try {
        const { data: userData, error: userError } = await supabase
          .from('user_roles')
          .select('category, country,role,is_crm_enabled')
          .eq('user_id', userId)
          .single();

        if (!isMounted) return;

        if (userError) {
          console.error('Error fetching user data:', userError);
        } else if (userData) {
          // Store user data in localStorage
          console.log('Fetched user data:', userData);
          localStorage.setItem('userCategory', userData.category || '');
          localStorage.setItem('userCountry', userData.country || '');
          localStorage.setItem('userRole', userData.role || '');
          localStorage.setItem('userId', userId);
          localStorage.setItem('userEmail', session.user.email || '');
          localStorage.setItem('crmEnabled', String(userData.is_crm_enabled));
          setCrmEnabledEverywhere(String(userData.is_crm_enabled));
          console.log('Stored user data in localStorage:', {
            category: userData.category,
            country: userData.country,
            role: userData.role
          });
        }
      } catch (error) {
        if (!isMounted) return;
        console.error('Error in checkAuth:', error);
      }

      // Call the callback with the salesTeamMember and userId
      if (isMounted) {
        onAuthChange(session.user.email || "", session.user.id);
      }
    };

    checkAuth();

    // Use a non-async callback for onAuthStateChange to avoid deadlocks
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === "SIGNED_OUT") {
        // Clear user data from localStorage on sign out
        localStorage.removeItem('userCategory');
        localStorage.removeItem('userCountry');
        localStorage.removeItem('userRole');
        localStorage.removeItem('userId');
        localStorage.removeItem('crmEnabled');
        localStorage.removeItem('userEmail');
        navigate("/auth");
      } else if (session) {
        // Instead of making Supabase calls inside the callback,
        // schedule them to run after the callback completes
        setTimeout(() => {
          const userId = session.user.id;
          // Fetch user data outside the callback using a separate function
          const fetchUserData = async () => {
            try {
              const { data: userData, error: userError } = await supabase
                .from('user_roles')
                .select('category, country')
                .eq('user_id', userId)
                .single();

              if (userError) {
                console.error('Error fetching user data on auth change:', userError);
              } else if (userData) {
                // Store user data in localStorage
                localStorage.setItem('userCategory', userData.category || '');
                localStorage.setItem('userCountry', userData.country || '');
                console.log('Updated user data in localStorage on auth change:', {
                  category: userData.category,
                  country: userData.country
                });
              }
            } catch (error) {
              console.error('Error in auth state change handler:', error);
            }
          };

          // Execute the function
          fetchUserData();
        }, 0);

        // Call the callback with the salesTeamMember and userId
        onAuthChange(session.user.email || "", session.user.id);
      }
    });

    return () => {
      console.log('useEnquiryAuth effect cleanup');
      isMounted = false;
      subscription.unsubscribe();
    };
  }, [navigate, onAuthChange]);
};
