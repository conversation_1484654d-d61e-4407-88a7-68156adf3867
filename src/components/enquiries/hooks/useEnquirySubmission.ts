import { useState } from "react";
import { supabase, STORAGE_BUCKETS } from "@/integrations/supabase/client";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { FormData, ChemicalData, CustomerData } from "../types/formTypes";
import { Database } from "@/integrations/supabase/types";
import { countryCodeToName } from "@/utils/destinations";

type EnquiryLifecycleStatus =
  Database["public"]["Enums"]["enquiry_lifecycle_status"];
type UnitType = Database["public"]["Enums"]["unit_type"];

const initialChemicalState: ChemicalData = {
  chemicalName: "",
  brand: "",
  product: "",
  casNumber: "",
  destination: "",
  destination_country: null,
  application: "",
  targetPrice: null,
  packagingType: "",
  qtyPerPackaging: null,
  qtyPerPackagingUnit: null,
  targetPriceCurrency: "",
  quantity: null,
  quantityUnit: null,
  category: "",
  incoterms: "",
  remarks: "",
  expectedprocurementVolume: null,
  expectedprocurementUnit: null,
  procurementVolume: null,
  procurementUnit: null,
  attachedFiles: [],
  criticality: "medium",
  offset_chemical_id: null,
  offset_chemical_name: null,
  type: null,
};

const initialCustomerState: CustomerData = {
  city: "",
  country: "",
  industries: [],
  salesTeamMember: "",
  customerFullName: "",
  customerPoc: "",
  customerPhone: "",
  customerEmail: "",
};

export const useEnquirySubmission = (
  chemicals: ChemicalData[], // Changed to accept array of chemicals
  customerData: CustomerData,
  setChemicals: React.Dispatch<React.SetStateAction<ChemicalData[]>>,
  setCustomerData: React.Dispatch<React.SetStateAction<CustomerData>>,
  isFormComplete: () => boolean,
  setShowSuccessDialog: React.Dispatch<React.SetStateAction<boolean>>,
  selectedCustomerId: string | null,
  selectedSalesPOC: string, // Add selectedSalesPOC as an argument
  salesTeamMembers: { value: string; label: string; category: string }[] // Add salesTeamMembers as an argument
) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  const selectedMember =
  selectedSalesPOC !== "Me"
    ? salesTeamMembers.find((member) => member.value === selectedSalesPOC)
    : null;

  const uploadDocuments = async (files: File[], enquiryId: string) => {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) return;

    console.log("Uploading documents for enquiry ID:", enquiryId);

    for (const file of files) {
      const fileExt = file?.name?.split(".").pop();
      const fileType = (file as any)?.fileType || 'others'; // Get file type
      const filePath = `${enquiryId}/${crypto.randomUUID()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from(STORAGE_BUCKETS.ENQUIRY_DOCUMENTS)
        .upload(filePath, file);

      if (uploadError) {
        console.error("Error uploading file:", uploadError);
        toast.error(`Failed to upload ${file.name}`);
        continue;
      }

      const { error: metadataError } = await supabase
        .from("enquiry_documents")
        .insert({
          enquiry_id: enquiryId,
          file_name: file.name,
          file_path: filePath,
          content_type: file.type,
          size: file.size,
          uploaded_by: session.user.id,
          document_type: fileType
        });

      if (metadataError) {
        console.error("Error saving file metadata:", metadataError);
        toast.error(`Failed to save metadata for ${file.name}`);
      }
    }
  };

  const handleSubmit = async (files: File[] = []) => {
    console.log("useEnquirySubmission: handleSubmit called with files:", files);

    // Check authentication
    console.log("Checking authentication session...");
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      console.error("No active session found");
      toast.error("Please sign in to create enquiries");
      navigate("/auth");
      return { success: false, error: "Authentication required" };
    }

    console.log("User authenticated:", session.user.id);
    console.log("Chemicals data in submission:", chemicals);

    // Validate form
    if (!isFormComplete()) {
      console.log("Form validation failed in submission handler");
      toast.error(
        "Please fill in all required fields before creating the enquiry"
      );
      return { success: false, error: "Form validation failed" };
    }

    console.log("Form validation passed, proceeding with submission");

    setIsSubmitting(true);

    try {
      // Handle customer creation/update only if customer name is provided
      let customerResult = null;

      console.log("Customer data:", customerData);
      console.log("Selected customer ID:", selectedCustomerId);

      if (
        customerData.customerFullName &&
        customerData.customerFullName.trim() !== ""
      ) {
        console.log("Customer name provided, processing customer data");

        try {
          // Define customer data structure for DB
          // The customer table expects 'name' instead of 'customer_full_name'

          const { data, error } = await supabase
            .from("customer")
            .select("id,customer_full_name")
            .ilike("customer_full_name", `%${customerData.customerFullName}%`)
            .order("customer_full_name", { ascending: true })
            .limit(1);

          if (error) {
            console.error("Error fetching customers:", error);
            return [];
          }

          const customerDataForDb = {
            customer_full_name: customerData.customerFullName, // This is the required field in the customer table
            customer_poc: customerData.customerPoc,
            customer_phone: customerData.customerPhone,
            customer_email: customerData.customerEmail,
            city: customerData.city,
            country: customerData.country,
            industries: customerData.industries,
          };

          console.log("Customer data for DB:", customerDataForDb);

          // Handle customer creation/update once
          if (selectedCustomerId || data.length > 0) {
            console.log(
              "Updating existing customer with ID:",
              selectedCustomerId
            );
            let newCustomerId = selectedCustomerId;
            if (data.length > 0) {
              newCustomerId = data[0]?.id;
            }

            try {
              // const { data: updatedCustomer, error: updateError } =
              //   await supabase
              //     .from("customer")
              //     .update({
              //       ...customerDataForDb,
              //       modified_at: new Date().toISOString(),
              //       modified_by: session.user.id,
              //     })
              //     .eq("id", newCustomerId)
              //     .select()
              //     .single();

              // console.log("Customer update result:", {
              //   updatedCustomer,
              //   updateError,
              // });

              // if (updateError) {
              //   console.error("Error updating customer:", updateError);
              //   throw updateError;
              // }

              customerResult = data[0];
            } catch (updateError) {
              console.error("Exception updating customer:", updateError);
              throw updateError;
            }
          } else {
            console.log("Creating new customer");
            try {
              const { data: newCustomer, error: insertError } = await supabase
                .from("customer")
                .insert({
                  ...customerDataForDb,
                  created_at: new Date().toISOString(),
                  created_by: session.user.id,
                  account_owner: (selectedSalesPOC && session.user.id !== selectedSalesPOC )
                  ? selectedMember?.label
                  : session.user.email,
                })
                .select()
                .single();

              console.log("Customer creation result:", {
                newCustomer,
                insertError,
              });

              if (insertError) {
                console.error("Error creating customer:", insertError);
                throw insertError;
              }

              customerResult = newCustomer;
            } catch (insertError) {
              console.error("Exception creating customer:", insertError);
              throw insertError;
            }
          }

          if (!customerResult) {
            console.error("No customer result after operation");
            throw new Error("Failed to create or update customer");
          }

          console.log("Customer processed successfully:", customerResult);
        } catch (customerError) {
          console.error("Error in customer processing:", customerError);
          throw customerError;
        }
      } else {
        console.log(
          "No customer name provided, skipping customer creation/update"
        );
      }

      // Create enquiries for each chemical
      console.log("Creating enquiries for chemicals:", chemicals.length);

      for (let i = 0; i < chemicals.length; i++) {
        const chemical = chemicals[i];
        console.log(
          `Processing chemical ${i + 1}/${chemicals.length}:`,
          chemical.chemicalName
        );

        try {
          // Base enquiry data without customer information
          const enquiryData: any = {
            chemical_name: chemical.chemicalName || null,
            offset_chemical_id: chemical.offset_chemical_id || null,
            offset_chemical_name: chemical.offset_chemical_name || null,
            brand: chemical.brand || null,
            product: chemical.product || null,
            cas_number: chemical.casNumber || null,
            destination: chemical.destination || null,
            destination_country: chemical.destination_country ? countryCodeToName[chemical.destination_country as keyof typeof countryCodeToName] || null : null,
            application: chemical.application || null,
            target_price: chemical.targetPrice || null,
            packaging_type: chemical.packagingType || null,
            qty_per_packaging: chemical.qtyPerPackaging || null,
            qty_per_packaging_unit: chemical.qtyPerPackagingUnit || null,
            target_price_currency: chemical.targetPriceCurrency || null,
            quantity:
              chemical.quantity !== undefined && chemical.quantity !== null
                ? chemical.quantity
                : 0,
            quantity_unit: (chemical.quantityUnit as UnitType) || null,
            sales_team_member: (selectedSalesPOC && session.user.id !== selectedSalesPOC )
              ? selectedMember?.label
              : session.user.email,
            sales_agent_id: (selectedSalesPOC && session.user.id !== selectedSalesPOC )
              ? selectedMember?.value
              : session.user.id,
            city: customerData.city,
            country: customerData.country,
            current_status: "enquiry_created" as EnquiryLifecycleStatus,
            confidence: chemical.criticality || "medium",
            category: chemical.category || null,
            incoterms: chemical.incoterms || null,
            remarks: chemical.remarks || null,
            procurement_volume: chemical.procurementVolume || null,
            procurement_unit: chemical.procurementUnit || null,
            expected_procurement_volume:
              chemical.expectedprocurementVolume || null,
            expected_procurement_unit: chemical.expectedprocurementUnit || null,
            is_new: true,
            enquiry_id: `ENQ-${crypto.randomUUID().substring(0, 5)}`,
            chemical_type: chemical?.type || "Others", // Optional field
          };

          console.log("Base enquiry data:", enquiryData);

          // Add customer information if available
          if (customerResult) {
            console.log("Adding customer ID to enquiry:", customerResult.id);
            enquiryData.customer_id = customerResult.id;
            enquiryData.customer_full_name = customerResult.customer_full_name; // Use the name from the customer record
          } else if (customerData.customerFullName) {
            console.log("Adding customer name to enquiry without ID");
            // If customer name is provided but no customer record was created
            // (this shouldn't happen normally, but just in case)
            enquiryData.customer_full_name = customerData.customerFullName;
          } else {
            console.log("No customer information available for this enquiry");
          }

          console.log("Final enquiry data to insert:", enquiryData);

          try {
            const { data: enquiryResult, error: insertError } = await supabase
              .from("enquiries")
              .insert(enquiryData)
              .select()
              .single();

            console.log("Enquiry creation result:", {
              enquiryResult,
              insertError,
            });

            if (insertError) {
              console.error("Error creating enquiry:", insertError);
              throw insertError;
            }

            console.log("Enquiry created successfully:", enquiryResult);
            console.log(
              "Checking for attached files:",
              chemical?.attachedFiles
            );
            if (chemical?.attachedFiles && chemical.attachedFiles?.length > 0) {
              console.log(
                `Uploading ${chemical.attachedFiles.length} documents for enquiry:`,
                enquiryResult.id
              );
              await uploadDocuments(chemical.attachedFiles, enquiryResult.id);
              console.log("Documents uploaded successfully");
            }
          } catch (insertError) {
            console.error("Exception creating enquiry:", insertError);
            throw insertError;
          }
        } catch (chemicalError) {
          console.error(`Error processing chemical ${i + 1}:`, chemicalError);
          throw chemicalError;
        }
      }

      // Reset the forms with complete cleanup
      console.log("Creating resetFormData function...");
      const resetFormData = () => {
        console.log("resetFormData called");
        try {
          console.log("Resetting React state...");
          // Reset React state
          setChemicals([initialChemicalState]);
          setCustomerData(initialCustomerState);
          console.log("React state reset");

          console.log("Clearing localStorage...");
          // Clear localStorage
          localStorage.removeItem("enquiry-chemicals");
          localStorage.removeItem("enquiry-customer");
          console.log("localStorage cleared");

          // Reset DOM elements
          console.log("Resetting DOM elements...");
          const form = document.querySelector("form");
          if (form) {
            form.reset();
            console.log("Form reset");
          } else {
            console.log("Form element not found");
          }

          // Reset all number inputs
          console.log("Resetting number inputs...");
          document.querySelectorAll('input[type="number"]').forEach((input) => {
            (input as HTMLInputElement).value = "";
          });
          console.log("Number inputs reset");

          setTimeout(() => {
            window.location.reload();
          }, 1000);

          console.log("Form data reset successfully");
        } catch (error) {
          console.error("Failed to reset form:", error);
          console.log("Falling back to page reload");
        }
      };

      console.log("All chemicals processed successfully");

      // Call the reset function after successful submission
      console.log("Calling resetFormData...");
      resetFormData();
      console.log("resetFormData completed");

      console.log("Setting showSuccessDialog to true");
      setShowSuccessDialog(true);

      // Return success result
      console.log("Returning success result");
      return { success: true };
    } catch (error) {
      console.error("Error:", error);
      toast.error("Failed to create enquiry. Please try again.");

      // Return failure result
      return { success: false, error };
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    isSubmitting,
    handleSubmit,
  };
};
