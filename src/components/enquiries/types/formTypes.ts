type QuantityUnit = "kg" | "g" | "lbs" | "pcs" | "Metric Ton (mt)" | "Pound (lb)" | "Gallon (gal)" | "Litre (L)" | "Kilolitre (Kl)" | "Kilogram (Kg)";

export interface ChemicalData {
  chemicalName: string;
  brand: string;
  product: string;
  casNumber: string;
  destination: string;
  destination_country: string | null;
  application: string;
  targetPrice: number | null;
  packagingType: string;
  qtyPerPackaging: number | null;
  qtyPerPackagingUnit: string | null;
  targetPriceCurrency: string;
  quantity: number | null;
  quantityUnit: string | null;
  category: string;
  incoterms: string;
  remarks: string;
  expectedprocurementVolume: number | null;
  expectedprocurementUnit: string | null;
  procurementVolume: number | null;
  procurementUnit: string | null;
  attachedFiles: FileWithType[];
  criticality: "high" | "medium" | "low";
  offset_chemical_id: string | null;
  offset_chemical_name: string | null;
  type?: string | null; 
}

// Customer/Business related data
export interface CustomerData {
  city: string;
  country: string;
  industries: string[];
  salesTeamMember: string;
  customerFullName?: string;
  customerPoc?: string;
  customerPhone: string;
  customerEmail: string;
}

// Form data with chemicals array
export interface FormData {
  chemicals: ChemicalData[];
  customer: CustomerData;
}

interface FileWithType extends File {
  fileType?: string;
}


export type { QuantityUnit };
