import { ReactNode, useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  <PERSON>tings,
  ClipboardList,
  Menu,
  X,
  FileText,
  FilePlus,
  HelpCircle,
  LogOut,
  Mail,
  LayoutDashboard,
  Package,
  ShoppingBag,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { useLocation, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/use-toast";
import SalesStack from "@/pages/SalesStack";
import CreatePurchaseOrderModal from "@/components/stats/enquiries/components/CreatePurchaseOrderModal";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import PostHogTrackerOnPageView from "@/components/posthog/PostHogTrackerOnPageView"
import { usePostHog } from "posthog-js/react";

type SidebarItemProps = {
  icon: ReactNode;
  label: string;
  active?: boolean;
  onClick: () => void;
};

const salesStackTitleTobeIncludedItems = [
  "Enquiries (SalesStack)",
  "landing-page",
];

const salesStackTitleTobeExcludedRoutes = [
  // Remove "/meetings/edit" from excluded routes
];

const SidebarItem = ({ icon, label, active, onClick }: SidebarItemProps) => (
  <button
    className={cn(
      "flex items-center gap-2 w-full px-3 py-2 text-sm font-medium rounded-md transition-colors",
      active
        ? "bg-sidebar-primary text-sidebar-primary-foreground"
        : "text-sidebar-foreground hover:bg-sidebar-accent"
    )}
    onClick={onClick}
  >
    <span className="w-5 h-5">{icon}</span>
    <span>{label}</span>
  </button>
);

type SidebarProps = {
  currentTab: string;
  onTabChange: (tab: string) => void;
  expanded: boolean;
  onToggleExpand: () => void;
};

const Sidebar = ({
  currentTab,
  onTabChange,
  expanded,
  onToggleExpand,
}: SidebarProps) => {
  const isMobile = useIsMobile();
  const [crmEnabled, setCrmEnabled] = useState(() =>
    localStorage.getItem("crmEnabled")
  );
  const navigate = useNavigate();

  const userName = localStorage.getItem("userEmail");
  const userRole = localStorage.getItem("userRole");
  const posthog = usePostHog();


useEffect(() => {
  const handleStorage = () => {
    setCrmEnabled(localStorage.getItem("crmEnabled"));
  };
  window.addEventListener("storage", handleStorage);
  window.addEventListener("crmEnabledChanged", handleStorage); // Listen for custom event
  handleStorage();
  return () => {
    window.removeEventListener("storage", handleStorage);
    window.removeEventListener("crmEnabledChanged", handleStorage);
  };
}, []);

  const handleSignOut = async (title = "") => {
    try {
      const { error } = await supabase.auth.signOut();
      localStorage.clear()
         if (posthog && userName) {
           posthog.capture("user_logout", {
             timestamp: new Date().toISOString(),
             email: userName,
           });
         }
      navigate("/auth");
      if (error) throw error;
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to sign out. Please try again.",
      });
    }
  };

  const tabs = [
    {
      id: "salesstack",
      label: "Enquiries (SalesStack)",
      icon: <ClipboardList size={16} />,
    },
    { id: "customers", label: "Customers", icon: <Users size={16} /> },
    { id: "snapshot", label: "Current Snapshot", icon: <BarChart size={16} /> },
    { id: "meetings", label: "Meetings", icon: <Calendar size={16} /> },
    { id: "metabase", label: "Metabase", icon: <Calendar size={16} /> },
    // { id: "dashboard", label: "Reports", icon: <PieChart size={16} /> },
    ...(userRole === "admin"
      ? [
          {
            id: "user-settings",
            label: "Access Management",
            icon: <Settings size={16} />,
          },
        ]
      : []),
  ];

  const userRoleMap = {
    admin: "Admin",
    sales: "Sales",
    bu_head: "BU Head",
  };

  if(crmEnabled !== "true") {
    return null;
  }

  return (
    <div
      className={cn(
        "h-full z-30 bg-sidebar flex flex-col transition-all duration-300",
        expanded ? "w-56" : "w-0",
        isMobile && !expanded && "-left-12"
      )}
    >
      <div className="flex items-center justify-between h-16 px-2 border-b border-sidebar-border">
        <h1
          className={cn(
            "font-bold text-base text-sidebar-foreground cursor-pointer",
            !expanded && "sr-only"
          )}
          onClick={()=> navigate("/")}
        >
          Mstack CRM
        </h1>
        <button
          onClick={onToggleExpand}
          className="p-1 text-sidebar-foreground hover:bg-sidebar-accent rounded-md"
        >
          {expanded ? <X size={16} /> : <Menu size={16} />}
        </button>
      </div>

      <div className="flex-1 py-2 space-y-0.5 overflow-y-auto">
        <Tabs value={currentTab} onValueChange={onTabChange}>
          <TabsList className="flex flex-col h-auto bg-transparent p-0 gap-2">
            <TabsTrigger
              value="salesstack"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm w-full justify-start px-3 py-2"
            >
              <ClipboardList className="mr-2 h-4 w-4" />
              Enquiries (SalesStack)
            </TabsTrigger>
            <TabsTrigger
              value="customers"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm w-full justify-start px-3 py-2"
            >
              <Users className="mr-2 h-4 w-4" />
              Customers
            </TabsTrigger>
            <TabsTrigger
              value="snapshot"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm w-full justify-start px-3 py-2"
            >
              <BarChart className="mr-2 h-4 w-4" />
              Current Snapshot
            </TabsTrigger>
            <TabsTrigger
              value="meetings"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm w-full justify-start px-3 py-2"
            >
              <Calendar className="mr-2 h-4 w-4" />
              Meetings
            </TabsTrigger>
            <TabsTrigger
              value="metabase"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm w-full justify-start px-3 py-2"
            >
              <PieChart className="mr-2 h-4 w-4" />
              Reports
            </TabsTrigger>
            {userRole === "admin" && (
              <TabsTrigger
                value="user-settings"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm w-full justify-start px-3 py-2"
              >
                <Settings className="mr-2 h-4 w-4" />
                Access Management
              </TabsTrigger>
            )}
          </TabsList>
        </Tabs>
      </div>

      <div className="p-2 border-t border-sidebar-border">
        <div
          className={cn(
            "flex items-center gap-2",
            !expanded && "justify-center"
          )}
        >
          {/* <div className="w-6 h-6 rounded-full bg-sidebar-primary flex items-center justify-center text-white text-xs">
            U
          </div> */}
          {expanded && (
            <div className="text-xs">
              <div className="font-medium">{userName}</div>
              <div className="text-xs text-muted-foreground">
                {userRoleMap[userRole]}
              </div>
              <div
                // variant="ghost"
                className="justify-start flex items-center gap-2 p-2 text-gray-500 hover:text-gray-600 hover:bg-gray-50 cursor-pointer"
                onClick={() => handleSignOut(currentTab)}
              >
                <LogOut className="h-4 w-4" />
                Sign Out
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

type HeaderProps = {
  title: string;
  expanded: boolean;
  onToggleExpand: () => void;
};

const Header = ({ title, expanded, onToggleExpand }: HeaderProps) => {
  const isMobile = useIsMobile();

  const location = useLocation();
  const navigate = useNavigate();
  const isAuthPage = location.pathname === "/auth";
  const isHelpPage = location.pathname === "/help";
  const pathname = location.pathname;
  const userName = localStorage.getItem("userEmail");
  const posthog = usePostHog();


  const isExcludedRoute = (currentPath: string): boolean => {
    const salesStackTitleTobeExcludedRoutes = ["/meetings/edit"];

    // Check if the current path starts with any of the excluded routes
    return salesStackTitleTobeExcludedRoutes.some((route) =>
      currentPath.startsWith(route)
    );
  };

  const { data: session } = useQuery({
    queryKey: ["auth-session"],
    queryFn: async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      return session;
    },
  });

  const handleSignOut = async (title = "") => {
    try {
      const { error } = await supabase.auth.signOut();
      localStorage.clear()
      navigate("/auth");
       if (posthog && userName) {
         posthog.capture("user_logout", {
           timestamp: new Date().toISOString(),
           email: userName,
         });
       }
      if (error) throw error;
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to sign out. Please try again.",
      });
    }
  };

  const scrollToEnquiryWidget = () => {
    const enquiryWidget = document.querySelector("[data-enquiry-widget]");
    if (enquiryWidget) {
      enquiryWidget.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  const [showPOModal, setShowPOModal] = useState(false);

  const purchaseOrderClick = () => {
    setShowPOModal(true);
  };

  const goToHelpCenter = () => {
    navigate("/help"); // Assuming '/help' is your help section route
    // Close the sheet/burger menu if needed
  };

  const menuContent = (
    <nav className="flex flex-col gap-4">
      <div className="px-2 py-4">
        <img
          src="/lovable-uploads/ad13b7d1-ebd6-43a9-a6e1-aa5027505965.png"
          alt="Logo"
          className="h-8 w-auto object-contain"
        />
      </div>

      {/* Menu Items */}
      <div className="flex flex-col space-y-3">
        <Button
          variant="ghost"
          className="justify-start"
          onClick={() => {
            // Add navigation logic
          }}
        >
          <FilePlus className="mr-2 h-5 w-5" />
          Create Enquiry
        </Button>

        <Button
          variant="ghost"
          className="justify-start"
          onClick={purchaseOrderClick}
        >
          <FilePlus className="mr-2 h-5 w-5" />
          Upload Purchase Order
        </Button>

        <Button
          variant="ghost"
          className="justify-start"
          onClick={goToHelpCenter}
        >
          <HelpCircle className="mr-2 h-5 w-5" />
          Help Center
        </Button>

        {session?.user?.email && (
          <Button variant="ghost" className="justify-start">
            <Mail className="mr-2 h-5 w-5" />
            {session.user.email}
          </Button>
        )}

        <Button
          variant="ghost"
          className="justify-start text-red-500 hover:text-red-600 hover:bg-red-50"
          onClick={() => handleSignOut()}
        >
          <LogOut className="mr-2 h-5 w-5" />
          Sign Out
        </Button>
      </div>
    </nav>
  );

  return (
    <>
      {title ? (
        <header className="h-16 border-b flex items-center justify-between px-3">
          {isMobile && (
            <button
              onClick={onToggleExpand}
              className="p-1 text-muted-foreground hover:bg-accent/10 rounded-md mr-2"
            >
              <Menu size={16} />
            </button>
          )}
          <div className="flex items-center gap-3">
            <img
              src="/lovable-uploads/ad13b7d1-ebd6-43a9-a6e1-aa5027505965.png"
              alt="Logo"
              className="h-8 w-auto object-contain animate-fade-in"
            />
            {/* <div>
              <h1 className="text-xl font-semibold text-gradient">
                Sales-Stack
              </h1>
            </div> */}
          </div>
          {/* <h1 className="text-lg font-semibold">{title}</h1> */}
          {!isAuthPage && session && (
            <div className="hidden md:flex items-center gap-4">
            {title === "Enquiries (SalesStack)" && (
              <Button
                onClick={purchaseOrderClick}
                variant="default"
                className="flex items-center gap-2 bg-[#294d48] hover:bg-[#294d48]/90 text-white shadow-md animate-fade-in"
              >
                <FilePlus className="h-5 w-5" />
                Upload Purchase Order
              </Button>
            )}
              <Button
                variant="outline"
                className="hidden sm:flex items-center gap-2 border-[#294d48]/20 text-[#294d48] hover:bg-[#294d48]/10 transition-all duration-300"
              >
                <Mail className="h-5 w-5" />
                {session.user?.email}
              </Button>
             {title === "Enquiries (SalesStack)" && <Button
                variant="ghost"
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-all duration-300"
                onClick={goToHelpCenter}
              >
                <HelpCircle className="h-5 w-5" />
                Help
              </Button>}
              <Button
                variant="ghost"
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-all duration-300"
                onClick={() => handleSignOut(title)}
              >
                <LogOut className="h-5 w-5" />
                Sign Out
              </Button>
            </div>
          )}
          <div className="flex items-center gap-3">
            <button className="p-1 rounded-full hover:bg-accent/10">
              <Calendar size={16} />
            </button>
            <button className="p-1 rounded-full hover:bg-accent/10">
              <span className="relative">
                <BarChart size={16} />
                <span className="absolute -top-1 -right-1 w-1.5 h-1.5 bg-primary rounded-full"></span>
              </span>
            </button>
          </div>
        </header>
      ) : (
        <header className="h-16 border-b flex items-center justify-between px-3">
          {isMobile && (
            <button
              onClick={onToggleExpand}
              className="p-1 text-muted-foreground hover:bg-accent/10 rounded-md mr-2"
            >
              <Menu size={16} />
            </button>
          )}
          <h1 className="text-lg font-semibold">{title}</h1>
          <div className="flex items-center gap-3">
            <button className="p-1 rounded-full hover:bg-accent/10">
              <Calendar size={16} />
            </button>
            <button className="p-1 rounded-full hover:bg-accent/10">
              <span className="relative">
                <BarChart size={16} />
                <span className="absolute -top-1 -right-1 w-1.5 h-1.5 bg-primary rounded-full"></span>
              </span>
            </button>
          </div>
        </header>
      )}
      <CreatePurchaseOrderModal
        open={showPOModal}
        onClose={() => setShowPOModal(false)}
        onSuccess={() => {
          setShowPOModal(false);
          toast({
            title: "Success",
            description: "Purchase order created successfully",
          });
        }}
      />
    </>
  );
};

type SidebarLayoutProps = {
  children: (tab: string) => ReactNode;
};

export const SidebarLayout = ({ children }: SidebarLayoutProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [currentTab, setCurrentTab] = useState(() => {
    if (location.pathname==="/") {
      return "landing-page";
    }
    if (location.pathname === "/crm") {
      return location.state?.activeTab || "salesstack";
    }
    return "salesstack";
  });
  const [expanded, setExpanded] = useState(location.state?.keepOpen || false);
  const isMobile = useIsMobile();

  useEffect(() => {
    if (location.pathname === "/meetings") {
      setCurrentTab("meetings");
    } else if (location.pathname === "/crm" && location.state?.activeTab) {
      setCurrentTab(location.state.activeTab);
    } else if (location.pathname === "/crm") {
      setCurrentTab("salesstack");
    }
  }, [location.pathname, location.state]);

  const handleTabChange = (tab: string) => {
    setCurrentTab(tab);

    const currentState = {
      selectedCustomer: null,
      selectedCustomerName: "",
      activeTab: tab,
      keepOpen: true,
    };

    navigate("/crm", { state: currentState });
    if (isMobile) {
      setExpanded(false);
    }
  };

  const tabTitles: Record<string, string> = {
    salesstack: "Enquiries (SalesStack)",
    customers: "Customers",
    "customer-detail": "Customer Detail",
    snapshot: "Current Snapshot",
    meetings: "Meetings",
    dashboard: "Reports",
    "user-settings": "Access Management",
    metabase: "DashBoard",
    "landing-page": "landing-page",
  };

  return (
    <div className="flex h-screen w-full bg-background">
      <div className="h-full">
        <PostHogTrackerOnPageView currentTab={currentTab} />
        <Sidebar
          currentTab={currentTab}
          onTabChange={handleTabChange}
          expanded={expanded}
          onToggleExpand={() => setExpanded(!expanded)}
        />
      </div>
      <div
        className={cn(
          "flex-1 flex flex-col transition-all duration-300",
          expanded ? (isMobile ? "ml-0" : "ml-0") : "ml-12",
          isMobile && !expanded && "ml-0"
        )}
      >
        <Header
          title={tabTitles[currentTab] || "Unknown Tab"}
          expanded={expanded}
          onToggleExpand={() => setExpanded(!expanded)}
        />

        <main
          className={`flex-1 overflow-auto ${
            currentTab === "landing-page" ? "" : "p-3"
          }`}
        >
          {children(currentTab)}
        </main>
      </div>
    </div>
  );
};
