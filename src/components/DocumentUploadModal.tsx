import React from "react";
import CompactDocumentUpload from "./enquiries/components/CompactDocumentUpload";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Upload, File, X } from "lucide-react";

const DOCUMENT_TYPES = [
  { key: "tds", label: "TDS" },
  { key: "sds", label: "SDS" },
  { key: "coa", label: "COA" },
  { key: "moa", label: "MOA" },
  { key: "others", label: "Others" },
];

interface DocumentUploadModalProps {
  visible: boolean;
  onClose: () => void;
  filesByType: {
    tds: File[];
    sds: File[];
    coa: File[];
    moa: File[];
    others: File[];
  };
  onFilesChange: (type: string, files: File[]) => void;
}

const DocumentUploadModal = ({
  visible,
  onClose,
  filesByType,
  onFilesChange,
}: DocumentUploadModalProps) => (
  <Dialog open={visible} onOpenChange={onClose}>
    <DialogContent
      className="sm:max-w-md bg-[#f8fafc] border-2 border-[#E6EAE9] rounded-2xl shadow-lg max-h-[72vh] flex flex-col p-0"
      style={{ minWidth: 350 }}
    >
      {/* Fixed Header */}
      <DialogHeader className="p-3 relative">
        <DialogTitle className=" text-[#294d48] text-xl font-semibold ">
          Upload Documents
        </DialogTitle>
      </DialogHeader>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto px-4 py-2 space-y-4">
        {DOCUMENT_TYPES.map((type) => (
          <div
            key={type.key}
            className="bg-white rounded-xl p-3 border border-gray-100 shadow-sm hover:shadow transition-all"
          >
            <div className="font-medium text-sm text-[#294d48] mb-2 uppercase tracking-wide">
              {type.label}
            </div>
            <div className="overflow-hidden max-w-[100vw]">
              <CompactDocumentUpload
                files={filesByType[type.key] || []}
                onFilesChange={(files) => onFilesChange(type.key, files)}
                className="w-full text-xs [&_.file-name]:truncate [&_.file-name]:max-w-[120px] [&_.file-name]:overflow-hidden [&_.file-name]:whitespace-nowrap [&_.file-name]:text-ellipsis [&_*]:text-xs [&_button]:py-1 [&_button]:px-2 [&_button]:text-xs [&_input]:h-7 [&_input]:py-1 [&_input]:px-2 [&_input]:text-xs"
              />
            </div>
          </div>
        ))}
      </div>
      {/* Fixed Footer */}
      <div className="flex justify-end px-6 py-4 border-t border-gray-200 bg-[#f8fafc] flex-shrink-0 z-10">
        <Button
          onClick={onClose}
          className="bg-[#294d48] hover:bg-[#294d48]/90 text-white px-6 py-2 rounded-md text-sm font-medium transition-colors"
        >
          Done
        </Button>
      </div>
    </DialogContent>
  </Dialog>
);

export default DocumentUploadModal;
