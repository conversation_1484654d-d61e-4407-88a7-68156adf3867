import React from 'react';
import { Select as BaseSelect, SelectContent as BaseSelectContent, SelectItem as BaseSelectItem, SelectTrigger as BaseSelectTrigger, SelectValue as BaseSelectValue } from "@/components/ui/select";

interface LabeledSelectItemProps {
  value: string;
  label: string;
  children: React.ReactNode;
}

export const LabeledSelectItem: React.FC<LabeledSelectItemProps> = ({ value, label, children }) => {
  return (
    <BaseSelectItem value={value} data-label={label}>
      {children}
    </BaseSelectItem>
  );
};

interface LabeledSelectProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  children: React.ReactNode;
  triggerClassName?: string;
}

export const LabeledSelect: React.FC<LabeledSelectProps> = ({
  value,
  onValueChange,
  placeholder,
  className,
  children,
  triggerClassName,
}) => {
  // Find the selected item's label
  const getSelectedLabel = () => {
    // Traverse the children to find the matching item and extract its label
    const selectedItem = React.Children.toArray(children).find(
      (child) => React.isValidElement(child) && child.props.value === value
    );

    if (React.isValidElement(selectedItem)) {
      // If the item has a label prop, use that
      if (selectedItem.props.label) {
        return selectedItem.props.label;
      }
      // Otherwise, use the children
      return selectedItem.props.children;
    }

    // If no item is selected, show the placeholder
    return placeholder;
  };

  return (
    <div className={className}>
      <BaseSelect value={value} onValueChange={onValueChange}>
        <BaseSelectTrigger className={triggerClassName}>
          <BaseSelectValue placeholder={placeholder}>
            {getSelectedLabel()}
          </BaseSelectValue>
        </BaseSelectTrigger>
        <BaseSelectContent>
          {children}
        </BaseSelectContent>
      </BaseSelect>
    </div>
  );
};
