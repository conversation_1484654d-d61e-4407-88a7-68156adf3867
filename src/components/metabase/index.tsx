import { API_CONFIG } from "@/config/api";
import axios from "axios";
import React, { useEffect, useRef, useState } from "react";
import { supabase } from "@/integrations/supabase/client";

const categoryMap = {
  Agro: "Agro",
  Coatings: "Coatings",
  Construction: "Construction",
  "PersonalCare": "PersonalCare",
  "OilGas": "OilGas",
  "FoodAdditives": "FoodAdditives",
  Pharmaceuticals: "Pharmaceuticals",
};

export default function Metabase() {
  const [userData,setUserData] = useState<any>()
  const getUserData = async () => {
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        return;
      }

      // Fetch user's category and country from user_roles table
      const userId = session.user.id;
      const { data: userData, error: userError } = await supabase
        .from("user_roles")
        .select("category, country,role,is_crm_enabled")
        .eq("user_id", userId)
        .single();

        setUserData(userData)
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(()=>{
    getUserData()
  },[])
  const userRole = userData?.role
  const category = userData?.category;
  const userEmail = localStorage.getItem("userEmail");
  return (
   userData && <DashboardEmbed
      type={userRole}
      visible={true}
      category={category}
      userEmail={userEmail}
    />
  );
}

export const DashboardEmbed = ({
  userId,
  type,
  visible,
  category,
  userEmail,
}: {
  userId?: string;
  type: string;
  visible: boolean;
  category: string;
  userEmail?: string;
}) => {
  const [dashboardUrl, setDashboardUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isIframeLoaded, setIsIframeLoaded] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const fetchEmbedUrl = async () => {
    try {
      setIsIframeLoaded(false);
      const response = await axios.post(
        `${API_CONFIG.catalogBaseUrl}metabase-dashboard`,
        {
          name: "Functions",
          category,
          role:type,
          sales_executive: userEmail,
        },
        {
          headers: {
            Authorization: `Bearer ${API_CONFIG.accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      const data = response.data;
      if (data.url) {
        setDashboardUrl(data.url);
      } else {
        setError("Failed to load dashboard URL");
      }
    } catch (err: any) {
      setError(err.message || "Unknown error");
    }
  };

  useEffect(() => {
    if (!dashboardUrl) fetchEmbedUrl();
  }, [dashboardUrl]);

  //   useEffect(() => {
  //   if (!url) return;

  //   const TOKEN_VALIDITY_MS = 2 * 60 * 1000; // 10 minutes
  //   const REFRESH_BUFFER_MS = 1 * 60 * 1000;  // Refresh 1 min before expiry

  //   const timer = setTimeout(() => {
  //     refreshDashboardUrl(); // Function to refetch URL and update iframe
  //   }, TOKEN_VALIDITY_MS - REFRESH_BUFFER_MS);

  //   return () => clearTimeout(timer);
  // }, [url]);

  if (error) {
    return <p className="text-red-500">Error: {error}</p>;
  }

  return (
    <div className="relative min-h-[800px]">
      {!isIframeLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-white z-10">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-600" />
          <span className="ml-3 text-gray-500">Loading dashboard...</span>
        </div>
      )}
      {dashboardUrl && (
        <iframe
          ref={iframeRef}
          title="Metabase Dashboard"
          src={dashboardUrl}
          frameBorder="0"
          width="100%"
          height="800"
          onLoad={() => setIsIframeLoaded(true)}
          allowTransparency={true}
          className="rounded-lg shadow border border-gray-200"
          onError={() => fetchEmbedUrl()} // fallback if load fails
        />
      )}
    </div>
  );
};
