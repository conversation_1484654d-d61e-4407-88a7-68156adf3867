
import { Card, CardContent, <PERSON>Header, <PERSON>Title } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";
import { ReactNode } from "react";

interface PlaceholderProps {
  title: string;
  icon: LucideIcon | (() => ReactNode);
  color: string;
  message: string;
}

export const PlaceholderContent = ({ title, icon, color, message }: PlaceholderProps) => {
  // Check if icon is a Lucide icon or a function
  const IconComponent = typeof icon === 'function' ? icon : icon;
  
  return (
    <div className="h-full flex flex-col">
      <Card className="flex-1">
        <CardHeader>
          <div className="flex items-center gap-3">
            <div 
              className="w-10 h-10 rounded-full flex items-center justify-center" 
              style={{ backgroundColor: `${color}20` }}
            >
              {typeof icon === 'function' ? 
                <IconComponent /> : 
                <IconComponent size={20} style={{ color }} />
              }
            </div>
            <CardTitle>{title}</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center text-center p-12">
          <div 
            className="w-16 h-16 rounded-full flex items-center justify-center mb-4" 
            style={{ backgroundColor: `${color}10` }}
          >
            {typeof icon === 'function' ? 
              <IconComponent /> : 
              <IconComponent size={30} style={{ color }} />
            }
          </div>
          <h3 className="text-lg font-medium mb-2">Coming Soon</h3>
          <p className="text-muted-foreground max-w-md">{message}</p>
        </CardContent>
      </Card>
    </div>
  );
};
