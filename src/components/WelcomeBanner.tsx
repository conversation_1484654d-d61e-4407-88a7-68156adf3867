import { Import, Flask<PERSON><PERSON><PERSON>, <PERSON><PERSON>he<PERSON> } from "lucide-react";
import { Card } from "./ui/card";

const WelcomeBanner = () => {
  return (
    <Card className="mb-8 bg-gradient-to-r from-primary-100 to-white p-6 animate-fadeIn">
      <div className="flex flex-col md:flex-row items-center justify-between gap-6">
        <div className="text-left space-y-2 flex-1">
          <h1 className="text-2xl md:text-3xl font-bold text-primary-900">
            Welcome to MStack
          </h1>
          <p className="text-primary-700 max-w-xl">
            Navigate complex tariff landscapes with data-driven insights. Our procurement intelligence 
            helps you optimize import costs and maintain competitive advantage.
          </p>
        </div>
        
        <div className="flex gap-6 text-primary-700">
          <div className="flex flex-col items-center gap-2">
            <div className="p-3 bg-primary-500 rounded-full text-white">
              <Import className="h-6 w-6" />
            </div>
            <span className="text-sm">Global</span>
          </div>
          
          <div className="flex flex-col items-center gap-2">
            <div className="p-3 bg-primary-500 rounded-full text-white">
              <FlaskConical className="h-6 w-6" />
            </div>
            <span className="text-sm">Simple</span>
          </div>
          
          <div className="flex flex-col items-center gap-2">
            <div className="p-3 bg-primary-500 rounded-full text-white">
              <ShieldCheck className="h-6 w-6" />
            </div>
            <span className="text-sm">Efficient</span>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default WelcomeBanner;