
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Award, CreditCard, Clock, ArrowUpCircle } from "lucide-react";

const TalkToUs = () => {
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="max-w-6xl mx-auto px-4">
      <Card className="my-12 p-8 bg-gradient-to-br from-primary-100 to-white text-primary-800">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center gap-4 mb-8">
            <ShieldCheck className="h-8 w-8 text-primary-600" />
            <h2 className="text-2xl font-bold">Talk to Our Experts</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div className="space-y-6">
              <p className="text-primary-700 leading-relaxed text-lg">
                Join thousands of businesses that trust MStack for their procurement intelligence needs. Our experts are ready to help you optimize your supply chain.
              </p>
              <div className="flex flex-col space-y-8">
                <div className="flex items-center">
                  <img 
                    src="/lovable-uploads/ad13b7d1-ebd6-43a9-a6e1-aa5027505965.png" 
                    alt="MStack Logo" 
                    className="h-12 object-contain"
                  />
                </div>
                <Button
                  onClick={scrollToTop}
                  className="bg-primary-600 text-white hover:bg-primary-700 w-fit"
                >
                  <ArrowUpCircle className="mr-2 h-5 w-5" />
                  Get in Touch
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 gap-6">
              <div className="flex items-start gap-4 group transition-all">
                <div className="p-2 rounded-lg bg-primary-50 text-primary-600 group-hover:bg-primary-100">
                  <Percent className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Save 10–25% on Procurement Costs</h3>
                  <p className="text-primary-600 text-sm">Optimize your spending with our intelligent solutions</p>
                </div>
              </div>

              <div className="flex items-start gap-4 group transition-all">
                <div className="p-2 rounded-lg bg-primary-50 text-primary-600 group-hover:bg-primary-100">
                  <Award className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Unmatched Quality Control</h3>
                  <p className="text-primary-600 text-sm">Rigorous standards for every procurement</p>
                </div>
              </div>

              <div className="flex items-start gap-4 group transition-all">
                <div className="p-2 rounded-lg bg-primary-50 text-primary-600 group-hover:bg-primary-100">
                  <CreditCard className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Flexible Credit Solutions</h3>
                  <p className="text-primary-600 text-sm">Tailored financial options for your business</p>
                </div>
              </div>

              <div className="flex items-start gap-4 group transition-all">
                <div className="p-2 rounded-lg bg-primary-50 text-primary-600 group-hover:bg-primary-100">
                  <Clock className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1">On-Time, Every Time</h3>
                  <p className="text-primary-600 text-sm">Reliable delivery you can count on</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default TalkToUs;
