import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { Loader2, User, Shield, Mail, Globe, Tag, Edit2, Check, X } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

type UserRole = "admin" | "sales" | "bu_head";
type UserCategory = "Agro" | "Food" | "Health" | "Tech" | "Other";

interface UserData {
  id: string;
  email: string;
  role?: UserRole;
  country?: string;
  category?: UserCategory;
  created_at?: string;
}

interface DatabaseUser {
  id: string;
  email: string;
  role?: UserRole;
  userData?: {
    role?: UserRole;
    country?: string;
    category?: UserCategory;
  };
  created_at?: string;
}

const countries = [
  "IND",
  "USA",
  "China",
  "Canada",
  "UAE",
];

export const UserSettings = () => {
  const [users, setUsers] = useState<UserData[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [editingUserId, setEditingUserId] = useState<string | null>(null);
  const [editedUser, setEditedUser] = useState<Partial<UserData> | null>(null);

  useEffect(() => {
    fetchCurrentUser();
    fetchUsers();
  }, []);

  const fetchCurrentUser = async () => {
    try {
      const user = {
        email: localStorage.getItem('userEmail'),
        id: localStorage.getItem('userId'),
        role: localStorage.getItem('userRole'),
        country: localStorage.getItem('userCountry'),
        category: localStorage.getItem('userCategory')
      }
      if (user?.email) {
        setCurrentUser(user);
        setIsAdmin(user.role === 'admin');
      }
    } catch (error) {
      console.error("Error fetching current user:", error);
    }
  };

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('user_list')
        .select(`*,userData:user_roles(*)`);

      if (error) throw error;
      
      // Ensure all required fields are present with default values if missing
      const formattedUsers: UserData[] = (data as DatabaseUser[] || []).map(user => ({
        id: user.id,
        email: user.email,
        role: user.userData[0]?.role === null ? 'sales' : user.userData[0]?.role,
        country: user.userData[0]?.country || '',
        category: user.userData[0]?.category === null ? 'Agro' : user.userData[0]?.category
      }));
      
      setUsers(formattedUsers);
    } catch (error) {
      console.error("Error fetching users:", error);
      toast.error("Failed to fetch users");
    } finally {
      setLoading(false);
    }
  };

  const handleEditClick = (user: UserData) => {
    setEditingUserId(user.id);
    setEditedUser({ ...user });
  };

  const handleCancelEdit = () => {
    setEditingUserId(null);
    setEditedUser(null);
  };

  const handleFieldChange = (field: keyof UserData, value: string) => {
    if (editedUser) {
      setEditedUser({ ...editedUser, [field]: value });
    }
  };

  const handleSaveEdit = async () => {
    if (!editedUser || !editingUserId) return;

    try {
      // First check if the user exists in user_roles table
      const { data: existingUser, error: checkError } = await supabase
        .from('user_roles')
        .select('*')
        .eq('user_id', editingUserId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is the error code for no rows returned
        throw checkError;
      }

      const userRoleData = {
        user_id: editingUserId,
        role: editedUser.role,
        country: editedUser.country,
        category: editedUser.category
      };

      let error;
      if (existingUser) {
        // Update existing user
        const { error: updateError } = await supabase
          .from('user_roles')
          .update({
            role: editedUser.role,
            country: editedUser.country,
            category: editedUser.category
          })
          .eq('user_id', editingUserId);
        error = updateError;
      } else {
        // Insert new user
        const { error: insertError } = await supabase
          .from('user_roles')
          .insert(userRoleData);
        error = insertError;
      }

      if (error) throw error;

      setUsers(users.map(user => 
        user.id === editingUserId ? { ...user, ...editedUser } : user
      ));

      toast.success("User updated successfully");
      setEditingUserId(null);
      setEditedUser(null);
    } catch (error) {
      console.error("Error updating user:", error);
      toast.error("Failed to update user");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="flex flex-col items-center justify-center h-full space-y-4">
        <Shield className="h-12 w-12 text-muted-foreground" />
        <h2 className="text-xl font-semibold">Access Denied</h2>
        <p className="text-muted-foreground">You don't have permission to access this page.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">User Management</h1>
      </div>

      <div className="grid gap-6">
        {/* <Card>
          <CardHeader>
            <CardTitle>Current User</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                <User className="h-6 w-6 text-primary" />
              </div>
              <div>
                <p className="font-medium">{currentUser?.email}</p>
                <p className="text-sm text-muted-foreground">Administrator</p>
              </div>
            </div>
          </CardContent>
        </Card> */}

        <Card>
          <CardHeader>
            <CardTitle>All Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Email</TableHead>
                    <TableHead>Country</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => {
                    const isEditing = editingUserId === user.id;
                    const currentUser = isEditing ? editedUser : user;

                    return (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Mail className="h-4 w-4 text-muted-foreground" />
                            <span>{user.email}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Select
                              value={currentUser?.country || ""}
                              onValueChange={(value) => handleFieldChange('country', value)}
                              disabled={!isEditing}
                            >
                              <SelectTrigger className="w-[100px]">
                                <SelectValue placeholder="Select country" />
                              </SelectTrigger>
                              <SelectContent>
                                {countries.map((country) => (
                                  <SelectItem key={country} value={country}>
                                    {country}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Select
                              value={currentUser?.category || "Agro"}
                              onValueChange={(value) => handleFieldChange('category', value)}
                              disabled={!isEditing}
                            >
                              <SelectTrigger className="w-[140px]">
                                <SelectValue placeholder="Select category" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Agro">Agro</SelectItem>
                                <SelectItem value="PersonalCare">Personal Care</SelectItem>
                                <SelectItem value="FoodAdditives">Food Additives</SelectItem>
                                <SelectItem value="Pharmaceuticals">Pharmaceuticals</SelectItem>
                                <SelectItem value="Coatings">Coatings</SelectItem>
                                <SelectItem value="OilGas">Oil & Gas</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Select
                            value={currentUser?.role}
                            onValueChange={(value) => handleFieldChange('role', value)}
                            disabled={!isEditing}
                          >
                            <SelectTrigger className="w-[120px]">
                              <SelectValue placeholder="Select role" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="admin">Admin</SelectItem>
                              <SelectItem value="sales">Sales</SelectItem>
                              <SelectItem value="bu_head">BU Head</SelectItem>
                            </SelectContent>
                          </Select>
                        </TableCell>
                        <TableCell>
                          {isEditing ? (
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={handleSaveEdit}
                              >
                                <Check className="h-4 w-4 text-green-600" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={handleCancelEdit}
                              >
                                <X className="h-4 w-4 text-red-600" />
                              </Button>
                            </div>
                          ) : (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleEditClick(user)}
                            >
                              <Edit2 className="h-4 w-4" />
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}; 