import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";

export interface EnquiryStats {
  total_enquiries: number;
  open_enquiries: number;
  closed: number;
  pending_on_me: number;
}

const defaultStats: EnquiryStats = {
  total_enquiries: 0,
  open_enquiries: 0,
  closed: 0,
  pending_on_me: 0,
};

export const useStats = (userEmail: string | undefined) => {
  const {
    data: stats,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["enquiry-stats", userEmail],
    enabled: !!userEmail,
    queryFn: async () => {
      if (!userEmail) return defaultStats;

      // Get total enquiries for this sales person
      const { count: totalCount } = await supabase
        .from("get_enquiry_board")
        .select("*", { count: "exact", head: true })
        .eq("sales_team_member", userEmail)
        .eq("is_new", true);

      // Get open enquiries (not in final states)
      const { count: openCount } = await supabase
        .from("get_enquiry_board")
        .select("*", { count: "exact", head: true })
        .eq("sales_team_member", userEmail)
        .not(
          "current_status",
          "in",
          '("cancelled","regret","sample_rejected","quote_rejected","po_raised")'
        )
        .eq("is_new", true);

      console.log("$$$", openCount);

      // Get closed enquiries
      const { count: closedCount } = await supabase
        .from("get_enquiry_board")
        .select("*", { count: "exact", head: true })
        .eq("sales_team_member", userEmail)
        .in("current_status", [
          "sample_requested",
          "sample_available",
          "sample_in_transit",
          "sample_delivered",
          "sample_redo"
        ])
        .eq("is_new", true);


      console.log("$$$", closedCount);

      // Get pending on me enquiries - using pricing_quotation_generated
      const { count: pendingCount } = await supabase
        .from("get_enquiry_board")
        .select("*", { count: "exact", head: true })
        .eq("sales_team_member", userEmail)
        .in("current_status", [
          "clarification_needed",
          "pricing_quotation_generated",
          "quote_accepted",
          "sample_delivered",
          "sample_accepted",
        ])
        .eq("is_new", true);

      return {
        total_enquiries: totalCount || 0,
        open_enquiries: openCount || 0,
        closed: closedCount || 0,
        pending_on_me: pendingCount || 0,
      } as EnquiryStats;
    },
  });

  return { stats: stats || defaultStats, isLoading, refetch };
};
