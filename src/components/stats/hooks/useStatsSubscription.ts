
import { useEffect } from 'react';
import { supabase } from "@/integrations/supabase/client";

export const useStatsSubscription = (userEmail: string | undefined, onUpdate: () => void) => {
  useEffect(() => {
    if (!userEmail) return;

    // Subscribe to changes in enquiries table for this user
    const enquiriesChannel = supabase
      .channel('public:enquiries')
      .on(
        'postgres_changes',
        { 
          event: '*', 
          schema: 'public', 
          table: 'enquiries',
          filter: `sales_team_member=eq.${userEmail}`
        },
        () => {
          console.log('Enquiries table changed for current user, refreshing stats...');
          onUpdate();
        }
      )
      .subscribe();

    // Cleanup subscriptions
    return () => {
      supabase.removeChannel(enquiriesChannel);
    };
  }, [onUpdate, userEmail]);
};
