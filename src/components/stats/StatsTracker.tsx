
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Card } from "@/components/ui/card";
import { useState } from "react";
import EnquiriesModal from "./EnquiriesModal";
import { TrendingUp, MessageCircleQuestion, HeartCrack, Clock, Package, Layers } from "lucide-react";
import StatsCard from "./components/StatsCard";
import { useStats } from "./hooks/useStats";
import { useStatsSubscription } from "./hooks/useStatsSubscription";

// Modal types for each stats card
type StatsModalType = "total" | "open" | "closed" | "pending" | null;

const StatsTracker = () => {
  const [modalType, setModalType] = useState<StatsModalType>(null);

  // Get the current user's email
  const { data: session } = useQuery({
    queryKey: ['auth-session'],
    queryFn: async () => {
      const { data: { session } } = await supabase.auth.getSession();
      return session;
    },
  });

  const { stats, isLoading, refetch } = useStats(session?.user?.email);
  
  // Setup real-time subscriptions
  useStatsSubscription(session?.user?.email, refetch);

  if (isLoading) {
    return (
      <div className="py-8">
        <Card className="p-6 bg-white border border-gray-200 rounded-lg">
          <div className="flex items-center justify-center h-40">
            <p className="text-gray-500">Loading statistics...</p>
          </div>
        </Card>
      </div>
    );
  }

  // Get appropriate modal title based on type
  const getModalTitle = (type: StatsModalType) => {
    switch (type) {
      case "total":
        return "All Enquiries";
      case "open":
        return "Open Enquiries";
      case "closed":
        return "Open Samples Enquiries";
      case "pending":
        return "Pending on Me";
      default:
        return "";
    }
  };

  // Map our modal types to the EnquiriesModal filter type
  const getEnquiriesModalFilter = (type: StatsModalType) => {
    switch (type) {
      case "open":
        return "open";
      case "closed":
        return "closed";
      case "pending":
        return "pending";
      case "total":
      default:
        return "total";
    }
  };

  return (
    <div className="py-8">
      <Card className="p-6 bg-white border border-gray-200 rounded-lg">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6 border-b border-gray-100 pb-4">
          <div className="flex items-center gap-2">
            <Layers className="h-5 w-5 text-[#294d48]" />
            <h2 className="text-xl font-semibold text-[#294d48]">Quick Actions</h2>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatsCard
            title="Total Enquiries"
            value={stats.total_enquiries}
            icon={TrendingUp}
            onClick={() => setModalType("total")}
          />
          <StatsCard
            title="Open Enquiries"
            value={stats.open_enquiries}
            icon={Package}
            onClick={() => setModalType("open")}
          />
          <StatsCard
            title="Open Samples"
            value={stats.closed}
            icon={Package}
            onClick={() => setModalType("closed")}
          />
          <StatsCard
            title="Pending on me"
            value={stats.pending_on_me}
            icon={Clock}
            onClick={() => setModalType("pending")}
          />
        </div>
      </Card>

      {modalType && (
        <EnquiriesModal
          open={modalType !== null}
          onOpenChange={(open) => !open && setModalType(null)}
          type="total"
          title={getModalTitle(modalType)}
          filter={getEnquiriesModalFilter(modalType)}
        />
      )}
    </div>
  );
};

export default StatsTracker;
