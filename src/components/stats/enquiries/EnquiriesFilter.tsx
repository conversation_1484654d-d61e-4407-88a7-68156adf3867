
import { useState } from "react";
import { Filter, FilterX } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export type FilterOptions = {
  country?: string;
  criticality?: string;
  status?: string;
};

export interface EnquiriesFilterProps {
  appliedFilters: FilterOptions;
  onFilterChange: (filters: FilterOptions) => void;
  countries: string[];
  statuses: string[];
}

const EnquiriesFilter = ({
  appliedFilters,
  onFilterChange,
  countries,
  statuses,
}: EnquiriesFilterProps) => {
  const [isExpanded, setIsExpanded] = useState(true); // Set to true by default
  
  const criticalities = ["high", "medium", "low"];

  const handleFilterChange = (key: keyof FilterOptions, value: string | undefined) => {
    const newFilters = { ...appliedFilters };
    
    if (value === "all" || !value) {
      delete newFilters[key];
    } else {
      newFilters[key] = value;
    }
    
    onFilterChange(newFilters);
  };

  const clearFilters = () => {
    onFilterChange({});
  };

  const activeFilterCount = Object.keys(appliedFilters).length;

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-3">
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2 bg-white"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? (
            <FilterX className="h-4 w-4 text-[#294d48]" />
          ) : (
            <Filter className="h-4 w-4 text-[#294d48]" />
          )}
          <span>Filters</span>
          {activeFilterCount > 0 && (
            <Badge className="ml-1 bg-[#294d48]">
              {activeFilterCount}
            </Badge>
          )}
        </Button>
        
        {activeFilterCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="text-sm text-gray-500 hover:text-gray-700"
          >
            Clear all
          </Button>
        )}
      </div>

      {isExpanded && (
        <div className="p-4 mb-4 grid grid-cols-1 md:grid-cols-3 gap-4 bg-white/80 rounded-lg border border-gray-200">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Country</label>
            <Select
              value={appliedFilters.country || "all"}
              onValueChange={(value) => handleFilterChange("country", value)}
            >
              <SelectTrigger className="bg-white border-[#D8DCDB]">
                <SelectValue placeholder="All Countries" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Countries</SelectItem>
                {countries.map((country) => (
                  <SelectItem key={country} value={country}>{country}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Priority</label>
            <Select
              value={appliedFilters.criticality || "all"}
              onValueChange={(value) => handleFilterChange("criticality", value)}
            >
              <SelectTrigger className="bg-white border-[#D8DCDB]">
                <SelectValue placeholder="All Priorities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                {criticalities.map((level) => (
                  <SelectItem key={level} value={level}>
                    {level.charAt(0).toUpperCase() + level.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Status</label>
            <Select
              value={appliedFilters.status || "all"}
              onValueChange={(value) => handleFilterChange("status", value)}
            >
              <SelectTrigger className="bg-white border-[#D8DCDB]">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                {statuses.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status.replace(/_/g, ' ').split(' ').map(word => 
                      word.charAt(0).toUpperCase() + word.slice(1)
                    ).join(' ')}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnquiriesFilter;
