
import { Button } from "@/components/ui/button";
import { TableCell, TableRow } from "@/components/ui/table";
import { Database } from "@/integrations/supabase/types";
import { format } from "date-fns";
import { Paperclip, TestTubes, XOctagon } from "lucide-react";

type EnquiryType = Database['public']['Tables']['enquiries']['Row'] & {
  enquiry_documents: Database['public']['Tables']['enquiry_documents']['Row'][];
  status_history?: Database['public']['Tables']['enquiry_status_history']['Row'][];
  sample_requested?: boolean;
  sample_requested_at?: string;
  enquiry_id?: string;
};

// We need to define a draft type without referencing non-existent tables
type DraftEnquiryType = {
  id: string;
  enquiry_id?: string;
  created_at?: string;
  customer_full_name?: string;
  chemical_name?: string;
  brand?: string;
  product?: string;
  country?: string;
  quantity?: number;
  target_quotation_date?: string;
  enquiry_documents: Database['public']['Tables']['enquiry_documents']['Row'][];
};

interface EnquiriesTableRowProps {
  enquiry: EnquiryType | DraftEnquiryType;
  type: "total" | "recent" | "drafts";
  expandedRow: string | null;
  onExpandRow: (id: string | null) => void;
  onRequestSample?: (enquiryId: string) => void;
  onCancelEnquiry?: (enquiryId: string) => void;
  onShowLifecycle?: (enquiry: EnquiryType) => void;
  isSelected?: boolean;
}

const formatStatus = (status: string) => {
  return status
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

const EnquiriesTableRow = ({
  enquiry,
  type,
  expandedRow,
  onExpandRow,
  onRequestSample,
  onCancelEnquiry,
  onShowLifecycle,
  isSelected = false
}: EnquiriesTableRowProps) => {
  const isStatusSampleRelated = (status: string) => {
    return status.includes('sample');
  };

  const isEnquiryActive = (status: string) => {
    return !['cancelled', 'order_fulfilled'].includes(status);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new_enquiry':
        return 'text-blue-600';
      case 'new_quote_wip':
        return 'text-amber-600';
      case 'quote_shared':
        return 'text-green-600';
      case 'lost_no_response':
        return 'text-red-600';
      case 'sample_requested':
      case 'sample_shared':
      case 'sample_approved':
      case 'sample_rejected':
        return 'text-purple-600';
      case 'order_received':
      case 'order_fulfilled':
        return 'text-emerald-600';
      case 'cancelled':
        return 'text-gray-600';
      default:
        return 'text-gray-600';
    }
  };

  // We'll use the timeline button instead of row click
  const handleRowClick = () => {};

  return (
    <TableRow
      className={`cursor-pointer hover:bg-gray-50/80 text-xs transition-colors ${
        isSelected
          ? 'bg-[#9b87f5]/10 hover:bg-[#9b87f5]/10 border-l-4 border-l-[#9b87f5] font-medium shadow-sm'
          : ''
      }`}
      onClick={handleRowClick}
    >
      <TableCell className="py-2 px-2">
        {format(new Date(enquiry.created_at || ""), "dd/MM/yyyy")}
      </TableCell>
      <TableCell className="py-2 px-2">
        <span className={`${isSelected ? 'text-[#7E69AB] font-semibold' : ''}`}>
          {enquiry.enquiry_id ? `ENQ-${enquiry.enquiry_id}` : 'N/A'}
        </span>
      </TableCell>
      <TableCell className="py-2 px-2">
        <div className="flex items-center gap-1 max-w-[120px]">
          <span className={`truncate ${isSelected ? 'text-[#7E69AB] font-semibold' : ''}`}>
            {enquiry.customer_full_name}
          </span>
          {enquiry.enquiry_documents && enquiry.enquiry_documents.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onExpandRow(expandedRow === enquiry.id ? null : enquiry.id);
              }}
              className="p-0.5 h-auto min-w-0"
            >
              <Paperclip className="h-3 w-3 text-[#7E69AB]" />
            </Button>
          )}
        </div>
      </TableCell>
      <TableCell className="py-2 px-2">
        <div className={`max-w-[100px] truncate ${isSelected ? 'font-medium' : ''}`}>
          {enquiry.chemical_name || `${enquiry.brand} - ${enquiry.product}`}
        </div>
      </TableCell>
      <TableCell className="py-2 px-2">
        <div className={`max-w-[80px] truncate ${isSelected ? 'font-medium' : ''}`}>
          {enquiry.country}
        </div>
      </TableCell>
      <TableCell className="py-2 px-2">
        <div className={`max-w-[60px] truncate ${isSelected ? 'font-medium' : ''}`}>
          {enquiry.quantity}
        </div>
      </TableCell>
      <TableCell className="py-2 px-2">
        <div className={`max-w-[80px] truncate ${isSelected ? 'font-medium' : ''}`}>
          {enquiry.target_quotation_date ? format(new Date(enquiry.target_quotation_date), "dd/MM/yyyy") : "-"}
        </div>
      </TableCell>
      {'current_status' in enquiry ? (
        <TableCell className="py-2 px-2">
          <div className="flex flex-col gap-0.5">
            <span className={`font-medium text-[11px] ${getStatusColor(enquiry.current_status)} ${isSelected ? 'font-semibold' : ''}`}>
              {formatStatus(enquiry.current_status)}
            </span>
            {'sample_requested_at' in enquiry && enquiry.sample_requested_at && (
              <span className="text-[10px] text-purple-600">
                Sample: {format(new Date(enquiry.sample_requested_at), "dd/MM")}
              </span>
            )}
          </div>
        </TableCell>
      ) : (
        <TableCell className="py-2 px-2">
          <span className="text-gray-600 text-[11px]">Draft</span>
        </TableCell>
      )}
      {type !== 'drafts' && 'current_status' in enquiry && (
        <TableCell className="py-2 px-2">
          <div className="flex items-center gap-2">
            {onRequestSample && (
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onRequestSample(enquiry.id);
                }}
                disabled={isStatusSampleRelated(enquiry.current_status)}
                className={`flex items-center gap-1 h-7 px-2 text-xs ${
                  isStatusSampleRelated(enquiry.current_status) ? 'text-purple-600 border-purple-600' : ''
                }`}
              >
                <TestTubes className="h-3 w-3" />
                {isStatusSampleRelated(enquiry.current_status) ? 'Requested' : 'Sample'}
              </Button>
            )}
            {onCancelEnquiry && (
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onCancelEnquiry(enquiry.id);
                }}
                disabled={!isEnquiryActive(enquiry.current_status)}
                className={`flex items-center gap-1 h-7 px-2 text-xs ${
                  !isEnquiryActive(enquiry.current_status) ? 'text-gray-600 border-gray-600' : 'text-red-600 border-red-600 hover:bg-red-50'
                }`}
              >
                <XOctagon className="h-3 w-3" />
                Cancel
              </Button>
            )}
          </div>
        </TableCell>
      )}
    </TableRow>
  );
};

export default EnquiriesTableRow;
