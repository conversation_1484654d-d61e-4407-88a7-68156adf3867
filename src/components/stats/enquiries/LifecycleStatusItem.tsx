import { useState } from "react";
import { getStatusColor } from "./utils/statusUtils";
import { useClarificationQueries } from "./hooks/useClarificationQueries";
import { Database } from "@/integrations/supabase/types";
import StatusHeader from "./components/StatusHeader";
import StatusItemMetadata from "./components/StatusItemMetadata";
import QuotationActions from "./components/QuotationActions";
import ClarificationActions from "./components/ClarificationActions";
import SampleTracker from "./components/SampleTracker";
import SampleActions from "./components/SampleActions";
import { useSampleRequestStatus } from "./hooks/useSampleRequestStatus";
import PurchaseOrderButton from "./components/PurchaseOrderButton";
import PurchaseOrderDocumentList from "./components/PurchaseOrderDocumentList";
import { Paperclip } from "lucide-react";
import FileAttachmentModal from "./components/FileAttachmentModal";

type EnquiryLifecycleStatus =
  | Database["public"]["Enums"]["enquiry_lifecycle_status"]
  | string;

interface LifecycleStatusItemProps {
  status: EnquiryLifecycleStatus;
  isActive: boolean;
  date?: string;
  notes?: string;
  timeDifference?: string;
  procurementPOC?: string;
  enquiryId?: string;
  hasFeedback?: boolean;
  feedbackData?: {
    response: string;
    reason: string;
    created_at?: string;
    type?: string;
  } | null;
  salesAgentEmail?: string;
  onSampleRequestSuccess?: () => void;
  currentStatus?: EnquiryLifecycleStatus;
  enquiryStatusId?: string;
}

const LifecycleStatusItem = ({
  status,
  isActive,
  date,
  notes,
  timeDifference,
  procurementPOC,
  enquiryId,
  hasFeedback = false,
  feedbackData = null,
  salesAgentEmail,
  onSampleRequestSuccess,
  currentStatus,
  enquiryStatusId,
}: LifecycleStatusItemProps) => {
  const [isAttachmentsOpen, setIsAttachmentsOpen] = useState(false);

  // Check if enquiryId exists before using it
  const { data: sampleRequestData, isLoading: isLoadingSampleStatus } =
    useSampleRequestStatus(
      status === "sample_requested" && enquiryStatusId
        ? enquiryStatusId
        : undefined
    );

  console.log("Sample Request Data:", sampleRequestData);

  const {
    clarificationQueries,
    isLoading: isLoadingClarifications,
    updateClarificationQuery,
  } = useClarificationQueries(enquiryId);

  console.log(`LifecycleStatusItem (${status}):`, {
    hasFeedback,
    feedbackData,
    feedbackType: feedbackData?.type,
    isQuotationStatus: status === "pricing_quotation_generated",
    isClarificationStatus: status === "clarification_needed",
    isSampleRequestedStatus: status === "sample_requested",
    isSampleDeliveredStatus: status === "sample_delivered",
    isSampleAcceptedStatus: status === "sample_accepted",
    isPORaised: currentStatus === "po_raised",
    enquiryId,
    clarificationsCount: clarificationQueries?.length || 0,
    salesAgentEmail,
    sampleStatus: sampleRequestData?.status,
    currentStatus,
  });

  const baseColorClass = getStatusColor(status as string, isActive);

  const cardClasses = isActive
    ? `${baseColorClass} border`
    : `${baseColorClass} opacity-60 border`;

  console.log("heree", status, hasFeedback, feedbackData);

  const isQuotationStatus = status === "pricing_quotation_generated";
  const isClarificationStatus = status === "clarification_needed";
  console.log(
    "****",
    isQuotationStatus,
    isClarificationStatus,
    clarificationQueries
  );
  const isRegretStatus = status === "regret";
  const isSampleRequestedStatus = status === "sample_requested";
  const isSampleAcceptedStatus = status === "sample_accepted";

  const isSampleDelivered = currentStatus === "sample_delivered";
  const hasSampleRelatedStatus = [
    "sample_delivered",
    "sample_accepted",
    "sample_rejected",
    "sample_redo",
  ].includes(currentStatus || "");

  const isPORaised = currentStatus === "po_raised";
  const isPORaisedStatus = status === "po_raised";

  const shouldShowSampleFeedback =
    isSampleRequestedStatus &&
    (hasSampleRelatedStatus ||
      (hasFeedback && feedbackData?.type === "sample"));

  console.log("hello", isQuotationStatus);

  console.log("Sample feedback visibility check:", {
    isSampleRequestedStatus,
    isSampleDelivered,
    hasSampleRelatedStatus,
    hasFeedback,
    feedbackType: feedbackData?.type,
    shouldShowSampleFeedback,
    isPORaised,
    isPORaisedStatus,
    status,
  });

  const handleUpdateClarificationQuery = ({
    id,
    response,
    status,
  }: {
    id: string;
    response: string;
    status: string;
  }) => {
    updateClarificationQuery({
      id,
      response,
      status: status as "pending" | "resolved" | "rejected",
    });
  };

  // if (
  //   status === "quote_accepted" ||
  //   status === "quote_redo" ||
  //   status === "sample_redo"
  // ) {
  //   return null;
  // }

  console.log(
    "hellooohere",
    status,
    isSampleAcceptedStatus,
    isPORaised,
    enquiryId
  );

  return (
    <div className={`rounded-lg p-4 ${cardClasses}`}>
      <div className="flex flex-col space-y-3">
        <StatusHeader status={status as string} isActive={isActive} />

        {isSampleRequestedStatus && (
          <div className="mb-3 mt-1">
            <SampleTracker
              currentStatus={currentStatus || ""}
              sampleStatus={sampleRequestData?.status}
              sampleRequestId={sampleRequestData?.id}
              sampleRequestData={sampleRequestData}
            />

            {/* Display Sample Request Data */}
            {sampleRequestData && (
              <div className="mt-3 p-3 bg-white/50 rounded-md border border-gray-100">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="text-sm font-semibold text-gray-700">
                    Sample Request Details
                  </h4>
                  <button
                    onClick={() => setIsAttachmentsOpen(true)}
                    className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-900"
                  >
                    <Paperclip className="w-4 h-4" />
                    <span>Documents</span>
                  </button>
                </div>

                <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                  <div>
                    <span className="text-gray-600 font-semibold">
                      Quantity:
                    </span>{" "}
                    <span className="font-medium">
                      {sampleRequestData.quantity}{" "}
                      {sampleRequestData.quantity_unit}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 font-semibold">
                      Delivery City:
                    </span>{" "}
                    <span className="font-medium">
                      {sampleRequestData.delivery_city}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 font-semibold">
                      Delivery Country:
                    </span>{" "}
                    <span className="font-medium">
                      {sampleRequestData.delivery_country}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 font-semibold">
                      Postal Code:
                    </span>{" "}
                    <span className="font-medium">
                      {sampleRequestData.delivery_postal_code}
                    </span>
                  </div>
                  {sampleRequestData.sample_poc && (
                    <div className="col-span-2">
                      <span className="text-gray-600 font-semibold">
                        Sample POC:
                      </span>{" "}
                      <span className="font-medium">
                        {sampleRequestData.sample_poc}
                      </span>
                    </div>
                  )}

                  {sampleRequestData.remarks && (
                    <div className="col-span-2">
                      <span className="text-gray-600 font-semibold">
                        Remarks:
                      </span>{" "}
                      <span className="font-medium">
                        {sampleRequestData.remarks}
                      </span>
                    </div>
                  )}
                  <div className="col-span-2">
                    <span className="text-gray-600 font-semibold">
                      Delivery Address:
                    </span>{" "}
                    <span className="font-medium">
                      {sampleRequestData.delivery_address}
                    </span>
                  </div>
                  {sampleRequestData?.expected_delivery_date && (
                    <div className="col-span-2">
                      <span className="text-gray-600 font-semibold">
                        Expected Delivery Date:
                      </span>{" "}
                      <span className="font-medium">
                        {sampleRequestData.expected_delivery_date}
                      </span>
                    </div>
                  )}
                  {sampleRequestData?.tracking_number && (
                    <div className="col-span-2">
                      <span className="text-gray-600 font-semibold">
                        Tracking Number:
                      </span>{" "}
                      <span className="font-medium">
                        {sampleRequestData.tracking_number}
                      </span>
                    </div>
                  )}
                  {sampleRequestData.carrier_name && (
                    <div className="col-span-2">
                      <span className="text-gray-600 font-semibold">
                      Carrier Name:
                      </span>{" "}
                      <span className="font-medium">
                        {sampleRequestData.carrier_name}
                      </span>
                    </div>
                  )}
                </div>

                {/* Attachments Modal */}
                <FileAttachmentModal
                  isOpen={isAttachmentsOpen}
                  setIsOpen={setIsAttachmentsOpen}
                  id={sampleRequestData.id}
                  type="sample_request"
                />
              </div>
            )}

            {shouldShowSampleFeedback && (
              <SampleActions
                enquiryId={enquiryId}
                hasFeedback={hasFeedback && feedbackData?.type === "sample"}
                feedbackData={
                  feedbackData?.type === "sample" ? feedbackData : null
                }
                onSuccess={onSampleRequestSuccess}
                currentStatus={currentStatus}
                enquiryStatusId={enquiryStatusId}
              />
            )}
          </div>
        )}

        {/* {isSampleAcceptedStatus && !isPORaised && enquiryId && (
          <PurchaseOrderButton
            enquiryId={enquiryId}
            onSuccess={onSampleRequestSuccess}
          />
        )} */}

        {/* Only show PO documents on the PO Raised status card */}
        {isPORaisedStatus && enquiryId && (
          <PurchaseOrderDocumentList
            enquiryId={enquiryId}
            enquiryStatusId={enquiryStatusId}
          />
        )}

        {isQuotationStatus && (
          <QuotationActions
            enquiryId={enquiryId}
            hasFeedback={hasFeedback && feedbackData?.type === "quotation"}
            feedbackData={
              feedbackData?.type === "quotation" ? feedbackData : null
            }
            onSampleRequestSuccess={onSampleRequestSuccess}
            currentStatus={currentStatus}
            enquiryStatusId={enquiryStatusId}
          />
        )}

        {isClarificationStatus && enquiryId && (
          <ClarificationActions
            enquiryId={enquiryId}
            clarificationQueries={clarificationQueries || []}
            isLoadingClarifications={isLoadingClarifications}
            updateClarificationQuery={handleUpdateClarificationQuery}
          />
        )}

        {isRegretStatus && salesAgentEmail && (
          <div className="ml-0 p-2 bg-red-50 backdrop-blur-sm rounded-md border border-red-100 text-sm">
            <span className="text-red-600">
              Marked as regret by: <strong>{salesAgentEmail}</strong>
            </span>
          </div>
        )}

        <StatusItemMetadata
          notes={
            [
              "quote_accepted",
              "quote_redo",
              "sample_redo",
              "sample_accepted",
            ].includes(status)
              ? null
              : notes
          }
          date={date}
          timeDifference={timeDifference}
          procurementPOC={procurementPOC}
          isActive={isActive}
        />
      </div>
    </div>
  );
};

export default LifecycleStatusItem;
