export interface QuoteAttachment {
  id: string;
  file_name: string;
  file_path: string;
  content_type: string;
  url?: string;
}

export interface QuoteGenerationAttachment {
  id: string;
  file_name: string;
  file_path: string;
  content_type: string;
  quote_generation_id: string;
}

export interface QuoteAttachmentsModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  quoteGenerationId: string;
}