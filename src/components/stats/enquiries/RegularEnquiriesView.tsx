
import { useEffect } from "react";
import { EnquiryType } from "./hooks/useEnquiriesData";
import EnquiryGrid from "./EnquiryGrid";
import EnquiryLifecycleDialog from "./EnquiryLifecycleDialog";
import { useQueryClient } from "@tanstack/react-query";

interface RegularEnquiriesViewProps {
  filteredEnquiries: EnquiryType[] | undefined;
  type: "total" | "recent";
  selectedEnquiry: EnquiryType | null;
  setSelectedEnquiry: (enquiry: EnquiryType | null) => void;
}

const RegularEnquiriesView = ({
  filteredEnquiries,
  type,
  selectedEnquiry,
  setSelectedEnquiry
}: RegularEnquiriesViewProps) => {
  const handleSelectEnquiry = (enquiry: EnquiryType) => {
    console.log("Selecting enquiry:", enquiry.id);
    setSelectedEnquiry(enquiry);
  };

  const queryClient = useQueryClient();

  // Function to refresh enquiries data
  const refreshEnquiries = () => {
    console.log("Refreshing enquiries data");
    queryClient.invalidateQueries({ queryKey: ["enquiries"] });
  };

  // Listen for refresh events
  useEffect(() => {
    const handleRefreshEvent = () => {
      console.log("Received refreshEnquiries event");
      refreshEnquiries();
    };

    window.addEventListener('refreshEnquiries', handleRefreshEvent);

    return () => {
      window.removeEventListener('refreshEnquiries', handleRefreshEvent);
    };
  }, []);

  const handleCloseDialog = () => {
    // Important: properly reset the selected enquiry to null
    console.log("Closing dialog, resetting selected enquiry");
    setSelectedEnquiry(null);
  };

  return (
    <div className="relative w-full">
      {/* Grid view of all enquiry cards */}
      <EnquiryGrid
        filteredEnquiries={filteredEnquiries}
        type={type}
        selectedEnquiryId={selectedEnquiry?.id || null}
        onSelectEnquiry={handleSelectEnquiry}
      />

      {/* Lifecycle Dialog */}
      <EnquiryLifecycleDialog
        selectedEnquiry={selectedEnquiry}
        isOpen={!!selectedEnquiry}
        onClose={handleCloseDialog}
      />
    </div>
  );
};

export default RegularEnquiriesView;
