import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { X, Upload } from "lucide-react";
import DocumentUpload from "@/components/enquiries/components/DocumentUpload";
import FormControls from "./FormControls";
import { usePurchaseOrderSubmission } from "../hooks/usePurchaseOrderSubmission";
import { Input } from "antd";

interface PurchaseOrderFormProps {
  onClose: () => void;
  enquiryId: string;
  onSuccess?: () => void;
}

const PurchaseOrderForm = ({
  onClose,
  enquiryId,
  onSuccess,
}: PurchaseOrderFormProps) => {
  const { notes, setNotes, files, setFiles,otherfiles,setOtherFiles, isSubmitting, handleSubmit,ponumber,
    setPoNumber, } =
    usePurchaseOrderSubmission(enquiryId, onClose, onSuccess);

  return (
    <Card className="p-4 bg-white/80 backdrop-blur-md border border-green-100 rounded-lg">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-sm font-medium text-green-700">
          Raise Purchase Order
        </h3>
        <Button
          variant="ghost"
          size="sm"
          className="h-7 w-7 p-0 rounded-full hover:bg-gray-100"
          onClick={onClose}
        >
          <X className="h-4 w-4 text-gray-500" />
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Document upload - required */}
        <div className="space-y-2">
          <Label className="text-gray-700">
            Purchase Order Document (Required)
          </Label>
          <DocumentUpload
            onFilesChange={setFiles}
            files={files}
            accept=".pdf,.doc,.docx,.xls,.xlsx"
            label="Upload PO document or drag and drop file here"
            helpText="Supported formats: PDF, Word, Excel (Max: 10MB)"
          />
          {files.length === 0 && (
            <p className="text-xs text-red-500 mt-1">
              Please attach at least one document
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="notes" className="text-gray-700">
            Purchase Order Number
          </Label>
          <Input
            id="po_number"
            type="number"
min="0"
            placeholder="Enter PO Number"
            value={ponumber}
            onChange={(e) => setPoNumber(e.target.value)}
            className="min-h-[40px]"
          />
        </div>

        {/* Notes field - optional */}
        <div className="space-y-2">
          <Label htmlFor="notes" className="text-gray-700">
            Notes (Optional)
          </Label>
          <Textarea
            id="notes"
            placeholder="Add any additional notes about this purchase order..."
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            className="min-h-[80px] resize-none"
          />
        </div>

        <div className="space-y-2">
          <Label className="text-gray-700">
            TDS/Other Documents (Optional)
          </Label>
          <DocumentUpload
            onFilesChange={setOtherFiles}
            files={otherfiles}
            accept=".pdf,.doc,.docx,.xls,.xlsx"
            label="Upload Other documents or drag and drop file here"
            helpText="Supported formats: PDF, Word, Excel (Max: 10MB)"
          />
        </div>

        {/* Form controls */}
        <FormControls
          onClose={onClose}
          isSubmitting={isSubmitting}
          submitLabel="Submit Purchase Order"
        />
      </form>
    </Card>
  );
};

export default PurchaseOrderForm;
