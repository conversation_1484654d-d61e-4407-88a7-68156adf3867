import { useState } from "react";
import TimelineVerticalLine from "./TimelineVerticalLine";
import { formatTimeDifference } from "@/components/stats/enquiries/utils/timelineUtils";
import LifecycleStatusItem from "@/components/stats/enquiries/LifecycleStatusItem";
import { formatStatus } from "@/components/stats/enquiries/utils/statusUtils";
import { Database } from "@/integrations/supabase/types";

type EnquiryLifecycleStatus =
  | Database["public"]["Enums"]["enquiry_lifecycle_status"]
  | string;
type StatusHistoryItem =
  Database["public"]["Tables"]["enquiry_status_history"]["Row"];

interface EnquiryTimelineProps {
  enquiryId: string;
  currentStatus: EnquiryLifecycleStatus;
  statusHistory: StatusHistoryItem[];
  sampleFeedback?: {
    response: string;
    reason: string;
    created_at?: string;
    type?: string;
  } | null;
  hasSampleFeedback?: boolean;
  quotationFeedback?: {
    response: string;
    reason: string;
    created_at?: string;
    type?: string;
  } | null;
  hasQuotationFeedback?: boolean;
  onSampleRequestSuccess?: () => void;
}

const EnquiryTimeline = ({
  enquiryId,
  currentStatus,
  statusHistory,
  sampleFeedback = null,
  quotationFeedback = null,
  hasSampleFeedback = false,
  hasQuotationFeedback = false,
  onSampleRequestSuccess,
}: EnquiryTimelineProps) => {
  console.log("EnquiryTimeline rendering with:", {
    enquiryId,
    currentStatus,
    statusHistoryCount: statusHistory?.length || 0,
    statusHistoryIsArray: Array.isArray(statusHistory),
    statusHistoryItems: Array.isArray(statusHistory)
      ? statusHistory.map((item) => item.status)
      : [],
  });

  // Ensure statusHistory is always an array
  const safeStatusHistory = Array.isArray(statusHistory) ? statusHistory : [];

  console.log("filter", safeStatusHistory);

  // Format the timeline data
  const formattedTimeline = safeStatusHistory
    // .sort((a, b) => {
    //   const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
    //   const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
    //   return dateB - dateA;
    // })
    .map((historyItem, index, arr) => {
      const currentDate = historyItem.created_at
        ? new Date(historyItem.created_at)
        : null;
      const previousDate =
        index > 0 && arr[index - 1].created_at
          ? new Date(arr[index - 1].created_at)
          : null;

      // Set isActive to true for all cards
      const isActive = true;

      return {
        ...historyItem,
        timeDifference:
          currentDate && previousDate
            ? formatTimeDifference(currentDate, previousDate)
            : undefined,
      };
    });

  // Filter out all sample-related states except sample_requested
  const filteredTimeline = formattedTimeline.filter((item) => {
    const status = item.status as string;
    // Filter out sample_available, sample_in_transit and sample_delivered
    return !(
      status === "sample_available" ||
      status === "sample_in_transit" ||
      status === "sample_delivered"
    );
  });

  console.log("filter",filteredTimeline)

  // Get unique statuses in the timeline
  const timelineStatuses = new Set<string>();
  filteredTimeline.forEach((item) => {
    if (item.status) {
      timelineStatuses.add(item.status as string);
    }
  });

  // Check if we already have the current status in the timeline
  let hasCurrentStatusInTimeline = currentStatus
    ? timelineStatuses.has(currentStatus as string)
    : false;

  // If current status is one of the filtered sample states, we shouldn't show it as a separate card
  if (
    currentStatus === "sample_available" ||
    currentStatus === "sample_in_transit" ||
    currentStatus === "sample_delivered"
  ) {
    hasCurrentStatusInTimeline = true; // Pretend we already have it so it won't be added
  }

  // Check if the feedback is for a quotation or sample
  const isSampleFeedback = sampleFeedback?.type === "sample";
  const isQuotationFeedback = quotationFeedback?.type === "quotation";

  console.log("Timeline filtered:", {
    originalCount: formattedTimeline.length,
    filteredCount: filteredTimeline.length,
    hasCurrentStatusInTimeline,
    currentStatus,
    isSampleFeedback,
    isQuotationFeedback,
  });

  let currentStatusFeedback = null;

  if (currentStatus === 'pricing_quotation_generated' && quotationFeedback) {
    currentStatusFeedback = quotationFeedback;
  } else if (currentStatus === 'sample_requested' && sampleFeedback) {
    currentStatusFeedback = sampleFeedback;
  }
  

  return (
    <div className="relative">
      <TimelineVerticalLine />

      <div className="ml-6 space-y-6 relative">
        {filteredTimeline.map((item, index) => {
          // Check if this item should show feedback
          const shouldShowFeedback =
            item.status === "pricing_quotation_generated" ||
            item.status === "sample_requested";

          const feedbackData =
            item.status === "pricing_quotation_generated"
              ? quotationFeedback
              : sampleFeedback;

          console.log(
            "hereeee",
            hasQuotationFeedback,
            quotationFeedback,
            sampleFeedback,
            shouldShowFeedback,
            isSampleFeedback,
            isQuotationFeedback,
            item,
            item.status,
            feedbackData
          );

          return (
            <LifecycleStatusItem
              key={`${item.id || index}-${index}`}
              status={item.status as EnquiryLifecycleStatus}
              isActive={true}
              date={item.created_at}
              notes={item.notes}
              timeDifference={item.timeDifference}
              procurementPOC={item.procurement_poc}
              enquiryId={enquiryId}
              hasFeedback={shouldShowFeedback}
              feedbackData={shouldShowFeedback ? feedbackData : null}
              salesAgentEmail={item.sales_agent_email}
              onSampleRequestSuccess={onSampleRequestSuccess}
              currentStatus={currentStatus} // Pass current status
              enquiryStatusId={item.id}
            />
          );
        })}

        {/* Show current status if it's not already in the timeline and not a filtered sample state */}
        {!hasCurrentStatusInTimeline && currentStatus && (
          <LifecycleStatusItem
            status={currentStatus}
            isActive={true}
            hasFeedback={(isQuotationFeedback && currentStatus === 'pricing_quotation_generated') ||
                         (isSampleFeedback && currentStatus === 'sample_requested')}
            enquiryId={enquiryId}
            feedbackData={currentStatusFeedback}
            onSampleRequestSuccess={onSampleRequestSuccess}
            currentStatus={currentStatus}
          />
        )}
        
        {formattedTimeline.length === 0 && (
          <div className="mb-12 text-center py-8 px-4 bg-gray-50 rounded-lg border border-gray-200">
            <p className="text-gray-600">No status history available</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnquiryTimeline;
