import { cn } from "@/lib/utils";
import { SampleState } from "./types";
import { getStateDetails, isStateActive, getFeedbackIcon } from "./utils";

interface SampleStateIndicatorProps {
  state: SampleState;
  effectiveStatus: string;
  isLast: boolean;
  timestamp?: string;
}

const SampleStateIndicator = ({
  state,
  effectiveStatus,
  isLast,
  timestamp,
}: SampleStateIndicatorProps) => {
  const { icon, label } = getStateDetails(state);
  const active = isStateActive(state, effectiveStatus);

  return (
    <div className="flex flex-col items-center gap-1 relative z-10">
      <div
        className={cn(
          "rounded-full p-2 transition-all",
          active
            ? "bg-gradient-to-br from-[#9b87f5] to-[#7E69AB] text-white shadow-md"
            : "bg-gray-100 text-gray-400"
        )}
      >
        {icon}
      </div>
      <span
        className={cn(
          "text-xs font-medium",
          active ? "text-[#294d48]" : "text-gray-400"
        )}
      >
        {label}
      </span>
      {timestamp && (
  <span className={cn(
    "text-[10px]",
    active ? "text-gray-600" : "text-gray-400"
  )}>
    {new Date(Date.UTC(
      new Date(timestamp).getFullYear(),
      new Date(timestamp).getMonth(),
      new Date(timestamp).getDate(),
      new Date(timestamp).getHours(),
      new Date(timestamp).getMinutes(),
      new Date(timestamp).getSeconds()
    )).toLocaleString(undefined, {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: false,
    })}
  </span>
)}


      {/* Show feedback status below "Delivered" */}
      {isLast && getFeedbackIcon(effectiveStatus)}
    </div>
  );
};

export default SampleStateIndicator;
