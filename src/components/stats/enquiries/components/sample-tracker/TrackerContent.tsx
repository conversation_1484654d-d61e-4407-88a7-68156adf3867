import { sampleStates } from "./types";
import SampleStateIndicator from "./SampleStateIndicator";
import ConnectorLines from "./ConnectorLines";
import StatusIndicator from "./StatusIndicator";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

interface TrackerContentProps {
  effectiveStatus: string;
  sampleRequestId: string;
  sampleRequestData?: any;
}

const TrackerContent = ({
  effectiveStatus,
  sampleRequestId,
  sampleRequestData,
}: TrackerContentProps) => {
  // Ensure we have a valid status string
  const safeStatus = effectiveStatus || "sample_requested";

  console.log("TrackerContent rendering with status:", safeStatus);

  // Fetch status history for this sample request
  const { data: statusHistory } = useQuery({
    queryKey: ["sample-status-history", sampleRequestId],
    enabled: !!sampleRequestId,
    queryFn: async () => {
      const { data, error } = await supabase
        .from("sample_status_history")
        .select("*")
        .eq("sample_request_id", sampleRequestId)
        .order("changed_at", { ascending: true });

      if (error) {
        console.error("Error fetching status history:", error);
        return null;
      }
      console.log("Status history data:", data);
      return data;
    },
  });

  // Create a map of status timestamps
  const statusTimestamps =
    statusHistory?.reduce((acc, record) => {
      acc[record.sample_status] = record.changed_at;
      return acc;
    }, {} as Record<string, string>) ?? {};

  console.log("TrackerContent rendering with status:", safeStatus);
  console.log(
    "Status timestamps:",
    statusHistory,
    sampleRequestId,
    statusTimestamps
  );

  return (
    <div className="p-4 transition-all animate-accordion-down">
      <div className="flex justify-between items-center relative">
        {sampleStates.map((state, index) => (
          <SampleStateIndicator
            key={state}
            state={state}
            effectiveStatus={safeStatus}
            isLast={index === sampleStates.length - 1}
            timestamp={statusTimestamps[state]}
          />
        ))}

        {/* Connector lines - position at the center of the icons */}
        <ConnectorLines states={sampleStates} effectiveStatus={safeStatus} />
      </div>

      {/* Add current status indicator */}
      <StatusIndicator status={safeStatus} />

      {/* Shipping Details Section */}
      {(sampleRequestData?.expected_delivery_date ||
        sampleRequestData?.tracking_number ||
        sampleRequestData?.carrier_name) && (
        <div className="mt-4 p-3 bg-white/50 rounded-md border border-gray-100">
          <h4 className="text-sm font-semibold text-gray-700 mb-2">
            Shipping Details
          </h4>
          <div className="grid gap-2 text-sm">
            {sampleRequestData?.expected_delivery_date && (
              <div className="flex">
                <span className="text-gray-600 mr-2">Expected Delivery:</span>
                <span className="font-medium">sample</span>
              </div>
            )}
            {sampleRequestData?.tracking_number && (
              <div className="flex mr-2">
                <span className="text-gray-600">Tracking Number:</span>
                <span className="font-medium">
                  {sampleRequestData?.tracking_number}
                </span>
              </div>
            )}
            {sampleRequestData?.carrier_name && (
              <div className="flex mr-2">
                <span className="text-gray-600">Carrier:</span>
                <span className="font-medium">
                  {sampleRequestData?.carrier_name}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TrackerContent;
