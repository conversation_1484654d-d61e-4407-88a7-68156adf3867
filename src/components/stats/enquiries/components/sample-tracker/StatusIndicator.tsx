
interface StatusIndicatorProps {
  status: string;
}

const StatusIndicator = ({ status }: StatusIndicatorProps) => {
  // Format status for display by removing prefix and replacing underscores with spaces
  const formatStatusText = (status: string): string => {
    let formattedStatus = status.replace('sample_', '').replace(/_/g, ' ');
    // Capitalize first letter of each word
    return formattedStatus
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div className="mt-4 pt-3 border-t border-gray-100">
      <div className="flex justify-between items-center">
        <span className="text-xs text-gray-500">Current Status:</span>
        <span className="text-xs font-medium text-[#294d48] px-2 py-1 bg-[#9b87f5]/10 rounded">
          {formatStatusText(status)}
        </span>
      </div>
    </div>
  );
};

export default StatusIndicator;
