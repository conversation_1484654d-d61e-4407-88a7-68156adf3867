
import { Package, PackageCheck, Truck, Home, TestTubes, Check, X, RotateCcw } from "lucide-react";
import { SampleState, SampleFeedbackState } from "./types";

// Helper function to check if a state is active
export const isStateActive = (state: SampleState, effectiveStatus: string) => {
  const stateIndex = sampleStates.indexOf(state as SampleState);
  const currentIndex = sampleStates.indexOf(effectiveStatus as SampleState);
  
  // If effectiveStatus is not in sampleStates, handle it gracefully
  if (currentIndex === -1) {
    // If it's a feedback status or beyond, all states are active
    if (isFeedbackStatus(effectiveStatus) || 
        effectiveStatus === "po_raised") {
      return true;
    }
    return false;
  }
  
  return stateIndex <= currentIndex;
};

// Check if a status is a feedback status
export const isFeedbackStatus = (status: string): boolean => {
  return ['sample_accepted', 'sample_rejected', 'sample_redo'].includes(status);
};

// Get icon and label for a state
export const getStateDetails = (state: SampleState) => {
  switch (state) {
    case "sample_requested":
      return {
        icon: <TestTubes className="h-5 w-5" />,
        label: "Requested"
      };
    case "sample_available":
      return {
        icon: <Package className="h-5 w-5" />,
        label: "Available"
      };
    case "sample_in_transit":
      return {
        icon: <Truck className="h-5 w-5" />,
        label: "In Transit"
      };
    case "sample_delivered":
      return {
        icon: <Home className="h-5 w-5" />,
        label: "Delivered"
      };
    default:
      const stateString = state as string;
      const formatted = stateString.replace("sample_", "").replace(/_/g, " ");
      return {
        icon: <Package className="h-5 w-5" />,
        label: formatted || "Unknown"
      };
  }
};

// Get feedback icon if available
export const getFeedbackIcon = (effectiveStatus: string) => {
  if (!isFeedbackStatus(effectiveStatus)) return null;
  
  switch (effectiveStatus) {
    case "sample_accepted":
      return (
        <div className="flex items-center mt-2 px-2 py-1 rounded-full bg-green-100 text-green-800 text-xs font-medium">
          <Check className="h-3 w-3 mr-1" />
          Accepted
        </div>
      );
    case "sample_rejected":
      return (
        <div className="flex items-center mt-2 px-2 py-1 rounded-full bg-red-100 text-red-800 text-xs font-medium">
          <X className="h-3 w-3 mr-1" />
          Rejected
        </div>
      );
    case "sample_redo":
      return (
        <div className="flex items-center mt-2 px-2 py-1 rounded-full bg-amber-100 text-amber-800 text-xs font-medium">
          <RotateCcw className="h-3 w-3 mr-1" />
          Redo
        </div>
      );
    default:
      return null;
  }
};

import { sampleStates } from "./types";
