
import { ChevronDown, ChevronUp, PackageCheck } from "lucide-react";

interface TrackerHeaderProps {
  expanded: boolean;
  toggleExpanded: () => void;
}

const TrackerHeader = ({ expanded, toggleExpanded }: TrackerHeaderProps) => {
  return (
    <div 
      className="p-3 bg-[#9b87f5]/10 border-b border-[#9b87f5]/20 flex justify-between items-center cursor-pointer"
      onClick={toggleExpanded}
    >
      <h4 className="text-sm font-medium text-[#294d48] flex items-center">
        <PackageCheck className="h-4 w-4 mr-2 text-[#9b87f5]" />
        Sample Tracking
      </h4>
      {expanded ? 
        <ChevronUp className="h-4 w-4 text-[#294d48]" /> : 
        <ChevronDown className="h-4 w-4 text-[#294d48]" />
      }
    </div>
  );
};

export default TrackerHeader;
