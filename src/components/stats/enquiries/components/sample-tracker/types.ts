
// Sample tracker state types
export type SampleState = "sample_requested" | "sample_available" | "sample_in_transit" | "sample_delivered";
export type SampleFeedbackState = "sample_accepted" | "sample_rejected" | "sample_redo";

// List of sample tracking states in order
export const sampleStates: SampleState[] = [
  "sample_requested",
  "sample_available",
  "sample_in_transit",
  "sample_delivered"
];

export interface SampleRequestData {
  expected_delivery_date?: string;
  tracking_number?: string;
  carrier_name?: string;
  // add other fields if needed
}

export interface TrackerContentProps {
  effectiveStatus: string;
  sampleRequestId: string;
  sampleRequestData?: SampleRequestData;
}

export interface SampleTrackerProps {
  currentStatus: string;
  sampleStatus?: string;
  sampleRequestId?: string;
  sampleRequestData?: SampleRequestData;
}
