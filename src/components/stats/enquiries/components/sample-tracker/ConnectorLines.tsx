
import { cn } from "@/lib/utils";
import { SampleState } from "./types";
import { isStateActive } from "./utils";

interface ConnectorLinesProps {
  states: readonly SampleState[];
  effectiveStatus: string;
}

const ConnectorLines = ({ states, effectiveStatus }: ConnectorLinesProps) => {
  return (
    <div className="absolute top-1/2 left-0 right-0 h-0.5 transform -translate-y-1/2 z-0">
      <div className="relative w-full h-full">
        {states.map((state, index) => {
          if (index >= states.length - 1) return null;
          
          const active = isStateActive(state, effectiveStatus) && 
                         isStateActive(states[index + 1], effectiveStatus);
          const width = `${100 / (states.length - 1)}%`;
          
          return (
            <div 
              key={`connector-${index}`}
              className={cn(
                "absolute h-full transition-all",
                active
                  ? "bg-gradient-to-r from-[#9b87f5] to-[#7E69AB]" 
                  : "bg-gray-200"
              )}
              style={{
                left: `${(index * 100) / (states.length - 1)}%`,
                width: width
              }}
            />
          );
        })}
      </div>
    </div>
  );
};

export default ConnectorLines;
