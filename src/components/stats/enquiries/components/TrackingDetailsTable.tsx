import React from 'react';

interface TrackingDetailsTableProps {
  trackingData: any;
}

const TrackingDetailsTable: React.FC<TrackingDetailsTableProps> = ({
  trackingData
}) => {
  return (
    <div className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden mb-4">
      {/* Header */}
      {/* <div className="px-4 py-2 bg-gray-100/50 flex justify-between items-center border-b border-gray-200">
        <h4 className="text-sm font-medium text-gray-700">Tracking Details</h4>
      </div> */}

      {/* Table Container with horizontal scroll */}
      <div className="overflow-x-auto">
        <table className="w-full text-sm border border-gray-200">
          <thead className="bg-gray-100">
            <tr>
              <th className="p-2 text-left font-medium border-r">Tracking Number</th>
              <th className="p-2 text-left font-medium border-r">Tracking URL</th>
              <th className="p-2 text-left font-medium border-r">Carrier</th>
              <th className="p-2 text-left font-medium">Expected Delivery</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="p-2 border-r border-t bg-gray-50">
                {trackingData?.tracking_number || '-'}
              </td>
              <td className="p-2 border-r border-t bg-gray-50">
                {trackingData?.tracking_url || '-'}
              </td>
              <td className="p-2 border-r border-t bg-gray-50">
                {trackingData?.carrier_name || '-'}
              </td>
              <td className="p-2 border-t bg-gray-50">
                {trackingData?.expected_delivery_date || '-'}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TrackingDetailsTable;
