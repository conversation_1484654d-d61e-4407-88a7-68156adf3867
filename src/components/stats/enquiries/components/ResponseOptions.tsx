
import { Database } from "@/integrations/supabase/types";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, RefreshCw } from "lucide-react";

type CustomerResponse = Database["public"]["Enums"]["enquiry_lifecycle_status"] | string;

interface ResponseOptionsProps {
  selectedResponse: CustomerResponse | null;
  onSelect: (response: CustomerResponse) => void;
}

const ResponseOptions = ({ selectedResponse, onSelect }: ResponseOptionsProps) => {
  // Define the response options with their values
  const options: { value: CustomerResponse; label: string; icon: React.ReactNode; color: string }[] = [
    {
      value: "quote_accepted",
      label: "Accept Quotation",
      icon: <CheckCircle className="h-4 w-4" />,
      color: "bg-green-50 border-green-200 text-green-700"
    },
    {
      value: "quote_rejected",
      label: "Reject Quotation",
      icon: <XCircle className="h-4 w-4" />,
      color: "bg-red-50 border-red-200 text-red-700"
    },
    {
      value: "quote_redo",
      label: "Request New Quotation",
      icon: <RefreshCw className="h-4 w-4" />,
      color: "bg-amber-50 border-amber-200 text-amber-700"
    }
  ];

  const handleSelect = (value: CustomerResponse) => {
    console.log("Selected response option:", value); // Debug log
    onSelect(value);
  };

  return (
    <div className="flex flex-wrap gap-2">
      {options.map((option) => (
        <Button
          key={option.value}
          type="button"
          variant="outline"
          className={`flex items-center gap-1.5 rounded-full px-4 py-2 border ${
            selectedResponse === option.value
              ? `${option.color} ring-2 ring-offset-1 ${option.color.replace('bg-', 'ring-')}`
              : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
          }`}
          onClick={() => handleSelect(option.value)}
        >
          {option.icon}
          {option.label}
        </Button>
      ))}
    </div>
  );
};

export default ResponseOptions;
