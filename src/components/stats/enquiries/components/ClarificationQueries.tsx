
import { format } from "date-fns";
import { MessageCircle, Check, X, Clock } from "lucide-react";
import { ClarificationQuery } from "../hooks/useClarificationQueries";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";

interface ClarificationQueriesProps {
  queries: ClarificationQuery[];
  isLoading: boolean;
  onUpdateQuery?: (id: string, response: string, status: 'resolved' | 'rejected') => void;
}

const ClarificationQueries = ({ 
  queries, 
  isLoading,
  onUpdateQuery 
}: ClarificationQueriesProps) => {
  const [responses, setResponses] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState<Record<string, boolean>>({});

  if (isLoading) {
    return (
      <div className="p-4 bg-gray-50 rounded-md animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
        <div className="h-10 bg-gray-200 rounded w-full mb-2"></div>
      </div>
    );
  }

  if (!queries.length) {
    return (
      <div className="p-4 bg-gray-50 rounded-md text-center">
        <p className="text-gray-500">No clarification queries found</p>
      </div>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'resolved':
        return <Check className="h-3.5 w-3.5 text-green-600" />;
      case 'rejected':
        return <X className="h-3.5 w-3.5 text-red-600" />;
      default:
        return <Clock className="h-3.5 w-3.5 text-amber-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'resolved':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Resolved</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Rejected</Badge>;
      default:
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Pending</Badge>;
    }
  };

  const handleResponseChange = (id: string, value: string) => {
    setResponses(prev => ({ ...prev, [id]: value }));
  };

  const handleSubmitResponse = (id: string, status: 'resolved' | 'rejected') => {
    if (!onUpdateQuery) return;
    
    setIsSubmitting(prev => ({ ...prev, [id]: true }));
    onUpdateQuery(id, responses[id] || '', status);
    
    // Reset after submission (will be re-fetched)
    setTimeout(() => {
      setIsSubmitting(prev => ({ ...prev, [id]: false }));
      setResponses(prev => ({ ...prev, [id]: '' }));
    }, 1000);
  };

  return (
    <div className="space-y-3">
      {queries.map((query) => (
        <div 
          key={query.id} 
          className="bg-white rounded-lg border border-gray-200 overflow-hidden"
        >
          <div className="p-3 flex items-start justify-between gap-2">
            <div className="flex items-center gap-2 flex-shrink-0">
              <div className="h-8 w-8 rounded-full bg-[#F0EBFA] flex items-center justify-center">
                <MessageCircle className="h-4 w-4 text-[#7E69AB]" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Clarification</p>
                <p className="text-xs text-gray-500">
                  {query.created_at ? format(new Date(query.created_at), "dd MMM yyyy, HH:mm") : "Unknown date"}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-1.5">
              {getStatusBadge(query.status)}
            </div>
          </div>
          
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value={query.id} className="border-t border-gray-100">
              <AccordionTrigger className="py-2 px-3 text-sm hover:no-underline hover:bg-gray-50">
                <span className="font-normal">View details</span>
              </AccordionTrigger>
              <AccordionContent className="px-3 pb-3">
                <div className="space-y-3">
                  <div className="bg-[#F0EBFA]/30 p-2.5 rounded-md">
                    <p className="text-sm text-gray-800 whitespace-pre-wrap">{query.query}</p>
                    <p className="text-xs text-gray-500 mt-1.5">By {query.created_by}</p>
                  </div>
                  
                  {query.response ? (
                    <div className="bg-gray-50 p-2.5 rounded-md">
                      <p className="text-xs font-medium text-gray-700 mb-1">Response:</p>
                      <p className="text-sm text-gray-800 whitespace-pre-wrap">{query.response}</p>
                      {query.resolved_at && (
                        <p className="text-xs text-gray-500 mt-1.5">
                          {format(new Date(query.resolved_at), "dd MMM yyyy, HH:mm")}
                        </p>
                      )}
                    </div>
                  ) : onUpdateQuery && query.status === 'pending' ? (
                    <div className="space-y-3 border border-gray-200 rounded-md p-3 bg-gray-50">
                      <div>
                        <label htmlFor={`response-${query.id}`} className="block text-xs font-medium text-gray-700 mb-1">
                          Response
                        </label>
                        <Textarea
                          id={`response-${query.id}`}
                          placeholder="Enter your response to this clarification..."
                          value={responses[query.id] || ''}
                          onChange={(e) => handleResponseChange(query.id, e.target.value)}
                          className="min-h-20"
                          disabled={isSubmitting[query.id]}
                        />
                      </div>
                      
                      <div className="flex justify-end gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="border-green-200 text-green-700 hover:bg-green-50"
                          onClick={() => handleSubmitResponse(query.id, 'resolved')}
                          disabled={isSubmitting[query.id] || !responses[query.id]?.trim()}
                        >
                          <Check className="h-4 w-4 mr-1" /> Resolve
                        </Button>
                        
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="border-red-200 text-red-700 hover:bg-red-50"
                          onClick={() => handleSubmitResponse(query.id, 'rejected')}
                          disabled={isSubmitting[query.id] || !responses[query.id]?.trim()}
                        >
                          <X className="h-4 w-4 mr-1" /> Reject
                        </Button>
                      </div>
                    </div>
                  ) : null}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      ))}
    </div>
  );
};

export default ClarificationQueries;
