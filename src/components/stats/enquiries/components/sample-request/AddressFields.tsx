
import { Input } from "@/components/ui/input";

interface AddressFieldsProps {
  address: string;
  setAddress: (value: string) => void;
  city: string;
  setCity: (value: string) => void;
  country: string;
  setCountry: (value: string) => void;
  postalCode: string;
  setPostalCode: (value: string) => void;
}

const AddressFields = ({
  address,
  setAddress,
  city,
  setCity,
  country,
  setCountry,
  postalCode,
  setPostalCode
}: AddressFieldsProps) => {
  return (
    <>
      {/* Delivery Address */}
      <div>
        <label className="text-sm font-medium mb-1 block">
          Address <span className="text-red-500">*</span>
        </label>
        <Input 
          value={address}
          onChange={(e) => setAddress(e.target.value)}
          placeholder="Street address"
          required
        />
      </div>
      
      {/* City, Country, Postal Code */}
      <div className="grid grid-cols-3 gap-2">
        <div>
          <label className="text-sm font-medium mb-1 block">
            City <span className="text-red-500">*</span>
          </label>
          <Input 
            value={city}
            onChange={(e) => setCity(e.target.value)}
            placeholder="City"
            required
          />
        </div>
        <div>
          <label className="text-sm font-medium mb-1 block">
            Country <span className="text-red-500">*</span>
          </label>
          <Input 
            value={country}
            onChange={(e) => setCountry(e.target.value)}
            placeholder="Country"
            required
          />
        </div>
        <div>
          <label className="text-sm font-medium mb-1 block">
            Postal Code <span className="text-red-500">*</span>
          </label>
          <Input 
            value={postalCode}
            onChange={(e) => setPostalCode(e.target.value)}
            placeholder="Postal code"
            required
          />
        </div>
      </div>
    </>
  );
};

export default AddressFields;
