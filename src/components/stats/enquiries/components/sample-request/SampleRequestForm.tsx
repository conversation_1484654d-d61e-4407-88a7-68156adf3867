
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { submitSampleRequest, quantityUnits } from "./utils";
import CompactDocumentUpload from "@/components/enquiries/components/CompactDocumentUpload";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import FormControls from "../../components/FormControls";
import CustomAutoComplete from "./CustomAutoComplete"; // Import the custom component

interface SampleRequestFormProps {
  enquiryId: string;
  onClose: () => void;
  onSuccess?: () => void;
  statushistoryId?: string;
  sampleRequestDropdownData?: any;
}

const SampleRequestForm = ({ enquiryId, onClose, onSuccess, statushistoryId , sampleRequestDropdownData}: SampleRequestFormProps) => {
  const [quantity, setQuantity] = useState<number | "">("");
  const [quantityUnit, setQuantityUnit] = useState("Pound (lb)");
  // These fields were removed from the UI but kept in the state for API compatibility
  const city = "";
  const country = "";
  const postalCode = "";
  const [remarks, setRemarks] = useState("");
  const [address, setAddress] = useState("");
  const [samplePoc, setSamplePoc] = useState(""); // Sample POC field
  const [email, setEmail] = useState(""); // Email field
  const [phone, setPhone] = useState(""); // Phone field
  const [files, setFiles] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uniqueAddresses, setUniqueAddresses] = useState([]);
  const [uniquePhones, setUniquePhones] = useState([]);
  const [uniqueEmails, setUniqueEmails] = useState([]);
  const [uniqueSamplePOcs, setUniqueSamplePOcs] = useState([]);

   // ADD THIS useEffect TO PROCESS THE DATA:
  useEffect(() => {
  if (sampleRequestDropdownData && sampleRequestDropdownData.length > 0) {
    // Extract unique addresses as array of objects
    const addresses = [...new Set(sampleRequestDropdownData.map(item => item.delivery_address).filter(Boolean))]
      .map(address => ({ value: address, label: address }));
    setUniqueAddresses(addresses);

    // Extract unique phones as array of objects
    const phones = [...new Set(sampleRequestDropdownData.map(item => item.contact_phone).filter(Boolean))]
      .map(phone => ({ value: phone, label: phone }));
    setUniquePhones(phones);

    // Extract unique emails as array of objects
    const emails = [...new Set(sampleRequestDropdownData.map(item => item.contact_email).filter(Boolean))]
      .map(email => ({ value: email, label: email }));
    setUniqueEmails(emails);

    // Extract unique sales members as array of objects
    const salesMembers = [...new Set(sampleRequestDropdownData.map(item => item.sample_poc).filter(Boolean))]
      .map(member => ({ value: member, label: member }));
    setUniqueSamplePOcs(salesMembers);
  }
}, [sampleRequestDropdownData]);



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!quantity || !address || !samplePoc) {
      toast.error("Please fill all required fields");
      return;
    }

    setIsSubmitting(true);

    try {
      const success = await submitSampleRequest(enquiryId, statushistoryId, {
        quantity: quantity as number,
        quantityUnit,
        address, // Passing empty string for address as it's removed from the form
        city,
        country,
        postalCode,
        remarks,
        samplePoc,
        email,
        phone,
        files
      });

      if (success) {
        if (onSuccess) {
          onSuccess();
        }
        onClose();
      }
    } catch (error) {
      console.error("Error in sample request submission:", error);
      toast.error(`An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="p-4 bg-white/80 backdrop-blur-md border border-[#E5DEFF] rounded-lg">
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* First row: 5 fields */}
        <div className="flex flex-wrap gap-4 mb-4">
          {/* Quantity field */}
          <div className="space-y-1 w-full sm:w-[18%]">
            <Label htmlFor="quantity" className="font-medium text-base text-gray-800">Quantity <span className="text-red-500">*</span></Label>
            <Input
              id="quantity"
              type="number"
              min="0"
              step="0.01"
              value={quantity}
              onChange={(e) => setQuantity(e.target.value ? parseFloat(e.target.value) : "")}
              placeholder="Enter quantity"
              className="h-10 focus:ring-0 focus:ring-offset-0 focus:border-gray-300 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-gray-300"
              required
            />
          </div>

          {/* Unit field */}
          <div className="space-y-1 w-full sm:w-[18%]">
            <Label htmlFor="unit" className="font-medium text-base text-gray-800">Unit <span className="text-red-500">*</span></Label>
            <Select value={quantityUnit} onValueChange={setQuantityUnit}>
              <SelectTrigger id="unit" className="h-10">
                <SelectValue placeholder="Select unit" />
              </SelectTrigger>
              <SelectContent>
                {quantityUnits.map((unit) => (
                  <SelectItem key={unit} value={unit}>
                    {unit}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Sample POC field */}
          <div className="space-y-1 w-full sm:w-[18%] z-1000">
            <Label htmlFor="samplePoc" className="font-medium text-base text-gray-800">Sample POC <span className="text-red-500">*</span></Label>
            <CustomAutoComplete
              id="samplePoc"
              value={samplePoc}
              onChange={setSamplePoc}
              options={uniqueSamplePOcs}
              placeholder="Enter or select POC"
              required
            />
          </div>

          {/* Email field */}
          <div className="space-y-1 w-full sm:w-[18%]">
            <Label htmlFor="email" className="font-medium text-base text-gray-800">Email </Label>
             <CustomAutoComplete
              id="email"
              value={email}
              onChange={setEmail}
              options={uniqueEmails}
              placeholder="Enter or select email"
            />
          </div>

          {/* Phone field */}
          <div className="space-y-1 w-full sm:w-[18%]">
            <Label htmlFor="phone" className="font-medium text-base text-gray-800">Phone Number</Label>
            <CustomAutoComplete
              id="phone"
              value={phone}
              onChange={setPhone}
              options={uniquePhones}
              placeholder="Enter or select phone"
            />
          </div>
        </div>

        {/* Second row: Delivery Address (wider) */}
        <div className="flex flex-wrap gap-4 mb-4">
          {/* Delivery Address field */}
          <div className="space-y-1 w-full sm:w-[56.5%]">
            <Label htmlFor="address" className="font-medium text-base text-gray-800">Delivery Address <span className="text-red-500">*</span></Label>
            <CustomAutoComplete
              id="address"
              value={address}
              onChange={setAddress}
              options={uniqueAddresses}
              placeholder="Enter or select address"
              required
            />
          </div>

          {/* Remarks field */}
          <div className="space-y-1 w-full sm:w-[18%]">
            <Label htmlFor="remarks" className="font-medium text-base text-gray-800">Remarks</Label>
            <Input
              id="remarks"
              value={remarks}
              onChange={(e) => setRemarks(e.target.value)}
              placeholder="Any remarks"
              className="h-10 focus:ring-0 focus:ring-offset-0 focus:border-gray-300 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-gray-300"
            />
          </div>

          {/* Documents field */}
          <div className="space-y-1 w-full sm:w-[18%]">
            <Label className="font-medium text-base text-gray-800">Documents </Label>
            <CompactDocumentUpload
              onFilesChange={setFiles}
              files={files}
              multiple={true}
              className="min-h-[60px] h-auto pb-2"
            />
          </div>
        </div>

        {/* Form controls */}
        <FormControls
          onClose={onClose}
          isSubmitting={isSubmitting}
          submitLabel="Submit Request"
        />
      </form>
    </Card>
  );
};

export default SampleRequestForm;
