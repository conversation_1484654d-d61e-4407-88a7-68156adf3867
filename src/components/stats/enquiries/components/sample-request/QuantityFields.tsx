
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { quantityUnits } from "./utils";

interface QuantityFieldsProps {
  quantity: number | "";
  setQuantity: (value: number | "") => void;
  quantityUnit: string;
  setQuantityUnit: (value: string) => void;
}

const QuantityFields = ({
  quantity,
  setQuantity,
  quantityUnit,
  setQuantityUnit
}: QuantityFieldsProps) => {
  return (
    <div className="grid grid-cols-3 gap-2">
      <div className="col-span-2">
        <label className="text-sm font-medium mb-1 block">
          Quantity <span className="text-red-500">*</span>
        </label>
        <Input
          type="number"
min="0"
          min="0"
          step="0.01"
          value={quantity}
          onChange={(e) => setQuantity(e.target.value ? parseFloat(e.target.value) : "")}
          placeholder="Enter quantity"
          required
        />
      </div>
      <div>
        <label className="text-sm font-medium mb-1 block">
          Unit <span className="text-red-500">*</span>
        </label>
        <Select value={quantityUnit} onValueChange={setQuantityUnit}>
          <SelectTrigger>
            <SelectValue placeholder="Select unit" />
          </SelectTrigger>
          <SelectContent>
            {quantityUnits.map((unit) => (
              <SelectItem key={unit} value={unit}>
                {unit}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default QuantityFields;
