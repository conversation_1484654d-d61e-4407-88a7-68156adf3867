
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface RemarksFieldProps {
  remarks: string;
  setRemarks: (value: string) => void;
  required?: boolean;
  maxLength?: number;
}

const RemarksField = ({
  remarks,
  setRemarks,
  required = false,
  maxLength = 500
}: RemarksFieldProps) => {
  return (
    <div>
      <Label htmlFor="remarks" className="text-sm font-medium mb-1 block">
        Additional Remarks <span className="text-gray-400">(HSN Code, Customer Product Code CPC, Grade, etc.)</span>
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Input
        id="remarks"
        value={remarks}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setRemarks(e.target.value)}
        placeholder="Any special requirements or notes"
        className="h-10"
        maxLength={maxLength}
        required={required}
      />
      {maxLength && (
        <div className="text-xs text-gray-500 mt-1 text-right">
          {remarks.length} / {maxLength}
        </div>
      )}
    </div>
  );
};

export default RemarksField;
