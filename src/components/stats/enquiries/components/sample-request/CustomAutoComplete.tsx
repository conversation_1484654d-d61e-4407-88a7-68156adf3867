import React, { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";

interface Option {
  value: string;
  label: string;
}

interface CustomAutoCompleteProps {
  value: string;
  onChange: (value: string) => void;
  options: Option[];
  placeholder?: string;
  className?: string;
  required?: boolean;
  id?: string;
}

const CustomAutoComplete: React.FC<CustomAutoCompleteProps> = ({
  value,
  onChange,
  options,
  placeholder = "Type to search...",
  className = "",
  required = false,
  id,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filteredOptions, setFilteredOptions] = useState<Option[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Filter options based on input value
  useEffect(() => {
    if (isOpen) {
      if (value) {
        const filtered = options.filter((option) =>
          option.label.toLowerCase().includes(value.toLowerCase())
        );
        setFilteredOptions(filtered);
      } else {
        setFilteredOptions(options);
      }
    }
  }, [value, options, isOpen]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    onChange(inputValue);
    if (!isOpen) setIsOpen(true);
  };

  // Handle option selection
  const handleOptionSelect = (option: Option) => {
    onChange(option.value);
    setIsOpen(false);
    inputRef.current?.blur();
  };

  // Handle input focus
  const handleInputFocus = () => {
    setIsOpen(true);
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      <Input
        ref={inputRef}
        id={id}
        type="text"
        value={value}
        onChange={handleInputChange}
        onFocus={handleInputFocus}
        placeholder={placeholder}
        required={required}
        className="h-10 focus:ring-0 focus:ring-offset-0 focus:border-gray-300 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-gray-300"
        autoComplete="off"
      />

      {isOpen && filteredOptions.length > 0 && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
          {filteredOptions.length > 0 && (
            <div
              className="max-h-48 overflow-y-auto"
              style={{ maxHeight: "12rem" }}
            >
              {filteredOptions.map((option, index) => (
                <div
                  key={index}
                  className="px-3 py-1 cursor-pointer hover:bg-gray-100 text-gray-600"
                  style={{ fontSize: "14px" }}
                  onClick={() => handleOptionSelect(option)}
                >
                  {option.label}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CustomAutoComplete;
