
import { Button } from "@/components/ui/button";

interface FormActionsProps {
  onClose: () => void;
  isSubmitting: boolean;
}

const FormActions = ({ onClose, isSubmitting }: FormActionsProps) => {
  return (
    <div className="flex justify-end gap-2 pt-2">
      <Button 
        type="button" 
        variant="outline" 
        onClick={onClose}
        disabled={isSubmitting}
      >
        Cancel
      </Button>
      <Button 
        type="submit"
        disabled={isSubmitting}
      >
        {isSubmitting ? 
          <span className="flex items-center gap-1">
            <span className="h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></span>
            Submitting...
          </span> : 
          "Submit Request"
        }
      </Button>
    </div>
  );
};

export default FormActions;
