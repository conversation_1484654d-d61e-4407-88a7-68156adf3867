import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";
import { STORAGE_BUCKETS } from "@/integrations/supabase/client";

export const quantityUnits = [
  "Metric Ton (mt)",
  "Pound (lb)",
  "Gallon (gal)",
  "Li<PERSON> (L)",
  "Kilolitre (Kl)",
  "Kilogram (Kg)",
  "Bottles",
  "Packets",
  "Bags"
];

export interface SampleRequestFormData {
  quantity: number;
  quantityUnit: string;
  address: string;
  city: string;
  country: string;
  postalCode: string;
  remarks: string;
  samplePoc?: string; // Optional Sample Point of Contact
  email?: string; // Optional Email
  phone?: string; // Optional Phone
  files: File[];
}

export const submitSampleRequest = async (
  enquiryId: string,
  statushistoryId: string,
  formData: SampleRequestFormData
): Promise<boolean> => {
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      toast.error(sessionError ? sessionError.message : "You must be logged in to request a sample");
      return false;
    }

    // Fetch the enquiry data to get the sales team member
    const { data: enquiryData, error: enquiryError } = await supabase
      .from("enquiries")
      .select("sales_team_member")
      .eq("id", enquiryId)
      .maybeSingle();

      // const { error } = await supabase
      // .from("enquiries")
      // .update({
      //   current_status: "sample_requested",
      //   last_status_change: new Date().toISOString(),
      // })
      // .eq("id", enquiryId);

    if (enquiryError) {
      console.error("Error fetching enquiry data:", enquiryError);
      toast.error(`Failed to retrieve enquiry data: ${enquiryError.message}`);
      return false;
    }

    if (!enquiryData) {
      console.error("No enquiry data found for id:", enquiryId);
      toast.error("Enquiry not found");
      return false;
    }

    if (!enquiryData.sales_team_member) {
      console.error("Sales team member is null for enquiry:", enquiryId);
      toast.error("Sales team member information is missing for this enquiry");
      return false;
    }

    console.log("Debug - Sales team member:", enquiryData.sales_team_member);
    const salesEmail = enquiryData.sales_team_member;

    // Do all operations one by one with explicit error handling

    // 1. Create the sample request with explicit status set to "sample_requested"
    console.log("Creating sample request with sales team member:", salesEmail);
    console.log("###enq", statushistoryId);
    // Fix the status type by explicitly typing it as a valid enum value
    const status: Database["public"]["Enums"]["enquiry_lifecycle_status"] =
      "sample_requested";

    const historyData = {
      enquiry_id: enquiryId,
      status: status,
      changed_by: session.user.email,
      notes: `Sample requested with quantity: ${formData.quantity} ${formData.quantityUnit}`,
      sales_agent_email: salesEmail,
    };

    console.log("Status history entry to be inserted:", historyData);

    // const { data: insertedHistory, error: historyError } = await supabase
    //   .from("enquiry_status_history")
    //   .insert(historyData)
    //   .select("id") // Select the ID of the inserted row
    //   .single(); // Since only one row is inserted

    // if (historyError) {
    //   throw new Error(
    //     `Failed to create enquiry status history: ${historyError.message}`
    //   );
    // }

    const statusHistoryId = undefined; // Retrieve the generated ID

    const { data: sampleRequestNumber, error: sampleRequestNumberError } = await supabase
      .rpc("generate_sample_request_number", {
        p_enquiry_id:enquiryId
      })
      .single();

    if (sampleRequestNumberError) {
      throw new Error(
        `Failed to generate sample request number: ${sampleRequestNumberError.message}`
      );
    }

    // First create the sample request and get its ID
    const { error: insertError, data: sampleRequestData } = await supabase
      .from("sample_requests")
      .insert({
        id: sampleRequestNumber,
        enquiry_id: enquiryId,
        status_history_id: statusHistoryId,
        quantity: formData.quantity,
        quantity_unit: formData.quantityUnit,
        delivery_address: formData.address,
        delivery_city: formData.city,
        delivery_country: formData.country,
        delivery_postal_code: formData.postalCode,
        remarks: formData.remarks,
        sample_poc: formData.samplePoc,
        contact_email: formData.email, // Add the Email field
        contact_phone: formData.phone, // Add the Phone field
        created_by: session.user.id,
        sales_team_member: salesEmail,
        status: "sample_requested"
      })
      .select()
      .single();

    if (insertError) throw insertError;

    // Upload documents if any
    if (formData.files && formData.files.length > 0) {
      for (const file of formData.files) {
        const fileExt = file.name.split('.').pop();
        const fileName = `${crypto.randomUUID()}.${fileExt}`;
        const filePath = `sampleRequests/${sampleRequestData.id}/${fileName}`;

        // Upload file to storage
        const { error: uploadError } = await supabase.storage
          .from(STORAGE_BUCKETS.SAMPLE_DOCUMENTS)
          .upload(filePath, file);

        if (uploadError) {
          console.error('File upload error:', uploadError);
          toast.error(`Failed to upload ${file.name}: ${uploadError.message}`);
          continue;
        }

        // Save attachment metadata
        const { error: metadataError } = await supabase
          .from('sample_requests_attachments')
          .insert({
            sample_request_id: sampleRequestData.id,
            file_name: file.name,
            file_path: filePath,
            content_type: file.type,
            size: file.size
          });

        if (metadataError) {
          console.error('Error saving file metadata:', metadataError);
          toast.error(`Failed to save metadata for ${file.name}`);
        }
      }
    }

    // Then create the initial status history entry
    const { error: statusHistoryError } = await supabase
      .from("sample_status_history")
      .insert({
        sample_request_id: sampleRequestData.id,
        enquiry_id: enquiryId,
        sample_status: "sample_requested",
        changed_at: new Date().toISOString()
      });

    if (statusHistoryError) {
      console.error("Error creating sample status history:", statusHistoryError);
      toast.error(`Failed to create status history: ${statusHistoryError.message}`);
      return false;
    }

    console.log("Sample request created successfully");

    // 2. Update the enquiry status
    // console.log("Updating enquiry status");
    // const { error: updateError } = await supabase
    //   .from("enquiries")
    //   .update({
    //     current_status: "sample_requested",
    //     last_status_change: new Date().toISOString(),
    //     sample_requested: true,
    //     sample_requested_at: new Date().toISOString(),
    //   })
    //   .eq("id", enquiryId);

    // if (updateError) {
    //   console.error("Error updating enquiry status:", updateError);
    //   toast.error(`Failed to update enquiry status: ${updateError.message}`);
    //   return false;
    // }

    console.log("Enquiry status updated successfully");

    // 3. Create a status history entry - WITH DIRECT VALUE FOR SALES AGENT EMAIL
    console.log("Creating status history with sales_agent_email:", salesEmail);

    console.log("Status history created successfully");
    toast.success("Sample request submitted successfully");
    return true;
  } catch (error) {
    console.error("Error in sample request submission:", error);
    toast.error(`An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`);
    return false;
  }
};
