import { Download, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface DocumentViewerProps {
  documentUrl: string | null;
  documentName: string;
  className?: string;
}

export const DocumentViewer = ({ documentUrl, documentName, className }: DocumentViewerProps) => {
  if (!documentUrl) {
    return <div className={className}>Loading document...</div>;
  }

  return (
    <div className={className}>
      <Button
        variant="outline"
        size="sm"
        onClick={() => window.open(documentUrl, "_blank")}
        className="text-xs"
      >
        <Eye className="h-3.5 w-3.5 mr-1" /> View
      </Button>
      
      <a href={documentUrl} download={documentName}>
        <Button variant="outline" size="sm" className="text-xs">
          <Download className="h-3.5 w-3.5 mr-1" /> Download
        </Button>
      </a>
    </div>
  );
};
