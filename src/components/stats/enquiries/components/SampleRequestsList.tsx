import { SampleRequest } from "../hooks/useSampleRequests";
import SampleTrackingItem from "./timeline/SampleTrackingItem";
import { Beaker } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useSampleRequestStatus } from "../hooks/useSampleRequestStatus";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

interface SampleRequestsListProps {
  sampleRequests: SampleRequest[];
  enquiryId: string;
  formatTimestamp: (timestamp: string | null | undefined) => string;
  formatTime: (timestamp: string | null | undefined) => string;
  onRefresh: () => void;
  onRequestSample: () => void;
}

/**
 * Component to display a list of sample requests
 */
const SampleRequestsList = ({
  sampleRequests,
  enquiryId,
  formatTimestamp,
  formatTime,
  onRefresh,
  onRequestSample,
}: SampleRequestsListProps) => {
  console.log("SampleRequestsList rendering with:", {
    sampleRequestsCount: sampleRequests?.length || 0,
  },sampleRequests);

  // If there are no sample requests, show a message
  if (!sampleRequests || sampleRequests.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-10">
        <Beaker className="h-12 w-12 text-gray-300 mb-4" />
        <p className="text-gray-500 text-center">
          No sample requests have been raised for this enquiry yet.
        </p>
        <Button
          variant="outline"
          size="sm"
          className="mt-4 bg-purple-50 text-purple-700 hover:bg-purple-100 border-purple-200"
          onClick={onRequestSample}
        >
          <Beaker className="h-4 w-4 mr-1" />
          Raise a Sample Request
        </Button>
      </div>
    );
  }

  // Render the list of sample requests
  return (
    <div className="space-y-6">
      {sampleRequests.map((sampleRequest, index) => (
        <div className="mb-4" key={sampleRequest.id || `sample-${index}`}>
          <SampleTrackingItem
            item={sampleRequest}
            enquiryId={enquiryId || ""}
            enquiryStatusId={sampleRequest.id}
            status={sampleRequest.status || ""}
            formatTimestamp={formatTimestamp}
            formatTime={formatTime}
            onRefresh={onRefresh}
          />
        </div>
      ))}
    </div>
  );
};

export default SampleRequestsList;
