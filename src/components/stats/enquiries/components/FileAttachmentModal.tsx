import { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Eye, Download, X, FileIcon } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { supabase, STORAGE_BUCKETS } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { Database } from "@/integrations/supabase/types";

type AttachmentTableName = 
  | "quotation_feedback_attachments"
  | "sample_feedback_attachments"
  | "sample_requests_attachments";

type FeedbackType = "quotation" | "sample" | "sample_request";

interface FileAttachmentModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  type: "quotation" | "sample" | "sample_request";
  id: string;
}

const FileAttachmentModal = ({ isOpen, setIsOpen, type, id }: FileAttachmentModalProps) => {
  const [attachments, setAttachments] = useState<Array<{ id: string; name: string; url: string; file_path: string }>>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Function to handle file download
  const handleDownload = async (file: { id: string; name: string; file_path: string }, type: FeedbackType) => {
    try {
      const bucketName = type === "quotation" 
        ? STORAGE_BUCKETS.QUOTATION_DOCUMENTS 
        : type === "sample"
        ? STORAGE_BUCKETS.SAMPLE_DOCUMENTS
        : STORAGE_BUCKETS.SAMPLE_DOCUMENTS;

      const { data, error } = await supabase.storage
        .from(bucketName)
        .download(file.file_path);

      if (error) {
        console.error('Download error:', error);
        toast.error('Failed to download file');
        return;
      }

      // Create a blob URL and trigger download
      const blob = new Blob([data], { type: data.type });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = file.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('File downloaded successfully');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download file');
    }
  };

  useEffect(() => {
    const fetchAttachments = async () => {
      if (!id || !type) return;
      setIsLoading(true);

      try {
        const tableName = type === "quotation" 
          ? "quotation_feedback_attachments" 
          : type === "sample" 
          ? "sample_feedback_attachments"
          : "sample_requests_attachments";
        
        const bucketName = type === "quotation" 
          ? STORAGE_BUCKETS.QUOTATION_DOCUMENTS 
          : type === "sample"
          ? STORAGE_BUCKETS.SAMPLE_DOCUMENTS
          : STORAGE_BUCKETS.SAMPLE_DOCUMENTS;

        const idField = type === "quotation" 
          ? "feedback_id" 
          : type === "sample"
          ? "feedback_id"
          : "sample_request_id";

        const { data, error } = await supabase
          .from(tableName)
          .select("id, file_name, file_path")
          .eq(idField, id);

        if (error) throw error;
        if (!data || data.length === 0) {
          setAttachments([]);
          return;
        }

        const attachmentsWithUrls = data.map((item) => ({
          id: item.id,
          name: item.file_name,
          url: supabase.storage.from(bucketName).getPublicUrl(item.file_path).data?.publicUrl || "",
          file_path: item.file_path,
        }));

        setAttachments(attachmentsWithUrls);
      } catch (error) {
        console.error("Error fetching attachments:", error);
        toast.error("Failed to load attachments");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) fetchAttachments();
  }, [isOpen, id, type]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="max-w-md">
        <div className="flex justify-between items-center mb-4">
          <DialogTitle>Attachments</DialogTitle>
          {/* <Button variant="ghost" size="sm" className="h-7 w-7 p-0 rounded-full" onClick={() => setIsOpen(false)}>
            <X className="h-4 w-4" />
          </Button> */}
        </div>

        <DialogDescription className="text-sm text-gray-500">
          Click "Download" to save the file.
        </DialogDescription>

        <ScrollArea className="mt-4 max-h-[300px]">
          {isLoading && <p className="text-sm text-center py-2">Loading attachments...</p>}

          {!isLoading && attachments.length > 0 ? (
            attachments.map((file) => (
              <div key={file.id} className="flex justify-between items-center p-3 bg-gray-100 rounded-md">
                <div className="text-sm truncate flex-1 flex items-center">
                  <FileIcon className="w-4 h-4 mr-2" />
                  {file.name}
                </div>
                <div className="flex space-x-2">
                  {/* <a href={file.url} target="_blank" rel="noopener noreferrer">
                    <Button variant="outline" size="sm">
                      <Eye className="w-4 h-4 mr-1" /> View
                    </Button>
                  </a> */}
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleDownload(file, type)}
                  >
                    <Download className="w-4 h-4 mr-1" /> Download
                  </Button>
                </div>
              </div>
            ))
          ) : (
            !isLoading && <p className="text-sm text-center py-2 text-gray-500">No attachments found</p>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default FileAttachmentModal;
