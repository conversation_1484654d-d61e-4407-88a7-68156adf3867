import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileText, Plus, Paperclip } from "lucide-react";
import PurchaseOrderForm from "./PurchaseOrderForm";
import { motion } from "framer-motion";
import FileAttachmentModal from "../components/FileAttachmentModal";
import { supabase } from "@/integrations/supabase/client";

interface PurchaseOrderButtonProps {
  enquiryId: string;
  onSuccess?: () => void;
}

const PurchaseOrderButton = ({
  enquiryId,
  onSuccess,
}: PurchaseOrderButtonProps) => {
  const [showForm, setShowForm] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [status, setStatus] = useState("");

  const fetchEnquiryStatus = async () => {
    const { data, error } = await supabase
      .from("enquiries")
      .select("current_status")
      .eq("id", enquiryId)
      .single();

    if (error) {
      console.error("Failed to fetch current status:", error);
      throw error;
    }

    console.log("Fetched current status:", data?.current_status);
    setStatus(data?.current_status);
    return data?.current_status;
  };

  useEffect(() => {
    fetchEnquiryStatus();
  }, [showForm]);

  if (status === "po_raised") {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="mt-4" // Added margin-top here
    >
      {showForm ? (
        <PurchaseOrderForm
          enquiryId={enquiryId}
          onClose={() => setShowForm(false)}
          onSuccess={onSuccess}
        />
      ) : (
        <Card className="shadow-sm border border-green-100 overflow-hidden bg-gradient-to-br from-white to-green-50">
          <CardContent className="p-3">
            <div className="flex items-center justify-between mb-2">
              {status === "quote_accepted" && (
                <h5 className="text-sm font-medium text-green-700">
                  Quotation Accepted - Raise Purchase Order
                </h5>
              )}
              {status === "sample_accepted" && (
                <h5 className="text-sm font-medium text-green-700">
                  Sample Accepted - Raise Purchase Order
                </h5>
              )}
            </div>
            <Button
              onClick={() => setShowForm(true)}
              className="w-full bg-green-100 hover:bg-green-200 text-green-800 border border-green-200"
              variant="outline"
            >
              <Plus className="h-4 w-4 mr-2" />
              Raise Purchase Order
            </Button>
          </CardContent>
        </Card>
      )}
    </motion.div>
  );
};

export default PurchaseOrderButton;
