import React from "react";
import { User } from "lucide-react";

interface EnquiryAssignedItemProps {
  item: any;
  formatTimestamp: (timestamp: string | null | undefined) => string;
  formatTime: (timestamp: string | null | undefined) => string;
}

const EnquiryAssignedItem: React.FC<EnquiryAssignedItemProps> = ({
  item,
  formatTimestamp,
  formatTime,
}) => {
  return (
    <div className="flex gap-4">
      <div className="flex-none">
        <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
          <User className="h-4 w-4 text-purple-600" />
        </div>
      </div>
      <div className="flex-grow">
        <div className="flex justify-between">
          <div className="flex flex-col gap-1">
            <span className="text-base font-bold">Enquiry Assigned</span>
            <span className="text-sm text-gray-600">
              Assigned to: <span className="font-bold">{item?.procurement_poc}</span>
            </span>
          </div>
          <span className="text-sm text-gray-500">
            {formatTimestamp(item.created_at)} {formatTime(item.created_at)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default EnquiryAssignedItem;
