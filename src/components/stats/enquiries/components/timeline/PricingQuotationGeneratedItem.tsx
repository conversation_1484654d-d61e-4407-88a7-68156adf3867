import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>heck,
  ChevronUp,
  ChevronDown,
} from "lucide-react";
import QuotationFeedbackTable from "../../components/QuotationFeedbackTable";
import FileAttachmentModal from "../FileAttachmentModal";
import { Button } from "@/components/ui/button";

import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import SampleRequestForm from "../sample-request/SampleRequestForm";
import QuotationFeedbackForm from "../../QuotationFeedbackForm";
import { QuoteGenerationDisplay } from "./QuoteGenerationDisplay";

interface PricingQuotationGeneratedItemProps {
  item: any;
  enquiryId: string;
  enquiryStatusId: string;
  formatTimestamp: (timestamp: string | null | undefined) => string;
  formatTime: (timestamp: string | null | undefined) => string;
  quotationFeedback?: any;
  onRefresh?: () => void; // Optional callback for refreshing parent component
  currentStatus?: string;
  handleRequestNewQuotation?: (quoteGeneration) => void; // Callback for requesting new quotation
}

const PricingQuotationGeneratedItem: React.FC<
  PricingQuotationGeneratedItemProps
> = ({
  item,
  enquiryId,
  enquiryStatusId,
  formatTimestamp,
  formatTime,
  quotationFeedback,
  onRefresh,
  currentStatus,
  handleRequestNewQuotation
}) => {
  const [hasFeedbackBeenSubmitted, setHasFeedbackBeenSubmitted] = useState(false);
  const [localFeedback, setLocalFeedback] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [quoteGenerationId, setQuoteGenerationId] = useState<string | null>(
    null
  );
  const [isQuotationExpanded, setIsQuotationExpanded] = useState(false);
  const [isFeedbackExpanded, setIsFeedbackExpanded] = useState(false);
  const [isFeedbackFormExpanded, setIsFeedbackFormExpanded] = useState(false);
  const [isDocumentsModalOpen, setIsDocumentsModalOpen] = useState(false);
  const [showSampleRequestForm, setShowSampleRequestForm] = useState(false); // Closed by default
  const [isSampleRequested, setIsSampleRequested] = useState(false);
  const [isLatestQuotation, setIsLatestQuotation] = useState(false);
  const [anyQuoteExpired, setAnyQuoteExpired] = useState(false); // State for anyExpired from child


  console.log("PricingQuotationGeneratedItem rendering with:", {
    enquiryId,
    enquiryStatusId,
    quotationFeedback,
    localFeedback,
    isLoading,
    quoteGenerationId,
    isQuotationExpanded,
    isFeedbackFormExpanded,
    isDocumentsModalOpen,
    currentStatus,
    isLatestQuotation,
    anyQuoteExpired, // Include new state in log
  });

  useEffect(() => {
    // Check if this is the latest price_quotation_generated entry for this enquiry
    const checkIfLatestQuotation = async () => {
      if (!enquiryId || !enquiryStatusId) return;

      try {
        // Get all price_quotation_generated entries for this enquiry, ordered by created_at desc
        const { data, error } = await supabase
          .from("enquiry_status_history")
          .select("id, created_at")
          .eq("enquiry_id", enquiryId)
          .eq("status", "pricing_quotation_generated")
          .order("created_at", { ascending: false });

        if (error) {
          console.error("Error checking if latest quotation:", error);
          return;
        }

        // If this is the first/most recent entry, set isLatestQuotation to true
        if (data && data.length > 0) {
          const isLatest = data[0].id === enquiryStatusId;
          console.log(`Quotation ${enquiryStatusId} is ${isLatest ? 'the latest' : 'not the latest'} quotation`);
          setIsLatestQuotation(isLatest);
        }
      } catch (error) {
        console.error("Error in checkIfLatestQuotation:", error);
      }
    };

    // Otherwise, fetch feedback from the database
    const fetchQuotationFeedback = async () => {
      if (!enquiryStatusId) return;

      setIsLoading(true);
      try {
        console.log(
          "Fetching quotation feedback for status ID:",
          enquiryStatusId
        );

        // First get the quote generation details
        const { data: quoteData, error: quoteError } = await supabase
          .from("quote_generation_details")
          .select("id")
          .eq("status_history_id", enquiryStatusId)
          .single();

        console.log("Quote generation details response:", {
          quoteData,
          enquiryStatusId,
          quoteError,
        });

        // Now fetch the feedback
        const { data: feedbackData, error: feedbackError } = await supabase
          .from("quotation_feedback")
          .select("id, created_at, reason, response, remarks")
          .eq("pricing_quote_id", enquiryStatusId)
          .single();

        console.log("Feedback response:", { feedbackData, feedbackError });

        if (feedbackError && feedbackError.code !== "PGRST116") {
          console.error("Error fetching quotation feedback:", feedbackError);
        } else if (feedbackData) {
          console.log("Found quotation feedback:", feedbackData);
          setLocalFeedback(feedbackData);
        }

        if (quoteError) {
          console.error("Error fetching quote generation details:", quoteError);
          return;
        }

        if (quoteData?.id) {
          setQuoteGenerationId(quoteData.id);
          console.log("Found quote generation ID:", quoteData.id);
        }
      } catch (error) {
        console.error("Error in fetchQuotationFeedback:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkIfLatestQuotation();
    fetchQuotationFeedback();
  }, [enquiryId, enquiryStatusId, quotationFeedback]);

  // // Check if sample has already been requested
  // useEffect(() => {
  //   const checkSampleRequested = async () => {
  //     if (!enquiryId) return;

  //     try {
  //       const { data, error } = await supabase
  //         .from("sample_requests")
  //         .select("id")
  //         .eq("enquiry_id", enquiryId)
  //         .maybeSingle();

  //       if (error) {
  //         console.error("Error checking sample request status:", error);
  //       } else if (data) {
  //         setIsSampleRequested(true);
  //       }
  //     } catch (error) {
  //       console.error("Exception checking sample status:", error);
  //     }
  //   };

  //   checkSampleRequested();
  // }, [enquiryId]);

  // Use localFeedback if available, otherwise fall back to quotationFeedback from props
  const feedbackData = localFeedback;

  // Calculate Request for New Quotation button visibility and enabled state
  // Button is shown only for the latest expired quotation.
  const shouldShowNewQuotationButton = isLatestQuotation && anyQuoteExpired;

  // Button is enabled if shown and the current status is not quote_revision_needed or quote_redo.
  const shouldEnableNewQuotationButton = shouldShowNewQuotationButton && 
    currentStatus !== 'quote_revision_needed' && 
    currentStatus !== 'quote_redo';

  // Handle feedback form success
  const handleFeedbackSuccess = async () => {
    console.log("Feedback submission successful, refreshing data...");
    // Refetch the feedback data
    setIsFeedbackFormExpanded(false);
    setHasFeedbackBeenSubmitted(true);

    // Fetch feedback from the database
    if (!enquiryStatusId) return;

    setIsLoading(true);
    try {
      // First get the quote generation details
      const { data: quoteData, error: quoteError } = await supabase
        .from("quote_generation_details")
        .select("id")
        .eq("status_history_id", enquiryStatusId)
        .single();

      if (quoteError) {
        console.error("Error fetching quote generation details:", quoteError);
        return;
      }

      if (quoteData?.id) {
        setQuoteGenerationId(quoteData.id);

        // Now fetch the feedback
        const { data: feedbackData, error: feedbackError } = await supabase
          .from("quotation_feedback")
          .select("id, created_at, reason, response, remarks")
          .eq("pricing_quote_id", enquiryStatusId)
          .single();

        if (feedbackError && feedbackError.code !== "PGRST116") {
          console.error("Error fetching quotation feedback:", feedbackError);
        } else if (feedbackData) {
          setLocalFeedback(feedbackData);
          setIsFeedbackExpanded(true);
        }
      }
    } catch (error) {
      console.error("Error in fetchQuotationFeedback:", error);
    } finally {
      setIsLoading(false);

      // Call the onRefresh callback if provided
      if (onRefresh) {
        console.log("Calling onRefresh callback after feedback submission");
        onRefresh();
      }
    }
  };

  return (
    <div className="flex gap-4">
      <div className="flex-none">
        <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
          <FileCheck className="h-4 w-4 text-indigo-600" />
        </div>
      </div>
      <div className="flex-grow">
        <div className="flex justify-between">
          <h2 className="text-base font-bold">Pricing Quotation Generated</h2>
          <span className="text-sm text-gray-500">
            {formatTimestamp(item.created_at)} {formatTime(item.created_at)}
          </span>
        </div>

        <div className="flex items-center mt-1 gap-2 flex-wrap">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center justify-between text-sm font-semibold text-gray-600 hover:text-gray-900 py-2 px-3 border border-gray-200 rounded-md bg-white hover:bg-gray-50 transition-colors"
            onClick={() => setIsQuotationExpanded(!isQuotationExpanded)}
          >
            Quotation Details
            {isQuotationExpanded ? (
              <ChevronUp className="h-3 w-3" />
            ) : (
              <ChevronDown className="h-3 w-3" />
            )}
          </Button>

          {feedbackData ? (
            <Button
              variant="outline"
              size="sm"
              className="flex items-center justify-between text-sm font-semibold text-gray-600 hover:text-gray-900 py-2 px-3 border border-gray-200 rounded-md bg-white hover:bg-gray-50 transition-colors"
              onClick={() => setIsFeedbackExpanded(!isFeedbackExpanded)}
            >
              <span>Quotation Feedback</span>
              {isFeedbackExpanded ? (
                <ChevronUp className="h-3 w-3" />
              ) : (
                <ChevronDown className="h-3 w-3" />
              )}
            </Button>
          ) : (
            currentStatus !== "quote_revision_needed" ? (
              <Button
                variant="outline"
                size="sm"
                className="flex items-center justify-between text-sm font-semibold text-gray-600 hover:text-gray-900 py-2 px-3 border border-gray-200 rounded-md bg-white hover:bg-gray-50 transition-colors"
                onClick={() => setIsFeedbackFormExpanded(!isFeedbackFormExpanded)}
              >
                Provide Quotation Feedback
                {isFeedbackFormExpanded ? (
                  <ChevronUp className="h-3 w-3" />
                ) : (
                  <ChevronDown className="h-3 w-3" />
                )}
              </Button>
            ) : (
              <Button
                variant="outline"
                size="sm"
                className="flex items-center justify-between text-sm font-semibold text-gray-400 py-2 px-3 border border-gray-200 rounded-md bg-gray-50 cursor-not-allowed"
                disabled={true}
              >
                Provide Quotation Feedback
                <ChevronDown className="h-3 w-3" />
              </Button>
            )
          )}

          {/* {feedbackData &&
            currentStatus === "quote_accepted" &&
            !isSampleRequested &&
            feedbackData.response === "quote_accepted" && (
              <Button
                variant="outline"
                size="sm"
                className="flex items-center justify-between text-sm font-semibold text-gray-600 hover:text-gray-900 py-2 px-3 border border-gray-200 rounded-md bg-white hover:bg-gray-50 transition-colors"
                onClick={() => setShowSampleRequestForm(!showSampleRequestForm)}
              >
                Request Sample
                {showSampleRequestForm ? (
                  <ChevronUp className="h-3 w-3" />
                ) : (
                  <ChevronDown className="h-3 w-3" />
                )}
              </Button>
            )} */}
        </div>

        {/* Quotation Feedback Display */}
        {isFeedbackExpanded && feedbackData && (
          <div className="mt-2">
            <QuotationFeedbackTable
              feedbackData={feedbackData}
              onOpenAttachments={() => setIsDocumentsModalOpen(true)}
            />
          </div>
        )}

        {/* Display quote generation details when expanded */}
        {isQuotationExpanded && (
          <QuoteGenerationDisplay
            statusChangeId={enquiryStatusId}
            isActive={true} // This prop is not used in QuoteGenerationDisplay anymore for button logic
            onRefresh={onRefresh}
            showRequestNewQuotationButton={shouldShowNewQuotationButton}
            enableRequestNewQuotationButton={shouldEnableNewQuotationButton}
            onAnyExpiredChange={setAnyQuoteExpired} // Pass the setter function
            handleRequestNewQuotation={handleRequestNewQuotation}
          />
        )}

        {/* Expanded feedback form */}
        {!feedbackData && isFeedbackFormExpanded && currentStatus !== "quote_revision_needed" && (
          <div className="mt-3">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-bold text-gray-700">
                Quotation Feedback:
              </h3>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0 rounded-full hover:bg-gray-100"
                onClick={() => setIsFeedbackFormExpanded(false)}
              >
                <ChevronUp className="h-4 w-4 text-gray-500" />
              </Button>
            </div>
            <QuotationFeedbackForm
              enquiryId={enquiryId}
              enquiryStatusId={enquiryStatusId}
              onClose={() => setIsFeedbackFormExpanded(false)}
              onSuccess={handleFeedbackSuccess}
            />
          </div>
        )}

        {/* Documents Modal */}
        {feedbackData && feedbackData.id && (
          <FileAttachmentModal
            isOpen={isDocumentsModalOpen}
            setIsOpen={setIsDocumentsModalOpen}
            id={feedbackData.id}
            type="quotation"
          />
        )}

        {/* Sample Request Form */}
        {/* {showSampleRequestForm && (
          <div className="mt-3">
            <SampleRequestForm
              enquiryId={enquiryId}
              statushistoryId={enquiryStatusId}
              onClose={() => setShowSampleRequestForm(false)}
              onSuccess={() => {
                console.log(
                  "Sample request submitted successfully, refreshing data..."
                );
                setShowSampleRequestForm(false);
                setIsSampleRequested(true);
                toast.success("Sample request submitted successfully");

                // Call the onRefresh callback if provided
                if (onRefresh) {
                  console.log(
                    "Calling onRefresh callback after sample request submission"
                  );
                  onRefresh();
                }
              }}
            />
          </div>
        )} */}
      </div>
    </div>
  );
};

export default PricingQuotationGeneratedItem;
