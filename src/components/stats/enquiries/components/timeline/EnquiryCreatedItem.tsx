import React from 'react';
import { FileText } from 'lucide-react';

interface EnquiryCreatedItemProps {
  item: any;
  formatTimestamp: (timestamp: string | null | undefined) => string;
  formatTime: (timestamp: string | null | undefined) => string;
}

const EnquiryCreatedItem: React.FC<EnquiryCreatedItemProps> = ({
  item,
  formatTimestamp,
  formatTime
}) => {
  return (
    <div className="flex gap-4">
      <div className="flex-none">
        <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
          <FileText className="h-4 w-4 text-gray-600" />
        </div>
      </div>
      <div className="flex-grow">
        <div className="flex justify-between">
          <div className="flex flex-col gap-1">
            <span className="text-base font-bold">Enquiry Created</span>
            <span className="text-sm text-gray-600">
              Sales POC: <span className="font-bold">{item.sales_agent_email}</span>
            </span>
          </div>
          <span className="text-sm text-gray-500">
            {formatTimestamp(item.created_at)} {formatTime(item.created_at)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default EnquiryCreatedItem;
