import { format } from 'date-fns';
import { <PERSON><PERSON>, Check, Paperclip, Eye, Download, FileIcon, FilePlus } from 'lucide-react';
import { useEffect, useState } from 'react';
import { supabase, STORAGE_BUCKETS } from "@/integrations/supabase/client";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import clsx from "clsx";

interface QuoteGenerationDisplayProps {
  statusChangeId: string;
  isActive?: boolean;
  onRefresh?: () => void; // Callback to refresh the parent component
  showRequestNewQuotationButton: boolean;
  enableRequestNewQuotationButton: boolean;
  onAnyExpiredChange: (anyExpired: boolean) => void; // Callback to pass the anyExpired state up
  handleRequestNewQuotation: (quoteGeneration) => void; // Callback to handle request new quotation
}

export const QuoteGenerationDisplay = ({
  statusChangeId,
  isActive = true,
  onRefresh,
  showRequestNewQuotationButton,
  enableRequestNewQuotationButton,
  onAnyExpiredChange,
  handleRequestNewQuotation
}: QuoteGenerationDisplayProps) => {
  const [quoteGeneration, setQuoteGeneration] = useState<any>(null);
  const [quoteOptions, setQuoteOptions] = useState<any[]>([]);
  const [additionalDocuments, setAdditionalDocuments] = useState<any[]>([]);
  const [isDocumentsModalOpen, setIsDocumentsModalOpen] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  // Function to check if expiry date is expired
  const isExpired = (expiryDate: string | null | undefined) => {
    if (!expiryDate) return false;
    const today = new Date();
    const exp = new Date(expiryDate);
    exp.setHours(23, 59, 59, 999);
    return exp < today;
  };

  // Check if any quote option is expired
  const anyExpired = quoteOptions.some(option => option.expiry_date && isExpired(option.expiry_date));

  // Inform parent about the anyExpired state
  useEffect(() => {
    onAnyExpiredChange(anyExpired);
  }, [anyExpired, onAnyExpiredChange]);

  // Handler for "Request for New Quotation"
  // const handleRequestNewQuotation = async() => {
  //   // TODO: Replace with your actual logic/modal for requesting a new quotation
  //   if (!quoteGeneration?.enquiry_id) {
  //   toast.error("No enquiry ID found for this quotation.");
  //   return;
  // }

  // try {
  //   // Get current timestamp in UTC (ISO format)
  //   const currentTimestamp = new Date().toISOString();

  //   // 1. Update enquiries table with new status and timestamp
  //   const { error: enquiryError } = await supabase
  //     .from("enquiries")
  //     .update({
  //       current_status: "quote_revision_needed",
  //       last_status_change: currentTimestamp
  //     })
  //     .eq("id", quoteGeneration.enquiry_id);

  //   if (enquiryError) throw enquiryError;

  //    // 2. Fetch latest procurement_poc from status history
  //    const { data: lastHistory, error: lastHistoryError } = await supabase
  //    .from("enquiry_status_history")
  //    .select("sales_agent_email, procurement_poc")
  //    .eq("enquiry_id", quoteGeneration.enquiry_id)
  //    .order("created_at", { ascending: false })
  //    .limit(1)
  //    .single();

  //    if (lastHistoryError) throw lastHistoryError;

  //   // 2. Insert into enquiry_status_history
  //   const { data: sessionData } = await supabase.auth.getSession();
  //   const userId = sessionData?.session?.user?.id || "";

  //   const { error: historyError } = await supabase
  //     .from("enquiry_status_history")
  //     .insert({
  //       enquiry_id: quoteGeneration.enquiry_id,
  //       status: "quote_revision_needed",
  //       changed_by: userId,
  //       sales_agent_email: lastHistory?.sales_agent_email || null,
  //       procurement_poc: lastHistory?.procurement_poc || null,
  //       notes: "Requested new quotation due to expiry.",
  //     });

  //   if (historyError) throw historyError;

  //   toast.success("Quote Revision has been requested to Sourcing Team.");

  //   // Call the onRefresh callback if provided to update the UI without page refresh
  //   if (onRefresh) {
  //     onRefresh();
  //   }
  // } catch (err) {
  //   toast.error("Failed to update enquiry status.");
  //   console.error(err);
  // }
  // };

  // Function to generate HTML content for copying
  const generateHtmlContent = () => {
    if (!quoteGeneration || !quoteOptions.length) return '';

    // Check if any quote option has CAS or Expiry Date for HTML generation
    const hasCasForHtml = quoteOptions.some(option => option.cas && option.cas !== '-');
    const hasExpiryDateForHtml = quoteOptions.some(option => option.expiry_date);

    // Generate table HTML - with minimal spacing
    const tableHtml = `<table style="width:100%; border-collapse: collapse; margin-bottom: 10px; font-family: Arial, sans-serif;">
<thead>
<tr style="background-color: #f3f4f6;">
<th style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">Customer Name</th>
<th style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">Product</th>
<th style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">Packaging</th>
<th style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">Price/Unit</th>
<th style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">Total Quantity</th>
<th style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">Amount</th>
<th style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">PO to Delivery Time</th>
${hasCasForHtml ? '<th style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">CAS</th>' : ''}
${hasExpiryDateForHtml ? '<th style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">Expiry Date</th>' : ''}
</tr>
</thead>
<tbody>

${quoteOptions.map((option, index) => {
  const expired = option.expiry_date && isExpired(option.expiry_date);
  return `<tr style="${expired ? 'background-color: #fee2e2;' : (index % 2 === 0 ? '' : 'background-color: #f9fafb;')}">
<td style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">${option.customer_name || ''}</td>
<td style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">${option.product_name || ''}</td>
<td style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">${option.packaging_name || ''}</td>
<td style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">${option.price || ''} ${option.currency || ''}/${option.unit || ''}</td>
<td style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">${option.quantity || ''} ${option.unit || ''}</td>
<td style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">${option.amount || ''} ${option.currency || ''}</td>
<td style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">${option.po_to_delivery_time || ''}</td>
${hasCasForHtml ? `<td style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">${option.cas || '-'}</td>` : ''}
${hasExpiryDateForHtml ? `<td style="padding: 8px; text-align: left; border: 1px solid #e5e7eb;">${option.expiry_date ? format(new Date(option.expiry_date), 'MMM d, yyyy') : '-'}</td>` : ''}
</tr>`;
}).join('')}
</tbody>
</table>`;

    // Generate terms HTML - with minimal spacing and matching UI layout
    const termsHtml = `<div style="padding: 12px; background-color: white; border-radius: 8px; border: 1px solid #e5e7eb; margin-top: 10px; font-family: Arial, sans-serif;">
<div style="display: flex; flex-direction: column; gap: 12px;">
<div style="display: flex; align-items: flex-start; gap: 8px;">
<div>
<span style="font-size: 14px; color: #6b7280; font-weight: 500;">Inco Terms:</span>
</div>
<div>
<span style="font-size: 14px; font-weight: 500;">${quoteGeneration.inco_terms || '-'}</span>
</div>
</div>
<div style="display: flex; align-items: flex-start; gap: 8px;">
<div>
<span style="font-size: 14px; color: #6b7280; font-weight: 500;">Payment Terms:</span>
</div>
<div>
<span style="font-size: 14px; font-weight: 500;">${quoteGeneration.payment_terms || '-'}</span>
</div>
</div>
</div>`;

    return tableHtml + termsHtml;
  };

  // Function to handle copy to clipboard
  const handleCopyToClipboard = async () => {
    const htmlContent = generateHtmlContent();

    try {
      // Try to use the modern Clipboard API with HTML support
      if (navigator.clipboard && typeof ClipboardItem !== 'undefined') {
        await navigator.clipboard.write([
          new ClipboardItem({
            'text/html': new Blob([htmlContent], { type: 'text/html' }),
            'text/plain': new Blob([htmlContent], { type: 'text/plain' })
          })
        ]);
      } else {
        // Fallback for browsers that don't support ClipboardItem
        // Create a temporary textarea element
        const textarea = document.createElement('textarea');
        textarea.value = htmlContent;
        textarea.style.position = 'fixed';
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);
      }

      setIsCopied(true);
      toast.success('Quotation copied to clipboard!');

      // Reset the copied state after 2 seconds
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
      toast.error('Failed to copy. Please try again.');
    }
  };



  // Function to handle opening the documents modal
  const handleOpenDocumentsModal = () => {
    setIsDocumentsModalOpen(true);
  };

  // Function to handle file download
  const handleDownload = async (file: any) => {
    try {
      const { data, error } = await supabase.storage
        .from(STORAGE_BUCKETS.QUOTATION_DOCUMENTS)
        .download(file.file_path);

      if (error) {
        console.error('Download error:', error);
        toast.error('Failed to download file');
        return;
      }

      // Create a blob URL and trigger download
      const blob = new Blob([data], { type: data.type });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = file.file_name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('File downloaded successfully');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download file');
    }
  };

  useEffect(() => {
    const fetchQuoteGenerationDetails = async () => {
      try {
        // Fetch quote generation details
        const { data, error } = await supabase
          .from('quote_generation_details')
          .select('*')
          .eq('status_history_id', statusChangeId)
          .single();

        if (error) throw error;
        setQuoteGeneration(data);

        // Fetch quote options
        const { data: optionsData, error: optionsError } = await supabase
          .from('quote_generation_options')
          .select('*')
          .eq('quote_generation_id', data.id);

        if (optionsError) throw optionsError;
        setQuoteOptions(optionsData || []);

        // Fetch additional documents
        const { data: documentsData, error: documentsError } = await supabase
          .from('quote_generation_attachments')
          .select('*')
          .eq('quote_generation_id', data.id);

        console.log("Documents Data:", documentsData, documentsError);

        if (documentsError) throw documentsError;
        setAdditionalDocuments(documentsData || []);
      } catch (error) {
        console.error("Error fetching quote generation details:", error);
      }
    };

    fetchQuoteGenerationDetails();
  }, [statusChangeId]);

  if (!quoteGeneration) return null;

  // Check if any quote option has CAS or Expiry Date
  const hasCasNumber = quoteOptions.some(option => option.cas && option.cas !== '-');
  const hasExpiryDate = quoteOptions.some(option => option.expiry_date);

  return (
    <div className="mt-2">
      {/* Custom Documents Modal */}
      <Dialog open={isDocumentsModalOpen} onOpenChange={setIsDocumentsModalOpen}>
        <DialogContent className="max-w-md">
          <div className="flex justify-between items-center mb-4">
            <DialogTitle>Quotation Attachments</DialogTitle>
          </div>

          <DialogDescription className="text-sm text-gray-500">
            Click "Download" to save the file.
          </DialogDescription>

          <ScrollArea className="mt-4 max-h-[300px]">
            {additionalDocuments.length > 0 ? (
              additionalDocuments.map((file) => {
                const fileUrl = supabase.storage
                  .from(STORAGE_BUCKETS.QUOTATION_DOCUMENTS)
                  .getPublicUrl(file.file_path).data?.publicUrl || "";

                return (
                  <div key={file.id} className="flex justify-between items-center p-3 bg-gray-100 rounded-md mb-2">
                    <div className="text-sm truncate flex-1 flex items-center">
                      <FileIcon className="w-4 h-4 mr-2" />
                      {file.file_name}
                    </div>
                    <div className="flex space-x-2">
                      {/* <a href={fileUrl} target="_blank" rel="noopener noreferrer">
                        <Button variant="outline" size="sm">
                          <Eye className="w-4 h-4 mr-1" /> View
                        </Button>
                      </a> */}
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleDownload(file)}
                      >
                        <Download className="w-4 h-4 mr-1" /> Download
                      </Button>
                    </div>
                  </div>
                );
              })
            ) : (
              <p className="text-sm text-center py-2 text-gray-500">No attachments found</p>
            )}
          </ScrollArea>
        </DialogContent>
      </Dialog>

      <div className="bg-gray-50 rounded-lg border border-gray-100 overflow-hidden">
        <div className="px-4 py-2 bg-gray-100/50 flex justify-between items-center">
          <h4 className="text-sm font-medium text-gray-700">Generated Quotation Details</h4>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="h-8 text-xs font-medium flex items-center gap-1.5 bg-white"
              onClick={handleCopyToClipboard}
            >
              {isCopied ? (
                <>
                  <Check className="h-3.5 w-3.5" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="h-3.5 w-3.5" />
                  Copy
                </>
              )}
            </Button>
            {showRequestNewQuotationButton && (
              <Button
                variant="outline"
                size="sm"
                className="h-8 text-xs font-medium flex items-center gap-1.5 bg-white"
                onClick={()=>handleRequestNewQuotation(quoteGeneration)}
                disabled={!enableRequestNewQuotationButton}
              >
                <FilePlus className="h-3.5 w-3.5" />
                Request for New Quotation
              </Button>
            )}
          </div>
        </div>
        <div className="p-4">
          <div className="space-y-6">

            {/* Quotation Options Table */}
            <div className="overflow-x-auto">
              <table className="w-full text-sm border border-gray-200 rounded-lg">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="p-2 text-left font-medium border-r">Customer Name</th>
                    <th className="p-2 text-left font-medium border-r">Product</th>
                    <th className="p-2 text-left font-medium border-r">Packaging</th>
                    <th className="p-2 text-left font-medium border-r">Price/Unit</th>
                    <th className="p-2 text-left font-medium border-r">Total Quantity</th>
                    <th className="p-2 text-left font-medium border-r">Amount</th>
                    <th className="p-2 text-left font-medium border-r">PO to Delivery Time</th>
                    {hasCasNumber && <th className="p-2 text-left font-medium border-r">CAS</th>}
                    {hasExpiryDate && <th className="p-2 text-left font-medium">Expiry Date</th>}
                  </tr>
                </thead>
                <tbody>
                  {quoteOptions.map((option, index) => (
                    <tr key={option.id || index}
                      className={clsx(
                        option.expiry_date && isExpired(option.expiry_date) ? "bg-red-100" : "",
                        "transition-colors"
                      )}
                    >
                      <td className="p-2 border-r border-t">{option.customer_name}</td>
                      <td className="p-2 border-r border-t">{option.product_name}</td>
                      <td className="p-2 border-r border-t">{option.packaging_name}</td>
                      <td className="p-2 border-r border-t">
                        {option.price} {option.currency}/{option.unit}
                      </td>
                      <td className="p-2 border-r border-t">
                        {option.quantity} {option.unit}
                      </td>
                      <td className="p-2 border-r border-t">
                        {option.amount} {option.currency}
                      </td>
                      <td className="p-2 border-r border-t">{option.po_to_delivery_time}</td>
                      {hasCasNumber && <td className="p-2 border-r border-t">{option.cas || '-'}</td>}
                      {hasExpiryDate && <td className="p-2 border-t">
                        {option.expiry_date ? format(new Date(option.expiry_date), 'MMM d, yyyy') : '-'}
                      </td>}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Common Fields */}
            <div className="bg-white p-3 rounded-lg border border-gray-200">
              <div className="flex flex-col gap-3">
                <div className="flex items-start gap-2">
                  <div>
                    <span className="text-sm text-gray-500 font-medium">Inco Terms:</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium">{quoteGeneration.inco_terms || '-'}</span>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <div>
                    <span className="text-sm text-gray-500 font-medium">Payment Terms:</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium">{quoteGeneration.payment_terms || '-'}</span>
                  </div>
                </div>
                {additionalDocuments && additionalDocuments.length > 0 && (
                  <div className="flex items-start gap-2">
                    <div>
                      <button
                        className="flex items-center gap-1.5 text-sm text-gray-500 font-medium hover:text-gray-700"
                        onClick={handleOpenDocumentsModal}
                      >
                        <Paperclip className="h-4 w-4" />
                        Attachments ({additionalDocuments.length})
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};