import React, { useState, useEffect } from "react";
import {
  Package,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import SampleRequestTable from "../../components/SampleRequestTable";
import TrackingDetailsTable from "../../components/TrackingDetailsTable";
import SampleFeedbackTable from "../../components/SampleFeedbackTable";
import { supabase } from "@/integrations/supabase/client";
// Removed unused date-fns import
import SampleFeedbackForm from "../SampleFeedbackForm";
import { useSampleRequestStatus } from "../../hooks/useSampleRequestStatus";
import { useQuery } from "@tanstack/react-query";
import FileAttachmentModal from "../FileAttachmentModal";

interface SampleTrackingItemProps {
  item: any; // The sample_requested status history item
  enquiryId: string;
  status: string;
  enquiryStatusId: string;
  formatTimestamp: (timestamp: string | null | undefined) => string;
  formatTime: (timestamp: string | null | undefined) => string;
  onRefresh?: () => void; // Optional callback for refreshing parent component
}

const tableViewStates = [
  "sample_accepted",
  "sample_rejected",
  "sample_redo",
]

const SampleTrackingItem: React.FC<SampleTrackingItemProps> = ({
  item: _item, // Unused but required by interface
  enquiryId,
  status: _status, // Unused but required by interface
  enquiryStatusId,
  formatTimestamp,
  formatTime,
  onRefresh,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [sampleFeedback, setSampleFeedback] = useState<any>(null);
  const [isFeedbackExpanded, setIsFeedbackExpanded] = useState(false);
  const [isDocumentsModalOpen, setIsDocumentsModalOpen] = useState(false);
  const [isAttachmentsOpen, setIsAttachmentsOpen] = useState(false);
  const [isTrackingExpanded, setIsTrackingExpanded] = useState(false);
  const [isTrackingTimeline, setIsTrackingTimeline] = useState(false);

  const sampleRequestData = _item;

  // const { data: sampleRequestData, isLoading: isLoadingSampleStatus } =
  //   useSampleRequestStatus(enquiryId);

  const sampleRequestId = sampleRequestData?.id;

  // Define fetchSampleFeedback outside useEffect so it can be called from elsewhere
  const fetchSampleFeedback = async () => {
    if (!enquiryStatusId) return;

    try {
      // Use .maybeSingle() instead of .single() to avoid errors when no rows are found
      const { data, error } = await supabase
        .from("sample_feedback")
        .select("id, created_at, reason, response, remarks,delivery_confirmation_date, testing_remarks, testing_confirmation_date ")
        .eq("sample_request_id", enquiryStatusId)

      if (error) {
        console.error("Error fetching sample feedback:", error);
      } else {
        console.log("Sample feedback data:", data, enquiryStatusId);
        setSampleFeedback(data[0]); // This will be null if no rows were found
      }
    } catch (error) {
      console.error("Exception fetching sample feedback:", error);
    }
  };

  // Fetch sample feedback data when component mounts or enquiryStatusId changes
  useEffect(() => {
    fetchSampleFeedback();
  }, [enquiryStatusId]);

  console.log("SampleTrackingItem rendering with:", {
    enquiryId,
    enquiryStatusId,
    sampleRequestData
  });

  // Fetch status history for this sample request
  const { data: statusHistory } = useQuery({
    queryKey: ["sample-status-history", sampleRequestId],
    enabled: !!sampleRequestId,
    queryFn: async () => {
      const { data, error } = await supabase
        .from("sample_status_history")
        .select("*")
        .eq("sample_request_id", sampleRequestId)
        .order("changed_at", { ascending: true });

      if (error) {
        console.error("Error fetching status history:", error);
        return null;
      }
      console.log("Status history data:", data);
      return data;
    },
  });

  // Create a map of status timestamps
  const statusTimestamps =
    statusHistory?.reduce((acc, record) => {
      acc[record.sample_status] = record.changed_at;
      return acc;
    }, {} as Record<string, string>) ?? {};

  // Helper function to format date and time as shown in the image
  const formatDateTime = (
    timestamp: string | null | undefined,
    timeOnly = false
  ) => {
    if (!timestamp) return "";

    // Parse the timestamp
    const date = new Date(timestamp);

    // Format the time in 24-hour format
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const formattedTime = `${hours}:${minutes}`;

    if (timeOnly) {
      return formattedTime;
    }

    // Format the date as DD/MM/YYYY
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0"); // Month is 0-indexed
    const year = date.getFullYear();
    const formattedDate = `${day}/${month}/${year}`;

    return `${formattedDate}, ${formattedTime}`;
  };

  // Check if tracking details are available
  const hasTrackingDetails =
    sampleRequestData &&
    (sampleRequestData.tracking_number ||
      sampleRequestData.carrier_name ||
      sampleRequestData.expected_delivery_date);

  // If no sample requested item found, don't render anything
  if (!sampleRequestData) {
    return null;
  }

  // Get the latest date for the current status
  const currentStatusDate = formatTimestamp(sampleRequestData.created_at);

  // Otherwise, render the full component
  return (
    <div className="flex gap-4">
      <div className="flex-none">
        <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
          <Package className="h-4 w-4 text-purple-600" />
        </div>
      </div>
      <div className="flex-grow">
        <div className="flex justify-between">
          <h2 className="text-base font-bold">Sample Tracking : {sampleRequestData.id}</h2>
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-500">
              {currentStatusDate} {formatTime(sampleRequestData.created_at)}
            </span>
          </div>
        </div>

        <div className="mt-2 flex flex-wrap gap-2">
          <button
            className="flex items-center justify-between text-sm font-semibold text-gray-600 hover:text-gray-900 py-2 px-3 border border-gray-200 rounded-md bg-white hover:bg-gray-50 transition-colors"
            onClick={() => setIsTrackingTimeline(!isTrackingTimeline)}
          >
            <span>Sample History</span>
            {isTrackingTimeline ? (
              <ChevronUp className="h-4 w-4 ml-2" />
            ) : (
              <ChevronDown className="h-4 w-4 ml-2" />
            )}
          </button>

          <button
            className="flex items-center justify-between text-sm font-semibold text-gray-600 hover:text-gray-900 py-2 px-3 border border-gray-200 rounded-md bg-white hover:bg-gray-50 transition-colors"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <span>Sample Request Details</span>
            {isExpanded ? (
              <ChevronUp className="h-4 w-4 ml-2" />
            ) : (
              <ChevronDown className="h-4 w-4 ml-2" />
            )}
          </button>


          {/* Tracking Details Button - Only show if tracking details exist */}
          {hasTrackingDetails && (
            <button
              className="flex items-center justify-between text-sm font-semibold text-gray-600 hover:text-gray-900 py-2 px-3 border border-gray-200 rounded-md bg-white hover:bg-gray-50 transition-colors"
              onClick={() => setIsTrackingExpanded(!isTrackingExpanded)}
            >
              <span>Tracking Details</span>
              {isTrackingExpanded ? (
                <ChevronUp className="h-4 w-4 ml-2" />
              ) : (
                <ChevronDown className="h-4 w-4 ml-2" />
              )}
            </button>
          )}

          {tableViewStates.includes(sampleFeedback?.response) ? (
            <button
              className="flex items-center justify-between text-sm font-semibold text-gray-600 hover:text-gray-900 py-2 px-3 border border-gray-200 rounded-md bg-white hover:bg-gray-50 transition-colors"
              onClick={() => setIsFeedbackExpanded(!isFeedbackExpanded)}
            >
              <span>Sample Feedback</span>
              {isFeedbackExpanded ? (
                <ChevronUp className="h-4 w-4 ml-2" />
              ) : (
                <ChevronDown className="h-4 w-4 ml-2" />
              )}
            </button>
          ) : (
            // Only show the feedback button if the sample has been delivered
            statusTimestamps.sample_delivered && (
              <button
                className="flex items-center justify-between text-sm font-semibold text-gray-600 hover:text-gray-900 py-2 px-3 border border-gray-200 rounded-md bg-white hover:bg-gray-50 transition-colors"
                onClick={() => setShowFeedbackForm(!showFeedbackForm)}
              >
                Provide Sample Feedback
                {showFeedbackForm ? (
                  <ChevronUp className="h-4 w-4 ml-2" />
                ) : (
                  <ChevronDown className="h-4 w-4 ml-2" />
                )}
              </button>
            )
          )}
        </div>

        {/* Sample Timeline Table */}
        {isTrackingTimeline && (
          <div className="mt-2 bg-gray-50 rounded-lg border border-gray-200 overflow-hidden mb-4">
            {/* Table Container with horizontal scroll */}
            <div className="overflow-x-auto">
              <table className="w-full text-sm border border-gray-200">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="p-2 text-left font-medium border-r">Requested</th>
                    <th className="p-2 text-left font-medium border-r">Available</th>
                    <th className="p-2 text-left font-medium border-r">In Transit</th>
                    <th className="p-2 text-left font-medium">Delivered</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="p-2 border-r border-t bg-gray-50">
                      {formatDateTime(statusTimestamps.sample_requested) || '-'}
                    </td>
                    <td className="p-2 border-r border-t bg-gray-50">
                      {formatDateTime(statusTimestamps.sample_available) || '-'}
                    </td>
                    <td className="p-2 border-r border-t bg-gray-50">
                      {formatDateTime(statusTimestamps.sample_in_transit) || '-'}
                    </td>
                    <td className="p-2 border-t bg-gray-50">
                      {formatDateTime(statusTimestamps.sample_delivered) || '-'}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Sample Request Details */}
        {isExpanded && sampleRequestData && (
          <div className="mt-2">
            <SampleRequestTable
              sampleRequestData={sampleRequestData}
              onOpenAttachments={() => setIsAttachmentsOpen(true)}
            />
          </div>
        )}

        {/* Tracking Details Section */}
        {isTrackingExpanded && hasTrackingDetails && (
          <div className="mt-2">
            <TrackingDetailsTable trackingData={sampleRequestData} />
          </div>
        )}

        {/* Sample Feedback Display */}
        {isFeedbackExpanded && tableViewStates.includes(sampleFeedback?.response) && (
          <div className="mt-2">
            <SampleFeedbackTable
              feedbackData={sampleFeedback}
              onOpenAttachments={() => setIsDocumentsModalOpen(true)}
            />
          </div>
        )}

        {/* Sample Feedback Form */}
        {showFeedbackForm && (
          <div className="mt-3">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-bold text-gray-700">
                Sample Feedback:
              </h3>
              <button
                className="h-7 w-7 p-0 rounded-full hover:bg-gray-100 flex items-center justify-center"
                onClick={() => setShowFeedbackForm(false)}
              >
                <ChevronUp className="h-4 w-4 text-gray-500" />
              </button>
            </div>
            <SampleFeedbackForm
              sampleDeliveredDate={statusTimestamps.sample_delivered}
              enquiryId={enquiryId}
              enquiryStatusId={enquiryStatusId}
              onClose={() => setShowFeedbackForm(false)}
              onSuccess={() => {
                console.log(
                  "Sample feedback submitted successfully, refreshing data..."
                );
                setShowFeedbackForm(false);
                // You could add a toast notification here if desired

                // Explicitly fetch the updated sample feedback data
                fetchSampleFeedback();

                // Call the onRefresh callback if provided
                if (onRefresh) {
                  console.log(
                    "Calling onRefresh callback after sample feedback submission"
                  );
                  onRefresh();
                }
              }}
            />
          </div>
        )}

        {/* Sample Feedback Documents Modal */}
        {sampleFeedback && sampleFeedback.id && (
          <FileAttachmentModal
            isOpen={isDocumentsModalOpen}
            setIsOpen={setIsDocumentsModalOpen}
            id={sampleFeedback.id}
            type="sample"
          />
        )}

        {/* Sample Request Documents Modal */}
        {sampleRequestData && sampleRequestData.id && (
          <FileAttachmentModal
            isOpen={isAttachmentsOpen}
            setIsOpen={setIsAttachmentsOpen}
            id={sampleRequestData.id}
            type="sample_request"
          />
        )}
      </div>
    </div>
  );
};

export default SampleTrackingItem;
