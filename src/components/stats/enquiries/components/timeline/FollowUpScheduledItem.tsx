import React from 'react';
import { Clock } from 'lucide-react';

interface FollowUpScheduledItemProps {
  item: any;
  formatTimestamp: (timestamp: string | null | undefined) => string;
}

const FollowUpScheduledItem: React.FC<FollowUpScheduledItemProps> = ({ 
  item, 
  formatTimestamp 
}) => {
  return (
    <div className="flex gap-4">
      <div className="flex-none">
        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
          <Clock className="h-4 w-4 text-blue-600" />
        </div>
      </div>
      <div className="flex-grow">
        <div className="flex justify-between">
          <h4 className="text-base font-medium">Follow-up Scheduled</h4>
          <span className="text-sm text-gray-500">
            {formatTimestamp(item.follow_up_date)}
          </span>
        </div>
        
        <p className="text-sm text-gray-600 mt-1">
          Follow up with customer scheduled for {formatTimestamp(item.follow_up_date)}
        </p>
        
        {item.notes && (
          <p className="text-sm text-gray-600 mt-1">{item.notes}</p>
        )}
      </div>
    </div>
  );
};

export default FollowUpScheduledItem;
