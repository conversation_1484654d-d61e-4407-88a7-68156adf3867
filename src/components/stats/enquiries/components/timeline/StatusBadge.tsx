import { cn } from "@/lib/utils";

interface StatusBadgeProps {
  status: string;
  className?: string;
}

const StatusBadge = ({ status, className }: StatusBadgeProps) => {
  // Map status to display text
  const getDisplayText = (status: string) => {
    const statusMap: Record<string, string> = {
      "quote_accepted": "Accepted",
      "quote_rejected": "Rejected",
      "quote_redo": "Redo Requested",
      "sample_accepted": "Accepted",
      "sample_rejected": "Rejected",
      "sample_redo": "Redo Requested",
      "sample_requested": "Requested",
      "sample_available": "Available",
      "sample_in_transit": "In Transit",
      "sample_delivered": "Delivered",
      "pricing_quotation_generated": "Generated",
      "enquiry_created": "Created",
      "enquiry_assigned": "Assigned",
      "clarification_needed": "Clarification Needed",
      "po_raised": "PO Raised",
      "regret": "Regretted",
      "cancelled": "Cancelled"
    };

    return statusMap[status] || status;
  };

  // Map status to color
  const getStatusColor = (status: string) => {
    if (status.includes("accepted")) return "bg-green-50 text-green-700";
    if (status.includes("rejected")) return "bg-red-50 text-red-700";
    if (status.includes("redo")) return "bg-amber-50 text-amber-700";
    if (status === "sample_requested") return "bg-blue-50 text-blue-700";
    if (status === "sample_available") return "bg-indigo-50 text-indigo-700";
    if (status === "sample_in_transit") return "bg-purple-50 text-purple-700";
    if (status === "sample_delivered") return "bg-green-50 text-green-700";
    if (status === "pricing_quotation_generated") return "bg-indigo-50 text-indigo-700";
    if (status === "po_raised") return "bg-green-50 text-green-700";
    if (status === "regret" || status === "cancelled") return "bg-red-50 text-red-700";
    if (status === "clarification" || status === "clarification_needed") return "bg-amber-50 text-amber-700";

    return "bg-gray-50 text-gray-700";
  };

  return (
    <span className={cn(
      "inline-flex items-center px-2 py-1 rounded-md text-xs font-medium",
      getStatusColor(status),
      className
    )}>
      {getDisplayText(status)}
    </span>
  );
};

export default StatusBadge;
