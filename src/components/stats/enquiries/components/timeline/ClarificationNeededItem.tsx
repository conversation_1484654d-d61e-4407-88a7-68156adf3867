import React, { useState } from "react";
import { MessageSquare, ChevronDown, ChevronUp } from "lucide-react";
import ClarificationQueriesTable from "../../components/ClarificationQueriesTable";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";

import { toast } from "sonner";

// Define the clarification query type
interface ClarificationQuery {
  id: string;
  enquiry_id: string;
  query: string;
  response?: string;
  status: "pending" | "resolved" | "rejected";
  created_at: string;
  resolved_at?: string;
}

interface ClarificationNeededItemProps {
  item: any;
  enquiryId: string;
  formatTimestamp: (timestamp: string | null | undefined) => string;
  formatTime: (timestamp: string | null | undefined) => string;
  onRefresh?: () => void; // Optional callback for refreshing parent component
}

const ClarificationNeededItem: React.FC<ClarificationNeededItemProps> = ({
  item,
  enquiryId,
  formatTimestamp,
  formatTime,
  onRefresh,
}) => {

  const [isExpanded, setIsExpanded] = useState(false);
  const queryClient = useQueryClient();

  // Fetch clarification queries
  const { data: clarificationQueries, isLoading } = useQuery({
    queryKey: ["clarification-queries", enquiryId],
    enabled: !!enquiryId,
    queryFn: async () => {
      if (!enquiryId) return [];

      const { data, error } = await supabase
        .from("enquiry_clarifications")
        .select("*")
        .eq("enquiry_id", enquiryId)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data as ClarificationQuery[];
    },
  });

  // Mutation to update clarification
  const updateClarificationMutation = useMutation({
    mutationFn: async ({
      id,
      response,
      status,
    }: {
      id: string;
      response?: string;
      status?: "pending" | "resolved" | "rejected";
    }) => {
      const updateData: any = {};
      if (response !== undefined) updateData.response = response;
      if (status !== undefined) {
        updateData.status = status;
        if (status === "resolved")
          updateData.resolved_at = new Date().toISOString();
      }

      const { data, error } = await supabase
        .from("enquiry_clarifications")
        .update(updateData)
        .eq("id", id)
        .select()
        .single();

      // update status of enquiry in enquiry table to enquiry_assigned
      if (status === "resolved") {
        await supabase
          .from("enquiries")
          .update({
            current_status: "enquiry_assigned",
            last_status_change: new Date().toISOString(),
          })
          .eq("id", enquiryId);
      }

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast.success("Clarification updated successfully");
      queryClient.invalidateQueries({
        queryKey: ["clarification-queries", enquiryId],
      });
      // Input field is cleared in the ClarificationQueriesTable component

      // Call the onRefresh callback if provided
      if (onRefresh) {
        console.log('Calling onRefresh callback after clarification update');
        onRefresh();
      }
    },
    onError: (error) => {
      console.error("Error updating clarification:", error);
      toast.error("Failed to update clarification");
    },
  });

  // Handle submit response
  const handleSubmitResponse = (id: string, responseText: string) => {
    if (!responseText.trim()) {
      toast.error("Please enter a response");
      return;
    }

    updateClarificationMutation.mutate({
      id,
      response: responseText,
      status: "resolved",
    });
  };


  return (
    <div className="flex gap-3 py-3">
      <div className="flex-none">
        <div className="w-8 h-8 rounded-full bg-amber-100 flex items-center justify-center">
          <MessageSquare className="h-4 w-4 text-amber-600" />
        </div>
      </div>

      <div className="flex-1">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <h2 className="text-sm font-bold">Clarification Requested</h2>
          </div>
          <span className="text-sm text-gray-500">
            {formatTimestamp(item.created_at)} {formatTime(item.created_at)}
          </span>
        </div>

        {/* Clarification Button */}
        <div className="mt-2">
          <Button
            variant="outline"
            size="sm"
              className="flex items-center justify-between text-sm font-semibold text-gray-600 hover:text-gray-900 py-2 px-3 border border-gray-200 rounded-md bg-white hover:bg-gray-50 transition-colors"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <span>Clarification Queries</span>
            {isExpanded ? (
              <ChevronUp className="h-3 w-3" />
            ) : (
              <ChevronDown className="h-3 w-3" />
            )}
          </Button>
        </div>

        {/* Clarification Queries - Only shown when expanded */}
        {isExpanded && (
          <div className="mt-3">
            {isLoading ? (
              <p className="text-xs text-gray-500">
                Loading clarifications...
              </p>
            ) : clarificationQueries && clarificationQueries.length > 0 ? (
              <ClarificationQueriesTable
                queries={clarificationQueries}
                onSubmitResponse={handleSubmitResponse}
                isSubmitting={updateClarificationMutation.isPending}
              />
            ) : (
              <p className="text-xs text-gray-500">No clarification queries found.</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ClarificationNeededItem;
