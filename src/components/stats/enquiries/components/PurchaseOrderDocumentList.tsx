import React from "react";
import { usePurchaseOrderDocuments } from "../hooks/usePurchaseOrderDocuments";
import DocumentListHeader from "./purchase-order/DocumentListHeader";
import DocumentLoadingState from "./purchase-order/DocumentLoadingState";
import DocumentErrorState from "./purchase-order/DocumentErrorState";
import DocumentDebugInfo from "./purchase-order/DocumentDebugInfo";
import DatabaseInfoDisplay from "./purchase-order/DatabaseInfoDisplay";
import EmptyDocumentsList from "./purchase-order/EmptyDocumentsList";
import DocumentCardList from "./purchase-order/DocumentCardList";
import { format } from 'date-fns';
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";

interface PurchaseOrderDocumentListProps {
  enquiryId?: string;
  enquiryStatusId: string; // Make this required since we need it for the query
}

const PurchaseOrderDocumentList: React.FC<PurchaseOrderDocumentListProps> = ({
  enquiryId,
  enquiryStatusId,
}) => {
  const { documents, isLoading, error, refetch } =
    usePurchaseOrderDocuments(enquiryStatusId);

  const poFiles = documents?.filter((doc) => doc.doc_type === "PO_FILE") || [];
  const otherFiles = documents?.filter((doc) => doc.doc_type === "Others") || [];
  
  // Get PO details from the first document (all documents have the same PO details)
  const poDetails = documents?.[0];

  if (!enquiryId) return null;
  if (isLoading) return <DocumentLoadingState />;
  if (error) return <DocumentErrorState error={error} onRefresh={refetch} />;

  return (
    <div className="mt-3 space-y-4">
      {/* PO Details Section at the top */}
      {poDetails && (
        <div className="space-y-2">
          {/* <DocumentListHeader 
            title="Purchase Order Details"
            count={1}
            onRefresh={refetch}
          /> */}
          <div className="bg-white border rounded-lg shadow-sm">
            <div className="p-4 space-y-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">PO Number:</span>
                <span className="text-sm text-gray-600">{poDetails.po_number}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">Notes:</span>
                <span className="text-sm text-gray-600">{poDetails.notes || 'No notes provided'}</span>
              </div>
              {/* <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">Created:</span>
                <span className="text-sm text-gray-600">
                  {format(new Date(poDetails.po_created_at), 'PPP')}
                </span>
              </div> */}
            </div>
          </div>
        </div>
      )}

      {/* Purchase Order Documents Section */}
      <div className="space-y-2">
        <DocumentListHeader 
          title="Purchase Order Documents"
          count={poFiles.length}
          onRefresh={refetch} 
        />
        {poFiles.length > 0 && <DocumentCardList documents={poFiles} />}
      </div>

      {/* Supporting Documents Section */}
      <div className="space-y-2">
        <DocumentListHeader 
          title="Other Documents"
          count={otherFiles.length}
          onRefresh={refetch} 
        />
        {otherFiles.length > 0 && <DocumentCardList documents={otherFiles} />}
      </div>
    </div>
  );
};

export default PurchaseOrderDocumentList;
