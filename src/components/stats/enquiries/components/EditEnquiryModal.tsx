import { useState, useEffect } from "react";
import { supabase, STORAGE_BUCKETS } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";
import { Button } from "@/components/ui/button";
import { Save, Loader2 } from "lucide-react";
import { toast } from "sonner";
import DocumentUpload from "@/components/enquiries/components/DocumentUpload";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";

// Import the form section components
import ProductInfoSection from "@/components/enquiries/form-sections/ProductInfoSection";
import QuantityPriceSection from "@/components/enquiries/form-sections/QuantityPriceSection";
import AdditionalDetailsSection from "@/components/enquiries/form-sections/AdditionalDetailsSection";
import { ChemicalData } from "@/components/enquiries/types/formTypes";

type EnquiryType = Database["public"]["Tables"]["enquiries"]["Row"] & {
  enquiry_documents: Database["public"]["Tables"]["enquiry_documents"]["Row"][];
};

interface EditEnquiryModalProps {
  open: boolean;
  onClose: () => void;
  enquiryId: string;
  onSuccess?: () => void;
}

const EditEnquiryModal = ({ open, onClose, enquiryId, onSuccess }: EditEnquiryModalProps) => {
  const [enquiry, setEnquiry] = useState<EnquiryType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [files, setFiles] = useState<File[]>([]);

  // State for the chemical data in the format expected by the form components
  const [chemicalData, setChemicalData] = useState<ChemicalData>({
    chemicalName: "",
    brand: "",
    product: "",
    casNumber: "",
    application: "",
    targetPrice: null,
    targetPriceCurrency: "",
    quantity: null,
    quantityUnit: null,
    category: "",
    incoterms: "",
    remarks: "",
    expectedprocurementVolume: null,
    expectedprocurementUnit: null,
    procurementVolume: null,
    procurementUnit: null,
    attachedFiles: [],
  });

  // State for the useChemicalName toggle
  const [useChemicalName, setUseChemicalName] = useState(true);

  useEffect(() => {
    if (open && enquiryId) {
      fetchEnquiry();
    }
  }, [open, enquiryId]);

  const fetchEnquiry = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from("enquiries")
        .select(`
          *,
          enquiry_documents (*)
        `)
        .eq("id", enquiryId)
        .single();

      if (error) {
        throw error;
      }

      setEnquiry(data as EnquiryType);

      // Map the enquiry data to the ChemicalData format
      setChemicalData({
        chemicalName: data.chemical_name || "",
        brand: data.brand || "",
        product: data.product || "",
        casNumber: data.cas_number || "",
        application: data.application || "",
        targetPrice: data.target_price,
        targetPriceCurrency: data.target_price_currency || "",
        quantity: data.quantity,
        quantityUnit: data.quantity_unit as any,
        category: data.category || "",
        incoterms: data.incoterms || "",
        remarks: data.remarks || "",
        expectedprocurementVolume: data.expected_procurement_volume,
        expectedprocurementUnit: data.expected_procurement_unit as any,
        procurementVolume: data.procurement_volume,
        procurementUnit: data.procurement_unit as any,
        attachedFiles: [],
      });

      // Set useChemicalName based on whether chemical_name is present
      setUseChemicalName(!!data.chemical_name);

    } catch (error) {
      console.error("Error fetching enquiry:", error);
      toast.error("Failed to load enquiry details");
    } finally {
      setIsLoading(false);
    }
  };

  const updateChemicalData = (data: Partial<ChemicalData>) => {
    setChemicalData(prev => ({ ...prev, ...data }));
  };

  // Upload documents function similar to the one in useEnquirySubmission
  const uploadDocuments = async (files: File[], enquiryId: string) => {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) return;

    for (const file of files) {
      const fileExt = file.name.split('.').pop();
      const filePath = `${enquiryId}/${crypto.randomUUID()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from(STORAGE_BUCKETS.ENQUIRY_DOCUMENTS)
        .upload(filePath, file);

      if (uploadError) {
        console.error('Error uploading file:', uploadError);
        toast.error(`Failed to upload ${file.name}`);
        continue;
      }

      const { error: metadataError } = await supabase
        .from('enquiry_documents')
        .insert({
          enquiry_id: enquiryId,
          file_name: file.name,
          file_path: filePath,
          content_type: file.type,
          size: file.size,
          uploaded_by: session.user.id
        });

      if (metadataError) {
        console.error('Error saving file metadata:', metadataError);
        toast.error(`Failed to save metadata for ${file.name}`);
      }
    }
  };

  const handleSave = async () => {
    if (!enquiry) return;

    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      toast.error("Please sign in to update enquiries");
      return;
    }

    setIsSaving(true);
    try {
      // Map the ChemicalData back to the enquiry format
      const enquiryData = {
        chemical_name: useChemicalName ? chemicalData.chemicalName : null,
        brand: !useChemicalName ? chemicalData.brand : null,
        product: !useChemicalName ? chemicalData.product : null,
        cas_number: chemicalData.casNumber,
        application: chemicalData.application,
        category: chemicalData.category,
        quantity: chemicalData.quantity,
        quantity_unit: chemicalData.quantityUnit,
        target_price: chemicalData.targetPrice,
        target_price_currency: chemicalData.targetPriceCurrency,
        incoterms: chemicalData.incoterms,
        remarks: chemicalData.remarks,
        procurement_volume: chemicalData.procurementVolume,
        procurement_unit: chemicalData.procurementUnit,
        expected_procurement_volume: chemicalData.expectedprocurementVolume,
        expected_procurement_unit: chemicalData.expectedprocurementUnit,
        // Keep other fields from the original enquiry
        country: enquiry.country,
        city: enquiry.city,
        confidence: enquiry.confidence,
        // Add modified timestamp
        modified_at: new Date().toISOString(),
        last_status_change: new Date().toISOString(),
      };

      const { error } = await supabase
        .from("enquiries")
        .update(enquiryData)
        .eq("id", enquiryId);

      if (error) throw error;

      // Handle file uploads if there are any new files
      if (files.length > 0) {
        await uploadDocuments(files, enquiryId);
      }

      toast.success("Enquiry updated successfully");
      if (onSuccess) onSuccess();
      onClose();
    } catch (error) {
      console.error("Error updating enquiry:", error);
      toast.error("Failed to update enquiry");
    } finally {
      setIsSaving(false);
    }
  };

  // Add a style tag to the document head to override the Dialog styles
  useEffect(() => {
    // Create a style element
    const styleElement = document.createElement('style');
    styleElement.innerHTML = `
      [data-radix-dialog-overlay] {
        background-color: rgba(0, 0, 0, 0.5) !important;
      }
      [data-radix-dialog-content] {
        background-color: white !important;
        max-height: 90vh !important;
        overflow-y: auto !important;
      }
    `;
    // Add an ID to identify this style element
    styleElement.id = 'edit-enquiry-dialog-styles';

    // Append the style element to the document head
    document.head.appendChild(styleElement);

    // Clean up function to remove the style element when the component unmounts
    return () => {
      const existingStyle = document.getElementById('edit-enquiry-dialog-styles');
      if (existingStyle) {
        document.head.removeChild(existingStyle);
      }
    };
  }, []);

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent
        className="max-w-4xl"
      >
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-[#294d48]">Edit Enquiry</DialogTitle>
          </DialogHeader>

          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 text-[#294d48] animate-spin" />
              <span className="ml-2">Loading enquiry details...</span>
            </div>
          ) : enquiry ? (
            <div className="space-y-6">
              {/* Product Information Section */}
              <ProductInfoSection
                useChemicalName={useChemicalName}
                setUseChemicalName={setUseChemicalName}
                chemical={chemicalData}
                updateChemicalData={updateChemicalData}
              />

              {/* Additional Details Section */}
              <AdditionalDetailsSection
                chemical={chemicalData}
                updateChemicalData={updateChemicalData}
              />

              {/* Quantity and Price Section */}
              <QuantityPriceSection
                chemical={chemicalData}
                updateChemicalData={updateChemicalData}
              />

              {/* Document Upload Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-[#294d48]">Documents</h3>

                {/* Existing Documents */}
                {enquiry.enquiry_documents && enquiry.enquiry_documents.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Existing Documents</h4>
                    <div className="space-y-2">
                      {enquiry.enquiry_documents.map((doc) => (
                        <div key={doc.id} className="flex items-center p-3 bg-gray-50 rounded-md">
                          <span className="flex-1 truncate">{doc.file_name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Upload New Documents */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Upload New Documents</h4>
                  <DocumentUpload
                    files={files}
                    onFilesChange={setFiles}
                    maxSize={10}
                    multiple={true}
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                    label="Upload documents or drag and drop files here"
                    helpText="Supported formats: PDF, Word, Excel, Images (Max: 10MB)"
                  />
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center py-12">
              <p className="text-red-500">Failed to load enquiry details</p>
            </div>
          )}

          <DialogFooter>
            <div className="flex justify-end gap-3 mt-6">
              <Button variant="outline" type="button" onClick={onClose} disabled={isSaving}>
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleSave}
                disabled={isLoading || isSaving}
                className="bg-[#294d48] hover:bg-[#1a302c]"
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
  );
};

export default EditEnquiryModal;
