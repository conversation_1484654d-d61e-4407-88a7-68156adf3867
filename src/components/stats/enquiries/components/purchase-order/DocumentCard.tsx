
import React, { useState } from "react";
import { FileText, Eye, Download } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { PurchaseOrderDocument } from "../../hooks/usePurchaseOrderDocuments";

interface DocumentCardProps {
  document: PurchaseOrderDocument;
}

const DocumentCard: React.FC<DocumentCardProps> = ({ document }) => {
  const [downloadFailed, setDownloadFailed] = useState(false);

  console.log("[DEBUG] DocumentCard:", document);

  const handleViewDocument = () => {
    if (!document.url) {
      toast.error("Document URL is not available");
      return;
    }
    
    window.open(document.url, '_blank');
  };

  const handleDownloadDocument = () => {
    if (!document.url) {
      toast.error("Document URL is not available");
      return;
    }

    try {
      // Use the global document object, not our document prop
      const link = window.document.createElement('a');
      link.href = document.url;
      link.download = document.file_name || 'purchase-order-document';
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);
      toast.success(`Downloading ${document.file_name}`);
    } catch (err) {
      console.error("[ERROR] Download error:", err);
      toast.error("Failed to download document");
      setDownloadFailed(true);
    }
  };

  return (
    <Card key={document.id} className="p-3 bg-white border border-gray-200">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4 text-[#294d48]" />
          <span className="text-sm text-gray-700 truncate max-w-[180px]" title={document.file_name}>
            {document.file_name}
          </span>
        </div>
        <div className="flex items-center gap-1">
          {document.url && (
            <>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={handleViewDocument}
                title="View document"
              >
                <Eye className="h-4 w-4 text-[#294d48]" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={handleDownloadDocument}
                title="Download document"
              >
                <Download className="h-4 w-4 text-[#294d48]" />
              </Button>
            </>
          )}
          {!document.url && (
            <span className="text-xs text-amber-500">URL unavailable</span>
          )}
        </div>
      </div>
      {downloadFailed && (
        <div className="mt-2 text-xs text-red-500">
          Failed to download this document. Please try again later.
        </div>
      )}
    </Card>
  );
};

export default DocumentCard;
