
import React from "react";
import { Info, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";

interface DocumentDebugInfoProps {
  debugInfo: string;
  onRefresh: () => void;
}

const DocumentDebugInfo: React.FC<DocumentDebugInfoProps> = ({ debugInfo, onRefresh }) => {
  return (
    <div className="mt-2 p-2 bg-amber-50 border border-amber-200 rounded-md text-sm text-amber-700 flex items-center gap-2">
      <Info className="h-4 w-4 text-amber-500" />
      <span>{debugInfo}</span>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-7 p-1 ml-auto"
        onClick={() => onRefresh()}
      >
        <RefreshCw className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default DocumentDebugInfo;
