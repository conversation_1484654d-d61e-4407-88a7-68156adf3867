
import React from "react";
import { AlertCircle, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";

interface DocumentErrorStateProps {
  error: Error;
  onRefresh: () => void;
}

const DocumentErrorState: React.FC<DocumentErrorStateProps> = ({ error, onRefresh }) => {
  return (
    <div className="mt-2 p-2 bg-red-50 rounded-md text-sm text-red-600 flex items-center gap-2">
      <AlertCircle className="h-4 w-4" />
      <span>Error loading purchase order documents: {error.message}</span>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-7 p-1 ml-auto"
        onClick={() => onRefresh()}
      >
        <RefreshCw className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default DocumentErrorState;
