
import React from 'react';
import { format } from 'date-fns';
import { Card } from "@/components/ui/card";
import DocumentCard from "./DocumentCard";
import { PurchaseOrderDocument } from "../../hooks/usePurchaseOrderDocuments";

interface DocumentCardListProps {
  documents: PurchaseOrderDocument[];
}

const DocumentCardList: React.FC<DocumentCardListProps> = ({ documents }) => {
  return (
    <div className="grid gap-2">
      {documents.map(doc => (
        <DocumentCard key={doc.id} document={doc} />
      ))}
    </div>
  );
};

export default DocumentCardList;
