
import React from "react";
import { Database } from "lucide-react";

interface DatabaseInfoDisplayProps {
  dbInfo: string;
}

const DatabaseInfoDisplay: React.FC<DatabaseInfoDisplayProps> = ({ dbInfo }) => {
  return (
    <div className="p-2 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-700 flex items-center gap-2">
      <Database className="h-4 w-4 text-blue-500" />
      <span>{dbInfo}</span>
    </div>
  );
};

export default DatabaseInfoDisplay;
