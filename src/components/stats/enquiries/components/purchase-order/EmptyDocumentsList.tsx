
import React, { useState, useEffect } from "react";
import { RefreshCw, Database } from "lucide-react";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import DatabaseInfoDisplay from "./DatabaseInfoDisplay";

interface EmptyDocumentsListProps {
  enquiryId?: string;
  refetch: () => void;
}

const EmptyDocumentsList: React.FC<EmptyDocumentsListProps> = ({ enquiryId, refetch }) => {
  const [retryCount, setRetryCount] = useState(0);
  const [dbInfo, setDbInfo] = useState<string | null>(null);
  
  // Direct database check for debugging
  useEffect(() => {
    const checkDatabase = async () => {
      if (!enquiryId) return;
      
      try {
        // Get PO ID directly
        const { data: poData } = await supabase
          .from('purchase_orders')
          .select('id')
          .eq('enquiry_id', enquiryId)
          .maybeSingle();
        
        if (!poData) {
          setDbInfo("No purchase order found in database for this enquiry.");
          return;
        }
        
        // Get attachment count
        const { data: attachments, count } = await supabase
          .from('purchase_order_attachments')
          .select('*', { count: 'exact' })
          .eq('purchase_order_id', poData.id);
        
        setDbInfo(`DB has: PO ID ${poData.id} with ${count || 0} attachment(s)`);
        
        // If we have attachments but they're not in our React state, force a refetch
        if ((count && count > 0)) {
          console.log("[INFO] Database has attachments but they're not in our state - forcing refetch");
          setTimeout(() => refetch(), 500);
        }
      } catch (err) {
        console.error("[ERROR] Database check error:", err);
        setDbInfo("Error checking database");
      }
    };
    
    checkDatabase();
  }, [enquiryId, refetch]);

  // Auto-retry on empty documents
  useEffect(() => {
    if (retryCount < 3) {
      const timer = setTimeout(() => {
        console.log("[DEBUG] Auto-retrying document fetch, attempt:", retryCount + 1);
        setRetryCount(prev => prev + 1);
        refetch();
      }, 2000); // Retry after 2 seconds
      
      return () => clearTimeout(timer);
    }
  }, [retryCount, refetch]);

  return (
    <div className="mt-2 space-y-2">
      {/* {dbInfo && <DatabaseInfoDisplay dbInfo={dbInfo} />} */}
      <div className="p-2 bg-amber-50 rounded-md text-sm text-amber-700 flex items-center justify-between">
        <span>No purchase order documents available</span>
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-7 p-1"
          onClick={() => {
            console.log("[DEBUG] Manual refresh triggered");
            setRetryCount(0);
            refetch();
          }}
        >
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default EmptyDocumentsList;
