
import React from "react";
import { RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface DocumentListHeaderProps {
  title: string;
  count: number;
  onRefresh: () => void;
}

const DocumentListHeader: React.FC<DocumentListHeaderProps> = ({ 
  title,
  count, 
  onRefresh 
}) => {
  return (
    <div className="flex items-center justify-between">
      <span className="text-sm font-medium text-gray-700">
        {title} ({count})
      </span>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-7 p-1"
        onClick={() => {
          console.log("[DEBUG] Manual refresh triggered");
          onRefresh();
        }}
      >
        <RefreshCw className="h-3 w-3" />
      </Button>
    </div>
  );
};

export default DocumentListHeader;
