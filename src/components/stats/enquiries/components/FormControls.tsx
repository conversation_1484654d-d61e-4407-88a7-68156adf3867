
import { Button } from "@/components/ui/button";
import { CheckCircle2 } from "lucide-react";

interface FormControlsProps {
  onClose: () => void;
  isSubmitting: boolean;
  submitLabel?: string; // Make this prop optional
}

const FormControls = ({ onClose, isSubmitting, submitLabel = "Submit Feedback" }: FormControlsProps) => {
  return (
    <div className="flex justify-end gap-2">
      <Button
        type="button"
        variant="outline"
        onClick={onClose}
        className="border-gray-200"
        disabled={isSubmitting}
      >
        Cancel
      </Button>
      <Button
        type="submit"
        className="text-gradient text-white flex items-center gap-1.5"
        disabled={isSubmitting}
      >
        {isSubmitting ? (
          <>
            <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
            <span>Submitting...</span>
          </>
        ) : (
          <>
            <CheckCircle2 className="h-4 w-4" />
            <span>{submitLabel}</span>
          </>
        )}
      </Button>
    </div>
  );
};

export default FormControls;
