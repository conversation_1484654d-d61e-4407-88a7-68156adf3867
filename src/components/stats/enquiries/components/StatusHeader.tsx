
import { getStatusIcon, formatStatus } from "../utils/statusUtils";

interface StatusHeaderProps {
  status: string;
  isActive: boolean;
}

const StatusHeader = ({ status, isActive }: StatusHeaderProps) => {
  return (
    <div className="flex items-center space-x-3">
      <div className={`flex-shrink-0 ${isActive ? 'animate-pulse-3-times' : ''}`}>
        {getStatusIcon(status)}
      </div>
      <p className={`font-medium ${isActive ? 'text-gray-900' : 'text-gray-600'}`}>
        {formatStatus(status)}
      </p>
    </div>
  );
};

export default StatusHeader;
