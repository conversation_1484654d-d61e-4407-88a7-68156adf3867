import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Eye, Download, FileIcon } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { supabase, STORAGE_BUCKETS } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface EnquiryDocumentsModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  enquiryId: string;
}

const EnquiryDocumentsModal = ({ isOpen, setIsOpen, enquiryId }: EnquiryDocumentsModalProps) => {
  const [documents, setDocuments] = useState<Array<{ id: string; name: string; url: string; file_path: string }>>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Function to handle file download
  const handleDownload = async (file: { id: string; name: string; file_path: string }) => {
    try {
      const { data, error } = await supabase.storage
        .from(STORAGE_BUCKETS.ENQUIRY_DOCUMENTS)
        .download(file.file_path);

      if (error) {
        console.error('Download error:', error);
        toast.error('Failed to download file');
        return;
      }

      // Create a blob URL and trigger download
      const blob = new Blob([data], { type: data.type });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = file.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('File downloaded successfully');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download file');
    }
  };

  useEffect(() => {
    const fetchDocuments = async () => {
      if (!enquiryId) return;
      setIsLoading(true);

      try {
        // Fetch documents from the enquiry_documents table
        const { data, error } = await supabase
          .from('enquiry_documents')
          .select('id, file_name, file_path, content_type')
          .eq('enquiry_id', enquiryId);

        if (error) throw error;
        
        if (!data || data.length === 0) {
          setDocuments([]);
          return;
        }

        // Get signed URLs for each document
        const documentsWithUrls = await Promise.all(
          data.map(async (doc) => {
            // Get a signed URL that expires in 1 hour (3600 seconds)
            const { data: urlData } = await supabase.storage
              .from(STORAGE_BUCKETS.ENQUIRY_DOCUMENTS)
              .createSignedUrl(doc.file_path, 3600);

            return {
              id: doc.id,
              name: doc.file_name,
              url: urlData?.signedUrl || "",
              contentType: doc.content_type,
              file_path: doc.file_path
            };
          })
        );

        setDocuments(documentsWithUrls);
      } catch (error) {
        console.error("Error fetching enquiry documents:", error);
        toast.error("Failed to load documents");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) fetchDocuments();
  }, [isOpen, enquiryId]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="max-w-md">
        <DialogTitle>Enquiry Documents</DialogTitle>
        <DialogDescription className="text-sm text-gray-500">
          Click "Download" to save the file.
        </DialogDescription>

        <ScrollArea className="mt-4 max-h-[300px]">
          {isLoading && <p className="text-sm text-center py-2">Loading documents...</p>}

          {!isLoading && documents.length > 0 ? (
            documents.map((file) => (
              <div key={file.id} className="flex justify-between items-center p-3 bg-gray-100 rounded-md mb-2">
                <div className="text-sm truncate flex-1 flex items-center">
                  <FileIcon className="w-4 h-4 mr-2" />
                  {file.name}
                </div>
                <div className="flex space-x-2">
                  {/* <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(file.url, "_blank")}
                  >
                    <Eye className="w-4 h-4 mr-1" /> View
                  </Button> */}
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleDownload(file)}
                  >
                    <Download className="w-4 h-4 mr-1" /> Download
                  </Button>
                </div>
              </div>
            ))
          ) : (
            !isLoading && <p className="text-sm text-center py-2 text-gray-500">No documents found</p>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default EnquiryDocumentsModal;
