import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Clock } from "lucide-react";
import NewEnquiryLifecycle from "../NewEnquiryLifecycle";

interface EnquiryTimelineButtonProps {
  enquiryId: string;
  buttonText?: string;
}

const EnquiryTimelineButton = ({ enquiryId, buttonText = "View Timeline" }: EnquiryTimelineButtonProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 flex items-center gap-1"
        onClick={() => setIsOpen(true)}
      >
        <Clock className="h-4 w-4" />
        {buttonText}
      </Button>

      <NewEnquiryLifecycle
        enquiryId={enquiryId}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </>
  );
};

export default EnquiryTimelineButton;
