
import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileText, Plus, Paperclip, Beaker } from "lucide-react";
import SampleRequestForm from "./sample-request/SampleRequestForm";
import PurchaseOrderForm from "./PurchaseOrderForm";
import { motion } from "framer-motion";
import { supabase } from "@/integrations/supabase/client";

interface SampleRequestButtonProps {
  enquiryId: string;
  feedbackData: {
    response: string;
    reason: string;
    created_at?: string;
  } | null;
  onSampleRequestSuccess?: () => void;
  currentStatus?: string;
  statushistoryId?: string;
}

const SampleRequestButton = ({
  enquiryId,
  feedbackData,
  onSampleRequestSuccess,
  currentStatus = "quote_accepted",
  statushistoryId,
}: SampleRequestButtonProps) => {
  const [showSampleForm, setShowSampleForm] = useState(false);
  const [showPOForm, setShowPOForm] = useState(false);
  const [isSampleDisabled, setIsSampleDisabled] = useState(false);
  const [status, setStatus] = useState("");
  
  // Fetch the latest status
  const fetchEnquiryStatus = async () => {
    const { data, error } = await supabase
      .from("enquiries")
      .select("current_status")
      .eq("id", enquiryId)
      .single();

    if (error) {
      console.error("Failed to fetch current status:", error);
      throw error;
    }

    console.log("Fetched current status:", data?.current_status);
    setStatus(data?.current_status);
  };

  useEffect(() => {
    fetchEnquiryStatus();
  }, [showSampleForm, showPOForm, enquiryId]);

  // Check if sample has already been requested
  const checkSampleRequested = async () => {
    try {
      const { data, error } = await supabase
        .from("sample_requests")
        .select("id")
        .eq("enquiry_id", enquiryId)
        .maybeSingle();

      if (error) {
        console.error("Error checking sample request status:", error);
      } else if (data) {
        setIsSampleDisabled(true);
      }
    } catch (error) {
      console.error("Exception checking sample status:", error);
    }
  };

  useEffect(() => {
    checkSampleRequested();
  }, []);

  // Hide both buttons if PO is already raised
  if (status === "po_raised") {
    return null;
  }

  if(isSampleDisabled) {
    return null;
  }

  return (
    <motion.div 
      initial={{ opacity: 0, y: 10 }} 
      animate={{ opacity: 1, y: 0 }} 
      transition={{ duration: 0.3 }} 
      className="mt-4"
    >
      <Card className="shadow-sm border border-green-100 overflow-hidden bg-gradient-to-br from-white to-green-50">
        <CardContent className="p-3 flex flex-col gap-3">
          {/* Title */}
          {/* <h5 className="text-sm font-medium text-green-700">
            {status === "quote_accepted" && "Quotation Accepted - Take Action"}
            {status === "sample_accepted" && "Sample Accepted - Proceed Further"}
          </h5> */}

          {/* Sample Request Button */}
          {!showSampleForm ? (
            <Button
              variant="outline"
              size="sm"
              className="bg-green-100 hover:bg-green-200 text-green-800 border-green-200 flex items-center gap-1.5"
              onClick={() => setShowSampleForm(true)}
              disabled={isSampleDisabled}
            >
              <Beaker className="h-4 w-4" />
              {isSampleDisabled ? "Sample Already Requested" : "Request Sample"}
            </Button>
          ) : (
            <SampleRequestForm
              enquiryId={enquiryId}
              onClose={() => setShowSampleForm(false)}
              onSuccess={() => {
                setIsSampleDisabled(true);
                if (onSampleRequestSuccess) onSampleRequestSuccess();
              }}
              statushistoryId={statushistoryId}
            />
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default SampleRequestButton;
