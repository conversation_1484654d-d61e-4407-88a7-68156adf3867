
import { Database } from "@/integrations/supabase/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckCircle, XCircle, RefreshCw } from "lucide-react";
import { cn } from "@/lib/utils";

type SampleResponse = "sample_accepted" | "sample_rejected" | "sample_redo";

interface SampleResponseOptionsProps {
  selectedResponse: "" | SampleResponse;
  onSelect: (response: SampleResponse) => void;
}

const SampleResponseOptions = ({ selectedResponse, onSelect }: SampleResponseOptionsProps) => {
  // Define the response options with their values
  const options: { value: SampleResponse; label: string; icon: React.ReactNode; color: string }[] = [
    {
      value: "sample_accepted",
      label: "Accept Sample",
      icon: <CheckCircle className="h-4 w-4" />,
      color: "bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
    },
    {
      value: "sample_rejected",
      label: "Reject Sample",
      icon: <XCircle className="h-4 w-4" />,
      color: "bg-red-50 border-red-200 text-red-700 hover:bg-red-100"
    },
    {
      value: "sample_redo",
      label: "Request New Sample",
      icon: <RefreshCw className="h-4 w-4" />,
      color: "bg-amber-50 border-amber-200 text-amber-700 hover:bg-amber-100"
    }
  ];

  const handleSelect = (value: SampleResponse) => {
    console.log("Selected sample response option:", value);
    onSelect(value);
  };

  return (
    <div className="flex flex-wrap gap-2">
      {options.map((option) => (
        <Button
          key={option.value}
          type="button"
          variant="outline"
          className={cn(
            "flex items-center gap-1.5 rounded-full px-4 py-2 border transition-all duration-200",
            selectedResponse === option.value
              ? `${option.color} ring-2 ring-offset-1 ${option.color.replace('bg-', 'ring-').replace(' hover:bg-green-100', '').replace(' hover:bg-red-100', '').replace(' hover:bg-amber-100', '')}`
              : `bg-white border-gray-200 text-gray-700 hover:bg-gray-50 ${option.color.includes('green') ? 'hover:border-green-300' : option.color.includes('red') ? 'hover:border-red-300' : 'hover:border-amber-300'}`
          )}
          onClick={() => handleSelect(option.value)}
        >
          {option.icon}
          {option.label}
        </Button>
      ))}
    </div>
  );
};

export default SampleResponseOptions;
