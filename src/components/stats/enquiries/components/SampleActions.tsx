
import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import SampleActionButtons from "./SampleActionButtons";
import QuotationFeedbackDisplay from "./QuotationFeedbackDisplay";
import { motion } from "framer-motion";
import { supabase } from "@/integrations/supabase/client";

interface SampleActionsProps {
  enquiryId?: string;
  hasFeedback?: boolean;
  feedbackData?: {
    id?: string; // Add the feedback ID
    response: string;
    reason: string;
    created_at?: string;
    type?: string;
  } | null;
  onSuccess?: () => void;
  currentStatus?: string;
  enquiryStatusId?:string
}

const SampleActions = ({ 
  enquiryId, 
  hasFeedback, 
  feedbackData,
  onSuccess,
  currentStatus,
  enquiryStatusId
}: SampleActionsProps) => {
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [hadFeedbackData, SetHasFeedbackData] = useState(false);
  const [feedback, setFeedback] = useState(null);
  const [status, setStatus] = useState("");

    useEffect(() => {
      const fetchEnquiryStatus = async () => {
        const { data, error } = await supabase
          .from("enquiries")
          .select("current_status")
          .eq("id", enquiryId)
          .single();
    
        if (error) {
          console.error("Failed to fetch current status:", error);
          throw error;
        }
    
        console.log("Fetched current status:", data?.current_status);
        setStatus(data?.current_status);
      };

      const fetchFeedback = async () => {
        console.log("### id", enquiryStatusId, status);
        const { data, error } = await supabase
          .from("sample_feedback")
          .select("id, created_at, reason, response")
          .eq("feedback_history_id", enquiryStatusId)
          .single(); // Ensures only one record is returned
  
        if (error) {
          console.error("Error fetching feedback: ###", error);
        } else {
          console.log("Feedback: ###", data);
          setFeedback(data);
          SetHasFeedbackData(true);
        }
      };
  
      fetchFeedback(); // Call the async function
      fetchEnquiryStatus();
    }, [showFeedbackForm]); // Empty dependency array ensures it runs only once on mount
  
  // Only proceed if we have an enquiry ID
  if (!enquiryId) return null;

  // Format timestamp from feedbackData if available
  const formatDate = (dateString?: string) => {
    if (!dateString) return "";
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short', day: 'numeric', year: 'numeric'
    });
  };
  
  // Check if the sample has been delivered
  const isSampleDelivered = status === "sample_delivered" || 
    ['sample_accepted', 'sample_rejected', 'sample_redo'].includes(status || '');
  
  // If feedback exists, display it using the same component as quotation feedback
  if (hasFeedback && feedback) {
    console.log("Showing sample feedback display:", feedback);
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="mt-4" // Added margin-top here
      >
        <QuotationFeedbackDisplay 
          response={feedback.response}
          reason={feedback.reason}
          date={formatDate(feedback.created_at)}
          entityId={enquiryId}
          feedbackId={feedback.id}
          type="sample"
        />
      </motion.div>
    );
  }
  
  // If no feedback yet and sample is delivered, show the action button
  if (isSampleDelivered) {
    console.log("Showing sample feedback form button");
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="mt-4" // Added margin-top here
      >
        <Card className="w-full shadow-sm overflow-hidden bg-gradient-to-br from-white to-gray-50 border-0">
          <CardContent className="p-3">
            <h5 className="text-sm font-medium text-gray-700 mb-2">Sample Delivered - Provide Feedback</h5>
            <SampleActionButtons
              enquiryId={enquiryId}
              showFeedbackForm={showFeedbackForm}
              setShowFeedbackForm={setShowFeedbackForm}
              onSuccess={onSuccess}
              enquiryStatusId={enquiryStatusId}
            />
          </CardContent>
        </Card>
      </motion.div>
    );
  }
  
  // No actions shown if sample is not delivered and there's no feedback
  return null;
};

export default SampleActions;
