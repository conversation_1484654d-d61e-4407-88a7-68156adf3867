import { Modal, Select, Input } from "antd";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { X, Upload, FileText, Plus, Trash2 } from "lucide-react";
// We're implementing the submission logic directly in this component
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface CreatePurchaseOrderModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

type CustomerOption = {
  value: string;
  label: string;
};

type EnquiryOption = {
  value: string;
  label: string | JSX.Element;
  chemical_name?: string;
  quantity?: number;
  quantity_unit?: string;
  procurement_volume?: number;
  procurement_unit?: string;
  id?: string;
  enquiry_id?: string;
};

const CreatePurchaseOrderModal = ({
  open,
  onClose,
  onSuccess,
}: CreatePurchaseOrderModalProps) => {
  const [enquiryOptions, setEnquiryOptions] = useState<EnquiryOption[]>([]);
  const [isLoadingEnquiries, setIsLoadingEnquiries] = useState(false);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [poEntries, setPoEntries] = useState<any[]>([
    {
      customerId: "",
      customerName: "",
      enquiryIds: [], // Changed to array for multiple selections
      enquiryNames: [], // Changed to array for multiple selections
      files: [],
      otherFiles: [],
      poNumber: "",
      poFrequency: "",
      poValue: "",
      poCurrency: "USD",
      notes: "",
    },
  ]);

  // State to track validation errors
  const [validationErrors, setValidationErrors] = useState<{
    [key: number]: {
      customerId?: boolean;
      enquiryIds?: boolean;
      files?: boolean;
      poNumber?: boolean;
      poFrequency?: boolean;
      poValue?: boolean;
    };
  }>({});

  // State to track customer suggestions
  const [customerSuggestions, setCustomerSuggestions] = useState<
    CustomerOption[]
  >([]);
  const [showSuggestions, setShowSuggestions] = useState<{
    [key: number]: boolean;
  }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset selections
  const resetSelections = () => {
    console.log("Resetting selections");
    setEnquiryOptions([]); // Clear enquiry options
    setCustomerSuggestions([]); // Clear customer suggestions
    setPoEntries([
      {
        customerId: "",
        customerName: "",
        enquiryIds: [],
        enquiryNames: [],
        files: [],
        otherFiles: [],
        poNumber: "",
        poFrequency: "",
        poValue: "",
        poCurrency: "USD",
        notes: "",
      },
    ]);
  };

  // Function to add a new PO entry
  const addPoEntry = () => {
    // Add a new empty entry
    setPoEntries([
      ...poEntries,
      {
        customerId: "",
        customerName: "",
        enquiryIds: [],
        enquiryNames: [],
        files: [],
        otherFiles: [],
        poNumber: "",
        poFrequency: "",
        poValue: "",
        poCurrency: "USD",
        notes: "",
      },
    ]);
  };

  // Function to remove a PO entry
  const removePoEntry = (index: number) => {
    // Don't allow removing the last entry
    if (poEntries.length <= 1) {
      toast.error("Cannot remove the last entry.");
      return;
    }

    const updatedEntries = [...poEntries];
    updatedEntries.splice(index, 1);
    setPoEntries(updatedEntries);
  };

  // Function to submit all PO entries
  const submitAllEntries = async (e: React.FormEvent) => {
    e.preventDefault();

    // If there are entries, submit them
    if (poEntries.length > 0) {
      await submitEntries(poEntries);
    } else {
      toast.error("Please add at least one PO entry");
    }
  };

  // Helper function to submit entries
  const submitEntries = async (entries: any[]) => {
    // Reset validation errors
    setValidationErrors({});

    // Track if any validation errors occurred
    let hasErrors = false;
    const newValidationErrors: { [key: number]: any } = {};

    // Validate all entries
    for (let i = 0; i < entries.length; i++) {
      const entry = entries[i];
      newValidationErrors[i] = {};

      // Check each required field
      // For customer, either customerId (from suggestion) or customerName (manual entry) is required
      if (!entry.customerId && !entry.customerName) {
        newValidationErrors[i].customerId = true;
        hasErrors = true;
      }

      if (!entry.enquiryIds || entry.enquiryIds.length === 0) {
        newValidationErrors[i].enquiryIds = true;
        hasErrors = true;
      }

      if (!entry.files || entry.files.length === 0) {
        newValidationErrors[i].files = true;
        hasErrors = true;
      }

      if (!entry.poNumber) {
        newValidationErrors[i].poNumber = true;
        hasErrors = true;
      }

      if (!entry.poFrequency || !/^\d+$/.test(entry.poFrequency) || parseInt(entry.poFrequency) <= 0) {
        newValidationErrors[i].poFrequency = true;
        hasErrors = true;
      }

      if (!entry.poValue || !/^\d+$/.test(entry.poValue) || parseInt(entry.poValue) <= 0) {
        newValidationErrors[i].poValue = true;
        hasErrors = true;
      }
    }

    // If there are validation errors, set them and return
    if (hasErrors) {
      setValidationErrors(newValidationErrors);
      toast.error("Please fill in all required fields");
      return;
    }

    // Show loading state
    setIsSubmitting(true);

    try {
      // Submit each entry one by one
      for (const entry of entries) {
        // Get the current user session
        const { data: authData } = await supabase.auth.getSession();
        if (!authData.session) {
          throw new Error(
            "User must be authenticated to submit purchase order"
          );
        }

        // Process each selected enquiry
        for (const enquiryId of entry.enquiryIds) {
          // If customer has an ID, use it directly. Otherwise, check if we need to create a new customer
          let customerId = entry.customerId;

          // If no customerId but we have a name, we might need to create a new customer
          if (!customerId && entry.customerName) {
            // Check if this customer already exists by name
            const { data: existingCustomers, error: customerLookupError } =
              await supabase
                .from("customer")
                .select("id")
                .ilike("name", entry.customerName)
                .limit(1);

            if (customerLookupError) {
              console.error("Error looking up customer:", customerLookupError);
            } else if (existingCustomers && existingCustomers.length > 0) {
              // Use the existing customer ID
              customerId = existingCustomers[0].id;
            } else {
              // Create a new customer
              const { data: newCustomer, error: createCustomerError } =
                await supabase
                  .from("customer")
                  .insert({
                    name: entry.customerName,
                    created_by: authData.session.user.id,
                  })
                  .select("id")
                  .single();

              if (createCustomerError) {
                console.error(
                  "Error creating new customer:",
                  createCustomerError
                );
                throw new Error(
                  `Failed to create new customer: ${createCustomerError.message}`
                );
              }

              if (newCustomer) {
                customerId = newCustomer.id;
              }
            }
          }

          // if the current_status of enquiry  is pricing_quotation_generated, first quote accepted status in history and then quotation feedback before the get the          pricing_quote_id: enquiryStatusId,
          const { data: enquiryData, error: enquiryError } = await supabase
            .from("enquiries")
            .select("current_status")
            .eq("id", enquiryId)
            .single();

          console.log("enquiryData %%%", enquiryData);

          if (enquiryData.current_status === "pricing_quotation_generated") {
            // get the pricing_quote_id by fetching latest id of enquiry_status_history which is pricing_quotation_generated
            const { data: pricingQuoteId, error: pricingQuoteError } =
              await supabase
                .from("enquiry_status_history")
                .select("id")
                .eq("enquiry_id", enquiryId)
                .eq("status", "pricing_quotation_generated")
                .order("created_at", { ascending: false })
                .limit(1)
                .single();

            if (pricingQuoteError) {
              console.error(
                "Error fetching pricing quote id:",
                pricingQuoteError
              );
              throw pricingQuoteError;
            }

            const { error: feedbackError } = await supabase
              .from("quotation_feedback")
              .insert({
                enquiry_id: enquiryId,
                pricing_quote_id: pricingQuoteId.id,
                response: "quote_accepted",
                remarks: "Accepted through direct PO",
                reason: "Direct PO",
                submitted_by: authData.session.user.email,
              });

            if (feedbackError) {
              console.error("Error submitting feedback:", feedbackError);
              throw feedbackError;
            }
          }

          // do same if the current_status of enquiry  is sample_delivered, first sample_accepted status in history and then sample feedback before the get the purchase order
          if (enquiryData.current_status === "sample_delivered") {
            // get the feedback_history_id by fetching latest id of enquiry_status_history which is sample_delivered
            const { data: feedbackHistoryId, error: feedbackHistoryError } =
              await supabase
                .from("enquiry_status_history")
                .select("id")
                .eq("enquiry_id", enquiryId)
                .eq("status", "sample_delivered")
                .order("created_at", { ascending: false })
                .limit(1)
                .single();

            const { error: sampleFeedbackError } = await supabase
              .from("sample_feedback")
              .insert({
                enquiry_id: enquiryId,
                feedback_history_id: feedbackHistoryId.id,
                response: "sample_accepted",
                remarks: "Accepted through direct PO",
                reason: "Direct PO",
                submitted_by: authData.session.user.email,
                type: "sample",
              });
            if (sampleFeedbackError) {
              console.error("Error submitting feedback:", sampleFeedbackError);
              throw sampleFeedbackError;
            }
          }

          // Update enquiry status
          const { error } = await supabase
            .from("enquiries")
            .update({
              current_status: "po_raised",
              last_status_change: new Date().toISOString(),
            })
            .eq("id", enquiryId);

          if (error) {
            throw new Error("Failed to raise PO. Please try again.");
          }

          // Create a status history record for PO raised
          const { data: historyData, error: historyError } = await supabase
            .from("enquiry_status_history")
            .insert({
              enquiry_id: enquiryId,
              status: "po_raised",
              changed_by: authData.session.user.email,
              sales_agent_email: authData.session.user.email,
              notes: "Purchase order raised",
            })
            .select("id")
            .single();

          if (historyError) {
            throw new Error(
              `Failed to create status history: ${historyError.message}`
            );
          }

          const historyStatusId = historyData.id;
          // Create the purchase order
          const { data: poData, error: poError } = await supabase.rpc(
            "create_purchase_order",
            {
              p_enquiry_id: enquiryId,
              p_notes: entry.notes || "",
              p_created_by: authData.session.user.id,
              p_po_number: entry.poNumber,
              p_frequency: entry.poFrequency || null,
              p_customer_id: customerId,
              p_history_status_id: historyStatusId,
              p_po_value: entry.poValue || null,
              p_po_currency: entry.poCurrency || "USD",
            } as any
          );

          if (poError) {
            console.error("Error creating purchase order:", poError);
            throw new Error(
              `Failed to create purchase order: ${poError.message}`
            );
          }

          if (!poData) {
            throw new Error("Failed to create purchase order: No ID returned");
          }

          // The RPC function returns an object with id and po_number
          console.log("Raw PO data returned:", poData, "Type:", typeof poData);

          // Extract the ID from the returned object
          const purchaseOrderId =
            typeof poData === "object" && poData !== null && "id" in poData
              ? String(poData.id)
              : String(poData);

          console.log(
            "Purchase order created with ID:",
            purchaseOrderId,
            "Type:",
            typeof purchaseOrderId
          );

          // Upload the PO documents
          if (entry.files && entry.files.length > 0) {
            for (const file of entry.files) {
              const fileExt = file.name.split(".").pop();
              const filePath = `${purchaseOrderId}/${crypto.randomUUID()}.${fileExt}`;
              console.log("Uploading file to path:", filePath);

              // Upload the file to storage
              const { error: uploadError } = await supabase.storage
                .from("purchase_order_documents")
                .upload(filePath, file, {
                  cacheControl: "3600",
                  upsert: false,
                });

              if (uploadError) {
                console.error("Error uploading file to storage:", uploadError);
                toast.error(
                  `Failed to upload ${file.name}: ${uploadError.message}`
                );
                continue;
              }

              // Create a record in the documents table
              const { error: docError } = await supabase
                .from("purchase_order_attachments")
                .insert({
                  purchase_order_id: purchaseOrderId,
                  file_name: file.name,
                  file_path: filePath,
                  content_type: file.type,
                  doc_type: "PO_FILE",
                });

              if (docError) {
                console.error("Error creating document record:", docError);
                toast.error(
                  `Failed to record document ${file.name}: ${docError.message}`
                );
                continue;
              }
            }
          }

          // Upload additional documents if any
          if (entry.otherFiles && entry.otherFiles.length > 0) {
            for (const file of entry.otherFiles) {
              const fileExt = file.name.split(".").pop();
              const filePath = `${purchaseOrderId}/${crypto.randomUUID()}.${fileExt}`;
              console.log("Uploading additional file to path:", filePath);

              // Upload the file to storage
              const { error: uploadError } = await supabase.storage
                .from("purchase_order_documents")
                .upload(filePath, file, {
                  cacheControl: "3600",
                  upsert: false,
                });

              if (uploadError) {
                console.error(
                  "Error uploading additional file to storage:",
                  uploadError
                );
                toast.error(
                  `Failed to upload ${file.name}: ${uploadError.message}`
                );
                continue;
              }

              // Create a record in the documents table
              const { error: docError } = await supabase
                .from("purchase_order_attachments")
                .insert({
                  purchase_order_id: purchaseOrderId,
                  file_name: file.name,
                  file_path: filePath,
                  content_type: file.type,
                  doc_type: "Others",
                });

              if (docError) {
                console.error(
                  "Error creating additional document record:",
                  docError
                );
                toast.error(
                  `Failed to record document ${file.name}: ${docError.message}`
                );
                continue;
              }
            }
          }
        }
      }

      // Reset form after successful submission
      resetSelections();

      // Show success message
      toast.success("All PO entries submitted successfully!");

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Close modal
      onClose();
    } catch (error) {
      console.error("Error submitting PO entries:", error);
      toast.error("Failed to submit PO entries. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to fetch customers based on search term
  const fetchCustomers = async (searchTerm: string) => {
    setIsLoadingSuggestions(true);
    try {
      const { data: customersData, error } = await supabase
        .from("customer")
        .select("id, customer_full_name")
        .textSearch("customer_full_name", `${searchTerm}:*`, { type: "websearch" })
        .limit(10); // Limit results to improve performance

      if (error) {
        console.error("Error fetching customers:", error);
        return [];
      }

      const customerOptions: CustomerOption[] = customersData.map(
        (customer: any) => ({
          value: customer.id,
          label: customer.customer_full_name,
        })
      );

      return customerOptions;
    } catch (error) {
      console.error("Error fetching customers:", error);
      return [];
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  // Function to handle customer input and show suggestions
  const handleCustomerInput = async (value: string, index: number) => {
    const updatedEntries = [...poEntries];
    updatedEntries[index] = {
      ...updatedEntries[index],
      customerName: value,
      customerId: "", // Reset ID until a suggestion is selected
    };
    setPoEntries(updatedEntries);

    // Show suggestions only if input is 5 or more characters
    if (value.length >= 1) {
      // Fetch customers from the database based on search term
      const customerOptions = await fetchCustomers(value);
      setCustomerSuggestions(customerOptions);

      // Update show suggestions state
      const updatedShowSuggestions = { ...showSuggestions };
      updatedShowSuggestions[index] = customerOptions.length > 0;
      setShowSuggestions(updatedShowSuggestions);
    } else {
      // Hide suggestions if input is less than 5 characters
      const updatedShowSuggestions = { ...showSuggestions };
      updatedShowSuggestions[index] = false;
      setShowSuggestions(updatedShowSuggestions);
      setCustomerSuggestions([]);
    }
  };

  // Function to handle customer suggestion selection
  const handleSelectCustomer = (
    customerId: string,
    customerName: string,
    index: number
  ) => {
    const updatedEntries = [...poEntries];
    updatedEntries[index] = {
      ...updatedEntries[index],
      customerId: customerId,
      customerName: customerName,
      // Reset other fields
      enquiryIds: [],
      enquiryNames: [],
      poNumber: "",
      poFrequency: "",
      poValue: "",
      notes: "",
      // Keep files and otherFiles as they are
    };
    setPoEntries(updatedEntries);

    // Hide suggestions
    const updatedShowSuggestions = { ...showSuggestions };
    updatedShowSuggestions[index] = false;
    setShowSuggestions(updatedShowSuggestions);

    // Fetch enquiries for the selected customer
    if (customerId) {
      fetchEnquiriesForCustomer(customerId);
    }
  };

  // Function to fetch enquiries for a specific customer
  const fetchEnquiriesForCustomer = async (customerId: string) => {
    if (!customerId) {
      setEnquiryOptions([]);
      return;
    }

    setIsLoadingEnquiries(true);
    try {
      const { data: enquiries, error } = await supabase
        .from("enquiries")
        .select(
          "id, chemical_name, quantity, quantity_unit, procurement_volume, procurement_unit, enquiry_id"
        )
        .eq("customer_id", customerId)
        .not(
          "current_status",
          "in",
          "(quote_rejected,sample_rejected,cancelled,regret)"
        );

      if (error) {
        console.error("Error fetching enquiries:", error);
        toast.error("Failed to load enquiries");
        setEnquiryOptions([]);
        return;
      }

      const options = enquiries.map((enquiry: any) => ({
        value: enquiry.id,
        label: enquiry.chemical_name,
        chemical_name: enquiry.chemical_name,
        quantity: enquiry.quantity,
        quantity_unit: enquiry.quantity_unit,
        procurement_volume: enquiry.procurement_volume,
        procurement_unit: enquiry.procurement_unit,
        id: enquiry.id,
        enquiry_id: enquiry.enquiry_id,
      }));

      setEnquiryOptions(options);
    } catch (error) {
      console.error("Error fetching enquiries:", error);
      toast.error("Failed to load enquiries");
      setEnquiryOptions([]);
    } finally {
      setIsLoadingEnquiries(false);
    }
  };

  // Fetch enquiries when customer selection changes in any entry
  useEffect(() => {
    // Get unique customer IDs from entries
    const customerIds = [
      ...new Set(poEntries.map((entry) => entry.customerId).filter((id) => id)),
    ];

    // Fetch enquiries for each customer
    const fetchAllEnquiries = async () => {
      const allOptions: EnquiryOption[] = [];
      for (const customerId of customerIds) {
        // Use a local function to fetch enquiries for a specific customer
        const fetchEnquiries = async (customerId: string) => {
          if (!customerId) {
            return [];
          }

          setIsLoadingEnquiries(true);
          try {
            const { data: enquiries, error } = await supabase
              .from("enquiries")
              .select("*")
              .eq("customer_id", customerId)
              .not(
                "current_status",
                "in",
                "(quote_rejected,sample_rejected,cancelled,regret)"
              );

            if (error) {
              console.error("Error fetching enquiries:", error);
              toast.error("Failed to load enquiries");
              return [];
            }

            const options = enquiries.map((enquiry: any) => ({
              value: enquiry.id,
              label: enquiry.chemical_name,
              chemical_name: enquiry.chemical_name,
              quantity: enquiry.quantity,
              quantity_unit: enquiry.quantity_unit,
              procurement_volume: enquiry.procurement_volume,
              procurement_unit: enquiry.procurement_unit,
              id: enquiry.id,
              enquiry_id: enquiry.enquiry_id,
            }));

            return options;
          } catch (error) {
            console.error("Error fetching enquiries:", error);
            toast.error("Failed to load enquiries");
            return [];
          } finally {
            setIsLoadingEnquiries(false);
          }
        };

        const options = await fetchEnquiries(customerId);
        allOptions.push(...options);
      }
      setEnquiryOptions(allOptions);
    };

    fetchAllEnquiries();
  }, [poEntries]);

  // We're implementing the submission logic directly

  return (
    <Modal
      open={open}
      onCancel={() => {
        resetSelections();
        onClose();
      }}
      title={
        <div className="flex justify-between items-center w-full pr-8">
          <span className="text-base font-medium">Upload Purchase Order</span>
          <Button
            type="button"
            size="sm"
            onClick={addPoEntry}
            className="bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-full h-8 w-8 p-0 flex items-center justify-center"
          >
            <Plus className="h-5 w-5" />
          </Button>
        </div>
      }
      footer={null}
      width="90%"
      centered
      destroyOnClose={true}
      getContainer={document.body}
      closeIcon={<X className="h-4 w-4" />}
    >
      <form onSubmit={submitAllEntries} className="space-y-4">
        {/* Header row with field labels */}
        <div className="flex flex-wrap gap-1 mb-2 px-1">
          <div className="w-[16%]">
            <p className="text-sm font-medium m-0">
              Customer <span className="text-red-500">*</span>
            </p>
          </div>
          <div className="w-[14%]">
            <p className="text-sm font-medium m-0">
              Enquiries <span className="text-red-500">*</span>
            </p>
          </div>
          <div className="w-[10%]">
            <p className="text-sm font-medium m-0">
              PO Document <span className="text-red-500">*</span>
            </p>
          </div>
          <div className="w-[10%]">
            <p className="text-sm font-medium m-0">
              PO Number <span className="text-red-500">*</span>
            </p>
          </div>
          <div className="w-[8%]">
            <p className="text-sm font-medium m-0">
              PO Freq.(Days) <span className="text-red-500">*</span>
            </p>
          </div>
          <div className="w-[14%]">
            <p className="text-sm font-medium m-0">
              PO Value <span className="text-red-500">*</span>
            </p>
          </div>
          <div className="w-[10%]">
            <p className="text-sm font-medium m-0">Additional Docs</p>
          </div>
          <div className="w-[10%]">
            <p className="text-sm font-medium m-0">Remarks</p>
          </div>
          <div className="w-[2%]">
            {/* Empty header for delete icon column */}
          </div>
        </div>

        {/* Display PO entries */}
        {poEntries.map((entry, index) => (
          <div
            key={index}
            className="flex flex-wrap gap-1 py-4 px-1 border-t border-gray-200"
          >
            {/* Customer Selection */}
            <div className="w-[16%] relative">
              <Input
                type="text"
                value={entry.customerName}
                onChange={(e) => handleCustomerInput(e.target.value, index)}
                placeholder="Enter Customer Name"
                className={`w-full ${
                  validationErrors[index]?.customerId ? "border-red-500" : ""
                }`}
              />

              {/* Customer Suggestions */}
              {showSuggestions[index] && (
                <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
                  {isLoadingSuggestions ? (
                    <div className="px-4 py-2 text-center text-gray-500">
                      Loading...
                    </div>
                  ) : customerSuggestions.length > 0 ? (
                    customerSuggestions.map((customer) => (
                      <div
                        key={customer.value}
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() =>
                          handleSelectCustomer(
                            customer.value,
                            customer.label,
                            index
                          )
                        }
                      >
                        {customer.label}
                      </div>
                    ))
                  ) : (
                    <div className="px-4 py-2 text-center text-gray-500">
                      No customers found. Type to create a new one.
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Enquiry Selection */}
            <div className="w-[14%]">
              <Select
                mode="multiple"
                value={entry.enquiryIds}
                disabled={!entry.customerId} // Disable if no customer is selected
                className={
                  validationErrors[index]?.enquiryIds
                    ? "border-red-500 w-full"
                    : "w-full"
                }
                onChange={(values) => {
                  const updatedEntries = [...poEntries];
                  const selectedEnquiries = enquiryOptions.filter((e) =>
                    values.includes(e.value)
                  );
                  updatedEntries[index] = {
                    ...updatedEntries[index],
                    enquiryIds: values,
                    enquiryNames: selectedEnquiries.map(
                      (e) => e.chemical_name || "Unknown"
                    ),
                  };
                  setPoEntries(updatedEntries);
                }}
                style={{ width: "100%" }}
                optionLabelProp="label"
                placeholder="Select Multiple Enquiries"
                dropdownStyle={{ maxWidth: "300px" }}
                listItemHeight={30}
                showSearch
                filterOption={(input, option) => {
                  const chemicalName =
                    enquiryOptions.find((e) => e.value === option?.value)
                      ?.chemical_name || "";
                  return chemicalName
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
              >
                {enquiryOptions.map((enquiry) => (
                  <Select.Option
                    key={enquiry.value}
                    value={enquiry.value}
                    label={
                      <div className="flex flex-wrap items-center gap-2 max-w-[180px]">
                        <span className="font-medium break-words">
                          {enquiry.chemical_name || "N/A"}
                        </span>
                        {enquiry.quantity && enquiry.quantity_unit && (
                          <span className="text-sm text-gray-500 whitespace-nowrap flex-shrink-0">
                            ({enquiry.quantity} {enquiry.quantity_unit})
                          </span>
                        )}
                      </div>
                    }
                  >
                    <div className="p-3">
                      <div className="font-medium text-base mb-2 break-words">
                        {enquiry.chemical_name || "N/A"}
                      </div>
                      {enquiry.enquiry_id && (
                        <div className="flex gap-2 text-sm text-gray-600">
                          <span>{enquiry.enquiry_id}</span>
                        </div>
                      )}
                      {enquiry.quantity && enquiry.quantity_unit && (
                        <div className="flex gap-2 text-sm text-gray-600">
                          <span className="font-medium">Quantity:</span>
                          <span>
                            {enquiry.quantity} {enquiry.quantity_unit}
                          </span>
                        </div>
                      )}
                      {enquiry.procurement_volume &&
                        enquiry.procurement_unit && (
                          <div className="flex gap-2 text-sm text-gray-600">
                            <span className="font-medium">
                              Procurement Volume:
                            </span>
                            <span>
                              {enquiry.procurement_volume}{" "}
                              {enquiry.procurement_unit}
                            </span>
                          </div>
                        )}
                    </div>
                  </Select.Option>
                ))}
              </Select>
            </div>

            {/* PO Document Upload */}
            <div className="w-[10%]">
              <div
                className={`flex items-center justify-center h-8 px-3 border rounded-md ${
                  validationErrors[index]?.files
                    ? "border-red-500"
                    : "border-gray-300"
                } bg-white hover:bg-gray-50 cursor-pointer
                }`}
              >
                <label className="flex items-center justify-center w-full h-full cursor-pointer">
                  <input
                    type="file"
                    className="hidden"
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                    // disabled={!entry.customerId}
                    onChange={(e) => {
                      if (e.target.files && e.target.files.length > 0) {
                        const updatedEntries = [...poEntries];
                        updatedEntries[index] = {
                          ...updatedEntries[index],
                          files: Array.from(e.target.files),
                        };
                        setPoEntries(updatedEntries);
                      }
                    }}
                  />
                  <div className="flex items-center gap-2 text-sm text-gray-600 w-full">
                    <Upload className="h-4 w-4 flex-shrink-0" />
                    <span className="truncate max-w-[80px]">
                      {entry.files[0]?.name || "Upload PO"}
                    </span>
                  </div>
                </label>
              </div>
            </div>

            {/* PO Number */}
            <div className="w-[10%]">
              <Input
                value={entry.poNumber}
                // disabled={!entry.customerId}
                onChange={(e) => {
                  const updatedEntries = [...poEntries];
                  updatedEntries[index] = {
                    ...updatedEntries[index],
                    poNumber: e.target.value,
                  };
                  setPoEntries(updatedEntries);
                }}
                placeholder="Enter PO number"
                className={`w-full h-8 ${
                  validationErrors[index]?.poNumber ? "border-red-500" : ""
                }`}
              />
            </div>

            {/* PO Frequency */}
            <div className="w-[8%]">
              <Input
                value={entry.poFrequency}
                onChange={(e) => {
                  const updatedEntries = [...poEntries];
                  updatedEntries[index] = {
                    ...updatedEntries[index],
                    poFrequency: e.target.value,
                  };
                  setPoEntries(updatedEntries);
                }}
                placeholder="frequency in days"
                className={`w-full h-8 ${
                  validationErrors[index]?.poFrequency ? "border-red-500" : ""
                }`}
                type="number"
                min="1"
                step="1"
              />
            </div>

            {/* PO Value and Currency */}
            <div className="w-[14%]">
              <div className="flex">
                <Input
                  value={entry.poValue}
                  onChange={(e) => {
                    const updatedEntries = [...poEntries];
                    updatedEntries[index] = {
                      ...updatedEntries[index],
                      poValue: e.target.value,
                    };
                    setPoEntries(updatedEntries);
                  }}
                  placeholder="Enter value"
                  className={`w-[55%] h-8 rounded-r-none ${
                    validationErrors[index]?.poValue ? "border-red-500" : ""
                  }`}
                  type="number"
                  min="1"
                  step="1"
                />
                <Select
                  value={entry.poCurrency}
                  onChange={(value) => {
                    const updatedEntries = [...poEntries];
                    updatedEntries[index] = {
                      ...updatedEntries[index],
                      poCurrency: value,
                    };
                    setPoEntries(updatedEntries);
                  }}
                  className="w-[45%] h-8 rounded-l-none"
                  options={[
                    { value: "USD", label: "USD $" },
                    { value: "INR", label: "INR ₹" },
                    { value: "EURO", label: "EUR €" },
                    { value: "YUAN", label: "CNY ¥" },
                    { value: "YEN", label: "JPY ¥" },
                    { value: "AED", label: "AED د.إ" },
                  ]}
                />
              </div>
            </div>

            {/* Additional Documents */}
            <div className="w-[10%]">
              <div
                className={`flex items-center justify-center h-8 px-3 border border-gray-300 rounded-md
                    bg-white hover:bg-gray-50 cursor-pointer`}
              >
                <label className="flex items-center justify-center w-full h-full cursor-pointer">
                  <input
                    type="file"
                    className="hidden"
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                    multiple
                    // disabled={!entry.customerId}
                    onChange={(e) => {
                      if (e.target.files && e.target.files.length > 0) {
                        const updatedEntries = [...poEntries];
                        updatedEntries[index] = {
                          ...updatedEntries[index],
                          otherFiles: Array.from(e.target.files),
                        };
                        setPoEntries(updatedEntries);
                      }
                    }}
                  />
                  <div className="flex items-center gap-2 text-sm text-gray-600 w-full">
                    <FileText className="h-4 w-4 flex-shrink-0" />
                    <span className="truncate max-w-[80px]">
                      {entry.otherFiles?.length > 0
                        ? `${entry.otherFiles.length} files`
                        : "Upload"}
                    </span>
                  </div>
                </label>
              </div>
            </div>

            {/* Remarks field */}
            <div className="w-[10%]">
              <Input
                value={entry.notes}
                // disabled={!entry.customerId}
                onChange={(e) => {
                  const updatedEntries = [...poEntries];
                  updatedEntries[index] = {
                    ...updatedEntries[index],
                    notes: e.target.value,
                  };
                  setPoEntries(updatedEntries);
                }}
                placeholder="Enter remarks"
                className="w-full h-8"
              />
            </div>

            {/* Action button */}
            <div className="w-[2%] flex items-center justify-center">
              {poEntries.length > 1 && (
                <button
                  type="button"
                  onClick={() => removePoEntry(index)}
                  className="text-red-500 hover:text-red-700 flex-shrink-0"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
        ))}

        {/* Footer with buttons */}
        <div className="flex justify-end pt-4 border-t">
          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                resetSelections();
                onClose();
              }}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-[#294d48] hover:bg-[#1e3a36] text-white"
            >
              {isSubmitting ? "Submitting..." : "Submit"}
            </Button>
          </div>
        </div>
      </form>
    </Modal>
  );
};

export default CreatePurchaseOrderModal;
