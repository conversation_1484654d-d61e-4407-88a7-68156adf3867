
import { Download } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { toast } from "sonner";

interface PdfViewerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  url: string | null;
  fileName: string | null;
  isExternalUrl: boolean;
}

const PdfViewer = ({ 
  open, 
  onOpenChange, 
  url, 
  fileName, 
  isExternalUrl 
}: PdfViewerProps) => {
  const handleDownload = (url: string, fileName: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName || 'quotation-document.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success("Download started");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[80vw] max-h-[90vh] p-0">
        <DialogHeader className="p-4 flex-row items-center justify-between">
          <DialogTitle className="text-lg">{fileName || "Quotation Document"}</DialogTitle>
          {url && fileName && (
            <Button 
              variant="outline" 
              onClick={() => handleDownload(url, fileName)}
              className="ml-auto"
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          )}
        </DialogHeader>
        <div className="h-[75vh] w-full">
          {url && !isExternalUrl && (
            <iframe 
              src={`${url}#toolbar=0`}
              className="w-full h-full border-0"
              title="Quotation Document Viewer"
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PdfViewer;
