import React, { useState } from 'react';
import { Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

interface ClarificationQuery {
  id: string;
  enquiry_id: string;
  query: string;
  response?: string;
  status: "pending" | "resolved" | "rejected";
  created_at: string;
  resolved_at?: string;
}

interface ClarificationQueriesTableProps {
  queries: ClarificationQuery[];
  onSubmitResponse: (id: string, response: string) => void;
  isSubmitting: boolean;
}

const ClarificationQueriesTable: React.FC<ClarificationQueriesTableProps> = ({
  queries,
  onSubmitResponse,
  isSubmitting
}) => {
  const [responseText, setResponseText] = useState("");

  const handleSubmit = (id: string) => {
    if (responseText.trim()) {
      onSubmitResponse(id, responseText);
      setResponseText("");
    }
  };

  return (
    <div className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden mb-4">
      {/* Header */}
      {/* <div className="px-4 py-2 bg-gray-100/50 border-b border-gray-200">
        <h4 className="text-sm font-medium text-gray-700">Clarification Queries</h4>
      </div> */}

      {/* Table Container with horizontal scroll */}
      <div className="overflow-x-auto">
      <table className="w-full text-sm border border-gray-200 table-fixed">
          <thead className="bg-gray-100">
            <tr>
              <th className="p-2 text-left font-medium border-r w-1/2">Query</th>
              <th className="p-2 text-left font-medium w-1/2">Response</th>
            </tr>
          </thead>
          <tbody>
            {queries.map((query) => (
              <tr key={query.id}>
                <td className="p-2 border-r border-t bg-gray-50 w-1/2">
                  <div className="whitespace-normal break-words">
                    {query.query}
                  </div>
                </td>
                <td className="p-2 border-t bg-gray-50 w-1/2">
                  {query.response ? (
                    <div className="whitespace-normal break-words">
                      {query.response}
                    </div>
                  ) : (
                    <div className="flex flex-row gap-2">
                      <Textarea
                        placeholder="Type your response..."
                        value={responseText}
                        onChange={(e) => setResponseText(e.target.value)}
                        className="min-h-[36px] py-2 text-sm resize-none focus:ring-0 focus:ring-offset-0 focus:border-gray-300 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-gray-300"
                      />
                      <Button
                        size="sm"
                        onClick={() => handleSubmit(query.id)}
                        disabled={isSubmitting || !responseText.trim()}
                        className="flex items-center gap-1 self-end"
                      >
                        <Send className="h-3 w-3" />
                        Submit
                      </Button>
                    </div>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ClarificationQueriesTable;
