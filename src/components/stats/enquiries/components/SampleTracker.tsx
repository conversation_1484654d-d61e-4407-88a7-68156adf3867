
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Card } from "@/components/ui/card";
import TrackerHeader from "./sample-tracker/TrackerHeader";
import TrackerContent from "./sample-tracker/TrackerContent";
import { SampleTrackerProps } from "./sample-tracker/types";

const SampleTracker = ({ currentStatus, sampleStatus, sampleRequestId }: SampleTrackerProps) => {
  const [expanded, setExpanded] = useState(true);
  const [animate, setAnimate] = useState(false);
  
  // Use sampleStatus if available, otherwise fall back to currentStatus
  const effectiveStatus = sampleStatus || currentStatus || 'sample_requested';

  console.log("SampleTracker rendering with:", { 
    currentStatus, 
    sampleStatus, 
    effectiveStatus 
  });
  
  // Trigger animation when status changes
  useEffect(() => {
    setAnimate(true);
    const timer = setTimeout(() => setAnimate(false), 1000);
    return () => clearTimeout(timer);
  }, [effectiveStatus]);

  return (
    <Card className={cn(
      "border border-[#E6EAE9] shadow-sm overflow-hidden transition-all duration-300 bg-gradient-to-br from-white to-gray-50",
      animate && "animate-pulse-3-times"
    )}>
      <TrackerHeader 
        expanded={expanded} 
        toggleExpanded={() => setExpanded(!expanded)}
      />
      
      {expanded && (
        <TrackerContent effectiveStatus={effectiveStatus as string} sampleRequestId={sampleRequestId as string} />
      )}
    </Card>
  );
};

export default SampleTracker;
