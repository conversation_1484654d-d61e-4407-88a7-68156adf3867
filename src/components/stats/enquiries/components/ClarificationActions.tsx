
import { useState } from "react";
import { MessageSquare } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import ClarificationQueries from "./ClarificationQueries";

interface ClarificationActionsProps {
  enquiryId: string;
  clarificationQueries: any[];
  isLoadingClarifications: boolean;
  updateClarificationQuery: (params: { id: string; response: string; status: string }) => void;
}

const ClarificationActions = ({
  enquiryId,
  clarificationQueries,
  isLoadingClarifications,
  updateClarificationQuery
}: ClarificationActionsProps) => {
  const hasActiveClarifications = clarificationQueries?.some(q => q.status === 'pending');

  return (
    <div className="ml-8 mt-2 flex flex-col gap-3">
      {clarificationQueries && clarificationQueries.length > 0 && (
        <div className="flex items-center gap-2 px-3 py-2 bg-amber-50 text-amber-700 rounded-md border border-amber-200">
          <MessageSquare className="h-4 w-4" />
          <span className="text-sm font-medium">
            {clarificationQueries.length} {clarificationQueries.length === 1 ? 'Query' : 'Queries'}
          </span>
          {hasActiveClarifications && (
            <Badge className="bg-amber-100 text-amber-800 ml-1">Pending</Badge>
          )}
        </div>
      )}
      
      {clarificationQueries && clarificationQueries.length > 0 && (
        <div className="animate-in fade-in duration-300">
          <ClarificationQueries 
            queries={clarificationQueries}
            isLoading={isLoadingClarifications}
            onUpdateQuery={(id, response, status) => 
              updateClarificationQuery({ id, response, status })
            }
          />
        </div>
      )}
    </div>
  );
};

export default ClarificationActions;
