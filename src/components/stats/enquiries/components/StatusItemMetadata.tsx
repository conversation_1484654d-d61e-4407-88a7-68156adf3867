
import { format } from "date-fns";
import { User } from "lucide-react";

interface StatusItemMetadataProps {
  notes?: string;
  date?: string;
  timeDifference?: string;
  procurementPOC?: string;
  isActive: boolean;
}

const StatusItemMetadata = ({ 
  notes, 
  date, 
  timeDifference, 
  procurementPOC, 
  isActive 
}: StatusItemMetadataProps) => {
  return (
    <>
      {procurementPOC && (
        <div className="ml-0 p-2 bg-white/60 backdrop-blur-sm rounded-md border border-gray-100 text-sm flex items-center gap-2">
          <User className="h-4 w-4 text-[#7E69AB]" />
          <span className="text-gray-700">Assigned to: <strong>{procurementPOC}</strong></span>
        </div>
      )}
      
      {/* {notes && (
        <div className="ml-0 p-2 bg-white/60 backdrop-blur-sm rounded-md border border-gray-100 text-sm text-gray-600">
          {notes}
        </div>
      )} */}
      
      {date && (
        <div className="text-sm space-y-1 ml-0">
          <div className={`${isActive ? 'text-gray-700 font-medium' : 'text-gray-500'} bg-white/40 backdrop-blur-sm px-2 py-1 rounded-md inline-block`}>
            {format(new Date(date), "dd/MM/yyyy HH:mm")}
          </div>
          {timeDifference && (
            <div className="text-[#7E69AB] font-medium ml-2">
              {timeDifference}
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default StatusItemMetadata;
