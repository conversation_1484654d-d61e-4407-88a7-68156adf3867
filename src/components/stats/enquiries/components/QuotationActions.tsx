import { useState, useEffect } from "react";
import QuotationActionButtons from "./QuotationActionButtons";
import QuotationFeedbackDisplay from "./QuotationFeedbackDisplay";
import SampleRequestButton from "./SampleRequestButton";
import { But<PERSON> } from "@/components/ui/button";
import { Download, Eye, FileIcon, Paperclip } from "lucide-react";
import PurchaseOrderButton from "./PurchaseOrderButton";
import { supabase } from "@/integrations/supabase/client";
import { useQuotationViewer } from "../hooks/useQuotationViewer";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { QuoteGenerationAttachment } from "../types/quotationTypes";

interface QuotationActionsProps {
  enquiryId?: string;
  hasFeedback?: boolean;
  feedbackData?: {
    id?: string; // Add the feedback ID
    response: string;
    reason: string;
    created_at?: string;
  } | null;
  onSampleRequestSuccess?: () => void;
  currentStatus?: string;
  enquiryStatusId?: string;
}

interface QuoteAttachment {
  id: string;
  file_name: string;
  file_path: string;
  content_type: string;
  url: string;
}

interface QuoteAttachmentsModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  quoteGenerationId: string;
}

const QuoteAttachmentsModal: React.FC<QuoteAttachmentsModalProps> = ({
  isOpen,
  setIsOpen,
  quoteGenerationId
}) => {
  const [attachments, setAttachments] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Function to handle file download
  const handleDownload = async (file: QuoteAttachment) => {
    try {
      const { data, error } = await supabase.storage
        .from("quotation-documents")
        .download(file.file_path);

      if (error) {
        console.error('Download error:', error);
        toast.error('Failed to download file');
        return;
      }

      // Create a blob URL and trigger download
      const blob = new Blob([data], { type: data.type });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = file.file_name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('File downloaded successfully');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download file');
    }
  };

  useEffect(() => {
    const fetchAttachments = async () => {
      if (!quoteGenerationId) return;
      setIsLoading(true);

      try {
        const { data, error } = await supabase
          .from('quote_generation_attachments')
          .select('*')
          .eq('quote_generation_id', quoteGenerationId);

        if (error) {
          console.error("Error fetching attachments:", error);
          setAttachments([]);
          return;
        }

        const attachmentsWithUrls = await Promise.all(
          (data as any).map(async (item:any) => {
            const { data: urlData } = await supabase.storage
              .from("quotation-documents")
              .createSignedUrl(item.file_path, 3600);

            return {
              id: item.id,
              file_name: item.file_name,
              file_path: item.file_path,
              content_type: item.content_type,
              url: urlData?.signedUrl || ''
            } as QuoteAttachment;
          })
        );

        setAttachments(attachmentsWithUrls);
      } catch (error) {
        console.error("Error fetching attachments:", error);
        toast.error("Failed to load attachments");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) fetchAttachments();
  }, [isOpen, quoteGenerationId]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="max-w-md">
        <DialogTitle>Quotation Attachments</DialogTitle>
        <DialogDescription className="text-sm text-gray-500">
          Click "View" to open or "Download" to save the file.
        </DialogDescription>

        <ScrollArea className="mt-4 max-h-[300px]">
          {isLoading && (
            <p className="text-sm text-center py-2">Loading attachments...</p>
          )}

          {!isLoading && attachments.length > 0
            ? attachments.map((file) => (
                <div
                  key={file.id}
                  className="flex justify-between items-center p-3 bg-gray-100 rounded-md mb-2"
                >
                  <div className="text-sm truncate flex-1 flex items-center">
                    <FileIcon className="w-4 h-4 mr-2" />
                    {file.file_name}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(file.url, "_blank")}
                    >
                      <Eye className="w-4 h-4 mr-1" /> View
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleDownload(file)}
                    >
                      <Download className="w-4 h-4 mr-1" /> Download
                    </Button>
                  </div>
                </div>
              ))
            : !isLoading && (
                <p className="text-sm text-center py-2 text-gray-500">
                  No attachments found
                </p>
              )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

const QuotationActions = ({
  enquiryId,
  hasFeedback,
  feedbackData,
  onSampleRequestSuccess,
  currentStatus,
  enquiryStatusId,
}: QuotationActionsProps) => {
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [hadFeedbackData, SetHasFeedbackData] = useState(false);
  const [feedback, setFeedback] = useState(null);
  const [status, setStatus] = useState("");
  const [showAttachments, setShowAttachments] = useState(false);
  const [quoteGenerationId, setQuoteGenerationId] = useState<any>(
    null
  );
  const { handleViewQuotation: viewQuotation } = useQuotationViewer();

  useEffect(() => {
    // Check if sample is already requested
    const fetchEnquiryStatus = async () => {
      const { data, error } = await supabase
        .from("get_enquiry_board")
        .select("current_status")
        .eq("id", enquiryId)
        .single();

      if (error) {
        console.error("Failed to fetch current status:", error);
        throw error;
      }

      console.log("Fetched current status:", data?.current_status);
      setStatus(data?.current_status);
    };

    const fetchFeedback = async () => {
      console.log("### id", enquiryStatusId, status);
      const { data, error } = await supabase
        .from("quotation_feedback")
        .select("id, created_at, reason, response,remarks")
        .eq("pricing_quote_id", enquiryStatusId)
        .single(); // Ensures only one record is returned

      if (error) {
        console.error("Error fetching feedback: ###", error);
      } else {
        console.log("Feedback: ###", data);
        setFeedback(data);
        SetHasFeedbackData(true);
      }
    };

    fetchFeedback(); // Call the async function
    fetchEnquiryStatus();
  }, [showFeedbackForm]); // Empty dependency array ensures it runs only once on mount

  useEffect(() => {
    const fetchQuoteGenerationId = async () => {
      if (!enquiryStatusId) return;

      const { data, error } = await supabase
        .from("quote_generation_details")
        .select("id")
        .eq("status_history_id", enquiryStatusId)
        .single();

      if (error) {
        console.error("Error fetching quote generation id:", error);
        return;
      }

      if (data) {
        setQuoteGenerationId(data.id);
      }
    };

    fetchQuoteGenerationId();
  }, [enquiryStatusId]);

  // Only proceed if we have an enquiry ID
  if (!enquiryId) {
    console.log("QuotationActions: No enquiryId provided");
    return null;
  }

  // Format timestamp from feedbackData if available
  const formatDate = (dateString?: string) => {
    if (!dateString) return "";
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  // Handle viewing the quotation
  const handleViewQuotation = async () => {
    console.log(
      "[DEBUG] Starting handleViewQuotation with enquiryStatusId:",
      enquiryStatusId
    );

    if (!enquiryStatusId) {
      console.error("[ERROR] No status history ID provided");
      return;
    }

    try {
      console.log(
        "[DEBUG] Fetching quotation details for status_history_id:",
        enquiryStatusId
      );

      console.log(
        "[DEBUG] Fetching quotation details for status_history_id:",
        enquiryStatusId
      );

      // Get the quotation details using status_history_id
      const { data, error } = await supabase
        .from("quote_generation_details")
        .select("pdf_file_path")
        .eq("status_history_id", enquiryStatusId)
        .single();

      console.log("[DEBUG] Quote generation details response:", {
        data,
        error,
      });

      if (error || !data?.pdf_file_path) {
        console.error("[ERROR] No quotation found:", error);
        toast.error("No quotation document found for this enquiry");
        return;
      }

      const filePath = data.pdf_file_path;
      console.log("[DEBUG] Retrieved file path:", filePath);

      // If it's an external URL, open directly
      if (filePath.startsWith("http")) {
        console.log("[DEBUG] Opening external URL directly:", filePath);
        window.open(filePath, "_blank");
        return;
      }

      console.log("[DEBUG] Generating signed URL for file path:", filePath);

      // Get signed URL for Supabase storage file
      const { data: urlData, error: urlError } = await supabase.storage
        .from("quotation-documents")
        .createSignedUrl(filePath, 3600);

      console.log("[DEBUG] Signed URL response:", { urlData, urlError });

      if (urlError || !urlData?.signedUrl) {
        console.error("[ERROR] Failed to generate URL:", urlError);
        toast.error("Failed to generate document URL");
        return;
      }

      console.log(
        "[DEBUG] Opening document with signed URL:",
        urlData.signedUrl
      );

      // Open the document in a new tab
      window.open(urlData.signedUrl, "_blank");
    } catch (error) {
      console.error("[ERROR] Unexpected error in handleViewQuotation:", error);
      toast.error("Failed to view quotation document");
    }
  };

  // If feedback exists, display it
  if (hadFeedbackData) {
    console.log(`Rendering feedback display for enquiry ${enquiryId}`);
    return (
      <div className="w-full">
        {feedback && (
          <QuotationFeedbackDisplay
            response={feedback?.response}
            reason={feedback?.reason}
            remarks={feedback?.remarks}
            date={formatDate(feedback?.created_at)}
            entityId={enquiryId}
            feedbackId={feedback?.id}
            type="quotation"
          />
        )}

        <div className="flex gap-2 mt-2">
          <Button
            variant="outline"
            size="sm"
            className="bg-green hover:bg-white-50 text-green-900 border-green-200 flex items-center gap-1.5"
            onClick={handleViewQuotation}
          >
            <Eye className="h-4 w-4" />
            View Quotation
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="bg-green hover:bg-white-50 text-gray-700 border-green-200 flex items-center gap-1.5"
            onClick={() => setShowAttachments(true)}
          >
            <Paperclip className="h-4 w-4" />
           Additional Attachments
          </Button>
        </div>

        {/* Attachments Modal */}
        {quoteGenerationId && (
          <QuoteAttachmentsModal
            isOpen={showAttachments}
            setIsOpen={setShowAttachments}
            quoteGenerationId={quoteGenerationId}
          />
        )}

        {/* Show Sample Request Button for accepted quotes */}
        {feedback.response === "quote_accepted" &&
          status != "po_raised" &&
          status != "sample_requested" &&
          status != "sample_delivered" &&
          status != "sample_accepted" && (
            <SampleRequestButton
              enquiryId={enquiryId}
              feedbackData={feedback}
              onSampleRequestSuccess={onSampleRequestSuccess}
              currentStatus={status}
              statushistoryId={enquiryStatusId}
            />
          )}

        {/* Show Sample Request Button for accepted quotes
        {feedback.response === "quote_accepted" &&
          status != "po_raised" &&
          status != "sample_requested" &&
          status != "sample_delivered" &&
          status != "sample_accepted" &&(
            <PurchaseOrderButton
              enquiryId={enquiryId}
              onSuccess={onSampleRequestSuccess}
            />
          )} */}
      </div>
    );
  }

  // If no feedback yet, show action buttons
  console.log(
    `Rendering action buttons for enquiry ${enquiryId} (no feedback yet)`
  );
  return (
    <div>
      <div className="flex gap-2 mt-2">
        <Button
          variant="outline"
          size="sm"
          className="bg-white hover:bg-green-50 text-green-700 border-green-200 flex items-center gap-1.5"
          onClick={handleViewQuotation}
        >
          <Eye className="h-4 w-4" />
          View Quotation
        </Button>

        <Button
          variant="outline"
          size="sm"
          className="bg-green hover:bg-white-50 text-gray-700 border-green-200 flex items-center gap-1.5"
          onClick={() => setShowAttachments(true)}
        >
          <Paperclip className="h-4 w-4" />
          Additional Attachments
        </Button>
      </div>

      {/* Attachments Modal */}
      {quoteGenerationId && (
        <QuoteAttachmentsModal
          isOpen={showAttachments}
          setIsOpen={setShowAttachments}
          quoteGenerationId={quoteGenerationId}
        />
      )}

      {status === "pricing_quotation_generated" && (
        <QuotationActionButtons
          enquiryId={enquiryId}
          showFeedbackForm={showFeedbackForm}
          setShowFeedbackForm={setShowFeedbackForm}
          onSuccess={onSampleRequestSuccess}
          enquiryStatusId={enquiryStatusId}
        />
      )}
    </div>
  );
};

export default QuotationActions;
