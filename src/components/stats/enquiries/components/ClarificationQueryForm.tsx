
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useClarificationQueries } from "../hooks/useClarificationQueries";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface ClarificationQueryFormProps {
  enquiryId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const ClarificationQueryForm = ({ 
  enquiryId, 
  onSuccess, 
  onCancel 
}: ClarificationQueryFormProps) => {
  const [query, setQuery] = useState("");
  const { addClarificationQuery, isSubmitting } = useClarificationQueries(enquiryId);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!query.trim()) {
      toast.error("Please enter a clarification query");
      return;
    }
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      const createdBy = session?.user.email || "Unknown";
      
      await addClarificationQuery({ 
        query: query.trim(), 
        createdBy 
      });
      
      // Update the enquiry status to clarification_needed
      const { error: statusError } = await supabase
        .from("enquiry_status_history")
        .insert({
          enquiry_id: enquiryId,
          status: "clarification_needed",
          changed_by: session.user.email || "",
          sales_agent_email: session?.user.email || "",
          notes: `Clarification needed: ${query.substring(0, 50)}${query.length > 50 ? '...' : ''}`
        });
        
      if (statusError) {
        console.error("Error updating enquiry status:", statusError);
        toast.error("Failed to update enquiry status");
      } else {
        // Update the current_status in the enquiries table
        const { error: enquiryError } = await supabase
          .from("enquiries")
          .update({ 
            current_status: "clarification_needed", 
            last_status_change: new Date().toISOString() 
          })
          .eq("id", enquiryId);
          
        if (enquiryError) {
          console.error("Error updating enquiry current status:", enquiryError);
        }
      }
      
      setQuery("");
      if (onSuccess) onSuccess();
    } catch (error) {
      console.error("Error in handleSubmit:", error);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4 p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
      <div>
        <h3 className="font-medium text-gray-900 mb-2">Request Clarification</h3>
        <Textarea
          placeholder="Describe what needs clarification..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="min-h-24 w-full"
          disabled={isSubmitting}
        />
      </div>
      
      <div className="flex justify-end space-x-2">
        {onCancel && (
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
        )}
        
        <Button 
          type="submit" 
          disabled={isSubmitting || !query.trim()}
        >
          {isSubmitting ? "Submitting..." : "Submit Clarification"}
        </Button>
      </div>
    </form>
  );
};

export default ClarificationQueryForm;
