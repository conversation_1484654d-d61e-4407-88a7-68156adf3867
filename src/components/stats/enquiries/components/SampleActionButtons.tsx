import React from "react";
import { Button } from "@/components/ui/button";
import { MessageSquare } from "lucide-react";
import SampleFeedbackForm from "./SampleFeedbackForm";

export interface SampleActionButtonsProps {
  enquiryId: string;
  showFeedbackForm: boolean;
  setShowFeedbackForm: React.Dispatch<React.SetStateAction<boolean>>;
  onSuccess?: () => void;
  enquiryStatusId?: string;
}

const SampleActionButtons = ({
  enquiryId,
  showFeedbackForm,
  setShowFeedbackForm,
  onSuccess,
  enquiryStatusId
}: SampleActionButtonsProps) => {

  // If feedback form is visible, show it
  if (showFeedbackForm) {
    return (
      <SampleFeedbackForm
        onClose={() => setShowFeedbackForm(false)}
        enquiryId={enquiryId}
        onSuccess={onSuccess}
        enquiryStatusId={enquiryStatusId}
      />
    );
  }

  // Otherwise show single action button
  return (
    <div className="mt-2">
      <Button
        variant="outline"
        size="sm"
        className="bg-white hover:bg-indigo-50 text-indigo-700 border-indigo-200 flex items-center gap-1.5"
        onClick={() => setShowFeedbackForm(true)}
      >
        <MessageSquare className="h-4 w-4" />
        Provide Sample Feedback
      </Button>
    </div>
  );
};

export default SampleActionButtons;
