import { format } from "date-fns";
import {
  ThumbsUp,
  ThumbsDown,
  RefreshCw,
  MessageSquare,
  Paperclip,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Toolt<PERSON>,
  TooltipContent,
  Too<PERSON><PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useState } from "react";
import FileAttachmentModal from "../components/FileAttachmentModal";
import { useQuotationViewer } from "../hooks/useQuotationViewer";

interface QuotationFeedbackDisplayProps {
  response: string;
  reason?: string;
  remarks?: string;
  date?: string;
  entityId?: string;
  feedbackId?: string;
  type?:string;
}

const QuotationFeedbackDisplay = ({
  response,
  reason,
  remarks,
  date,
  feedbackId,
  type
}: QuotationFeedbackDisplayProps) => {

  const [isOpen, setIsOpen] = useState(false);

  const getFeedbackIcon = (response: string) => {
    switch (response) {
      case "quote_accepted":
        return <ThumbsUp className="h-4 w-4 text-green-600" />;
      case "quote_rejected":
        return <ThumbsDown className="h-4 w-4 text-red-600" />;
      case "quote_redo":
        return <RefreshCw className="h-4 w-4 text-amber-600" />;
      case "sample_accepted":
        return <ThumbsUp className="h-4 w-4 text-green-600" />;
      case "sample_rejected":
        return <ThumbsDown className="h-4 w-4 text-red-600" />;
      case "sample_redo":
        return <RefreshCw className="h-4 w-4 text-amber-600" />;
      default:
        return <MessageSquare className="h-4 w-4 text-gray-600" />;
    }
  };

  const getFeedbackLabel = (response: string) => {
    switch (response) {
      case "quote_accepted":
        return "Quotation Accepted";
      case "quote_rejected":
        return "Quotation Rejected";
      case "quote_redo":
        return "New Quotation Requested";
      case "sample_accepted":
        return "Sample Accepted";
      case "sample_rejected":
        return "Sample Rejected";
      case "sample_redo":
        return "New Sample Requested";
      default:
        return "Feedback Provided";
    }
  };

  const getFeedbackColor = (response: string) => {
    switch (response) {
      case "quote_accepted":
      case "sample_accepted":
        return "bg-green-50 text-green-700 border-green-200";
      case "quote_rejected":
      case "sample_rejected":
        return "bg-red-50 text-red-700 border-red-200";
      case "quote_redo":
      case "sample_redo":
        return "bg-amber-50 text-amber-700 border-amber-200";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200";
    }
  };

  return (
    <div
      className={`p-3 rounded-md border animate-in fade-in duration-300 ${getFeedbackColor(
        response
      )}`}
    >
      <div className="flex items-center gap-2 mb-2">
        {getFeedbackIcon(response)}
        <span className="font-medium">{getFeedbackLabel(response)}</span>

        {date && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge
                  variant="outline"
                  className="ml-auto text-xs bg-white/60 hover:bg-white/80"
                >
                  {format(new Date(date), "dd/MM/yyyy")}
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                {format(new Date(date), "dd MMM yyyy, HH:mm")}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
      <div className="flex items-center justify-between w-full mt-2">
        {/* Reason Text - Takes 80% Width */}
        {reason && (
          <div className="w-4/5">
            <span className="text-xs font-medium text-gray-600">Reason: </span>
            <p className="text-sm break-words">{reason}</p>
          </div>
        )}
        
        {remarks && (
          <div className="w-4/5">
            <span className="text-xs font-medium text-gray-600">Remarks: </span>
            <p className="text-sm line-clamp-2">{remarks}</p>
          </div>
        )}

        {/* Attachment Icon - Takes 20% Width */}
        <div className="flex justify-end w-1/5 text-gray-500">
          <Paperclip 
            className="w-4 h-4 cursor-pointer hover:text-gray-700" 
            onClick={() => setIsOpen(true)}
          />
        </div>
        {/* File Attachment Modal */}
        {isOpen && (
          <FileAttachmentModal 
            isOpen={isOpen} 
            setIsOpen={setIsOpen} 
            id={feedbackId}
            type={type}
          />
        )}
      </div>
    </div>
  );
};

export default QuotationFeedbackDisplay;
