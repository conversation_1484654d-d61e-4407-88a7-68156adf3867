import React from 'react';
import { Paperclip } from 'lucide-react';

interface QuotationFeedbackTableProps {
  feedbackData: any;
  onOpenAttachments: () => void;
}

const QuotationFeedbackTable: React.FC<QuotationFeedbackTableProps> = ({
  feedbackData,
  onOpenAttachments
}) => {
  return (
    <div className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden mb-4">
      {/* Header */}
      {/* <div className="px-4 py-2 bg-gray-100/50 border-b border-gray-200">
        <h4 className="text-sm font-medium text-gray-700">Quotation Feedback</h4>
      </div> */}

      {/* Table Container with horizontal scroll */}
      <div className="overflow-x-auto">
        <table className="w-full text-sm border border-gray-200">
          <thead className="bg-gray-100">
            <tr>
              <th className="p-2 text-left font-medium border-r">Response</th>
              <th className="p-2 text-left font-medium border-r">Reason</th>
              <th className="p-2 text-left font-medium border-r">Remarks</th>
              <th className="p-2 text-left font-medium">Attachments</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="p-2 border-r border-t bg-gray-50">
                <span className="capitalize">
                  {feedbackData?.response?.replace(/_/g, " ") || '-'}
                </span>
              </td>
              <td className="p-2 border-r border-t bg-gray-50">
                {feedbackData?.reason || '-'}
              </td>
              <td className="p-2 border-r border-t bg-gray-50">
                {feedbackData?.remarks || '-'}
              </td>
              <td className="p-2 border-t bg-gray-50">
                <button
                  className="flex items-center gap-1.5 text-gray-600 hover:text-gray-900 px-2 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md"
                  onClick={onOpenAttachments}
                >
                  <Paperclip className="h-3.5 w-3.5" />
                  <span>View</span>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default QuotationFeedbackTable;
