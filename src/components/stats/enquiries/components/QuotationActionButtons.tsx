import React from "react";
import { Button } from "@/components/ui/button";
import { FileText } from "lucide-react";
import QuotationFeedbackForm from "../QuotationFeedbackForm";

export interface QuotationActionButtonsProps {
  enquiryId: string;
  showFeedbackForm: boolean;
  setShowFeedbackForm: React.Dispatch<React.SetStateAction<boolean>>;
  onSuccess?: () => void;
  enquiryStatusId?: string
}

const QuotationActionButtons = ({
  enquiryId,
  showFeedbackForm,
  setShowFeedbackForm,
  onSuccess,
  enquiryStatusId
}: QuotationActionButtonsProps) => {
  console.log("QuotationActionButtons for enquiryId:", enquiryId, "showFeedbackForm:", showFeedbackForm);
  
  // If feedback form is visible, show it
  if (showFeedbackForm) {
    console.log("Showing feedback form for enquiryId:", enquiryId);
    return (
      <QuotationFeedbackForm
        onClose={() => {
          console.log("Closing feedback form");
          setShowFeedbackForm(false);
        }}
        enquiryId={enquiryId}
        onSuccess={onSuccess}
        enquiryStatusId={enquiryStatusId}
      />
    );
  }

  // Otherwise show a single action button
  return (
    <div>
      <Button
        variant="outline"
        size="sm"
        className="bg-white hover:bg-green-50 text-green-700 border-green-200 flex items-center gap-1.5"
        onClick={() => {
          console.log("Opening feedback form for enquiryId:", enquiryId);
          setShowFeedbackForm(true);
        }}
      >
        <FileText className="h-4 w-4" />
        Provide Quotation Feedback
      </Button>
    </div>
  );
};

export default QuotationActionButtons;
