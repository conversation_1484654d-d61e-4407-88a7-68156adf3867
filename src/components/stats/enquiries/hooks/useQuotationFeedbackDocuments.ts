// import { useState, useEffect } from "react";
// import { useQuery } from "@tanstack/react-query";
// import { supabase, STORAGE_BUCKETS } from "@/integrations/supabase/client";

// export type QuotationFeedbackDocument = {
//   id: string;
//   file_name: string;
//   file_path: string;
//   content_type: string;
//   url?: string;
// };

// export const useQuotationFeedbackDocuments = (enquiryId?: string) => {
//   const [documents, setDocuments] = useState<QuotationFeedbackDocument[]>([]);

//   const { data, isLoading, error, refetch } = useQuery({
//     queryKey: ["quotation-feedback-documents", enquiryId],
//     queryFn: async () => {
//       if (!enquiryId) return null;

//       console.log("[DEBUG] Fetching feedback for enquiry ID:", enquiryId);

//       try {
//         // STEP 1: Get the feedback ID for this enquiry
//         const { data: feedbackData, error: feedbackError } = await supabase
//           .from("quotation_feedback")
//           .select("id")
//           .eq("enquiry_id", enquiryId)
//           .maybeSingle();

//         if (feedbackError) {
//           console.error("[ERROR] Error fetching feedback ID:", feedbackError);
//           throw new Error(`Failed to fetch feedback ID: ${feedbackError.message}`);
//         }

//         if (!feedbackData) {
//           console.log("[INFO] No feedback found for enquiry:", enquiryId);
//           return [];
//         }

//         const feedbackId = feedbackData.id;
//         console.log("[DEBUG] Found feedback ID:", feedbackId);

//         // STEP 2: Get the attachments for this feedback ID
//         const { data: attachments, error: attachmentsError } = await supabase
//           .from("quotation_feedback_attachments")
//           .select("*")
//           .eq("feedback_id", feedbackId);

//         if (attachmentsError) {
//           console.error("[ERROR] Error fetching attachments:", attachmentsError);
//           throw new Error(`Failed to fetch attachments: ${attachmentsError.message}`);
//         }

//         console.log(`[DEBUG] Found ${attachments?.length || 0} attachments for feedback ID:`, feedbackId);
        
//         if (!attachments || attachments.length === 0) {
//           console.log("[INFO] No attachments found for feedback ID:", feedbackId);
//           return [];
//         }

//         return attachments;
//       } catch (error: any) {
//         console.error("[ERROR] Error in useQuotationFeedbackDocuments:", error);
//         throw new Error(error.message || "Failed to fetch quotation feedback documents");
//       }
//     },
//     enabled: !!enquiryId,
//     retry: 3,
//     retryDelay: (attempt) => Math.min(attempt > 1 ? 2000 : 1000, 30 * 1000),
//     staleTime: 5 * 60 * 1000, // Cache for 5 minutes
//   });

//   useEffect(() => {
//     const processDocuments = async () => {
//       if (!data || data.length === 0) {
//         setDocuments([]);
//         return;
//       }

//       try {
//         console.log("[DEBUG] Processing documents:", data.length);

//         const processedDocs = await Promise.all(
//           data.map(async (doc) => {
//             try {
//               console.log("[DEBUG] Generating public URL for:", doc.file_name, "Path:", doc.file_path);

//               const { data: signedUrlData } =
//                 await supabase.storage
//                   .from(STORAGE_BUCKETS.QUOTATION_FEEDBACK_DOCUMENTS)
//                   .getPublicUrl(doc.file_path); // Use createSignedUrl if needed

//               return {
//                 ...doc,
//                 url: signedUrlData?.publicUrl,
//               };
//             } catch (err) {
//               console.error("[ERROR] Error processing document:", doc.file_name, err);
//               return { ...doc, url: undefined };
//             }
//           })
//         );

//         console.log("[DEBUG] Processed documents:", processedDocs.length, "documents");
//         setDocuments(processedDocs);
//       } catch (err) {
//         console.error("[ERROR] Error processing documents:", err);
//         setDocuments([]);
//       }
//     };

//     processDocuments();
//   }, [data]);

//   return {
//     documents,
//     isLoading,
//     error,
//     refetch,
//   };
// };
