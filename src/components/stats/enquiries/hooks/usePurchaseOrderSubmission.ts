import { useState } from "react";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export const STORAGE_BUCKET_PO = "purchase_order_documents";

// Define type for the RPC function response
type PurchaseOrderResponse = {
  id: string;
};

export const usePurchaseOrderSubmission = (
  enquiryId?: string,
  customerId?: string, // Add this parameter
  onClose?: () => void,
  onSuccess?: () => void,
  setSelectedCustomer?: (value: string) => void,  // Add these parameters
  setSelectedEnquiry?: (value: string) => void
) => {
  const [notes, setNotes] = useState<string>("");
  const [files, setFiles] = useState<File[]>([]);
  const [otherfiles, setOtherFiles] = useState<File[]>([]);
  const [ponumber, setPoNumber] = useState("");
  const [poFrequency, setPoFrequency] = useState("");

  const submitPurchaseOrderMutation = useMutation({
    mutationFn: async () => {
      console.log("Starting purchase order submission process");

      if (!enquiryId) {
        throw new Error("Missing enquiry ID");
      }

      if (!ponumber) {
        throw new Error("Please Enter The PO Number");
      }

      if (files.length === 0) {
        throw new Error("Please attach at least one PO document");
      }

      try {
        // Check if PO number already exists
        const { data: existingPOs, error: poCheckError } = await supabase
          .from("purchase_orders")
          .select("id")
          .eq("po_number", ponumber);

        if (poCheckError) {
          console.error("Error checking PO number:", poCheckError);
          throw new Error("Failed to validate PO number. Please try again.");
        }

        if (existingPOs && existingPOs.length > 0) {
          throw new Error(`Purchase Order number ${ponumber} already exists. Please use a unique PO number.`);
        }

        // Check authentication status
        const { data: authData } = await supabase.auth.getSession();

        if (!authData.session) {
          throw new Error(
            "Authentication required. Please log in to submit a purchase order."
          );
        }

        // First check the current status of the enquiry
        const { data: enquiryData, error: enquiryError } = await supabase
          .from("enquiries")
          .select("current_status")
          .eq("id", enquiryId)
          .single();

        if (enquiryError) {
          throw new Error(`Failed to check enquiry status: ${enquiryError.message}`);
        }

        // If current status is pricing_quotation_generated, first add quote_accepted status
        if (enquiryData.current_status === "pricing_quotation_generated") {
          const { error: quoteAcceptedError } = await supabase
            .from("enquiry_status_history")
            .insert({
              enquiry_id: enquiryId,
              status: "quote_accepted",
              changed_by: authData.session.user.email,
              sales_agent_email: authData.session.user.email,
              notes: "Quotation accepted by customer through direct PO"
            });

          if (quoteAcceptedError) {
            throw new Error(`Failed to create quote accepted status: ${quoteAcceptedError.message}`);
          }
        }

        // If current status is sample_delivered, first add sample_accepted status
        if (enquiryData.current_status === "sample_delivered") {
          const { error: sampleAcceptedError } = await supabase
            .from("enquiry_status_history")
            .insert({
              enquiry_id: enquiryId,
              status: "sample_accepted",
              changed_by: authData.session.user.email,
              sales_agent_email: authData.session.user.email,
              notes: "Sample accepted by customer through direct PO"
            });

          if (sampleAcceptedError) {
            throw new Error(`Failed to create sample accepted status: ${sampleAcceptedError.message}`);
          }
        }

        // Then create the PO raised status history record
        const { data: historyData, error: historyError } = await supabase
          .from("enquiry_status_history")
          .insert({
            enquiry_id: enquiryId,
            status: "po_raised",
            changed_by: authData.session.user.email,
            sales_agent_email: authData.session.user.email,
            notes: "Purchase order raised"
          })
          .select("id")
          .single();

        if (historyError) {
          throw new Error(`Failed to create status history: ${historyError.message}`);
        }

        const historyStatusId = historyData.id;

        // Then update enquiry status
        const { error } = await supabase
          .from("enquiries")
          .update({
            current_status: "po_raised",
            last_status_change: new Date().toISOString(),
          })
          .eq("id", enquiryId);

        if (error) {
          throw new Error("Failed to raise PO. Please try again.");
        }

        // Finally create the purchase order with the history status ID
        const { data: poData, error: poError } = await supabase.rpc(
          "create_purchase_order",
          {
            p_enquiry_id: enquiryId,
            p_notes: notes,
            p_created_by: authData.session.user.id,
            p_po_number: ponumber,
            p_customer_id: customerId,
            p_history_status_id: historyStatusId  // Add the history status ID
          } as any
        );

        if (poError) {
          console.error("Error creating purchase order:", poError);
          throw new Error(
            `Failed to create purchase order: ${poError.message}`
          );
        }

        if (!poData) {
          throw new Error("Failed to create purchase order: No ID returned");
        }

        const purchaseOrderId = (poData as PurchaseOrderResponse).id;
        console.log("Purchase order created with ID:", purchaseOrderId);

        // Upload the PO documents
        let uploadErrors = 0;
        let successfulUploads = 0;

        for (const file of files) {
          console.log(
            `Uploading file: ${file.name} (${file.size} bytes, type: ${file.type})`
          );
          const fileExt = file.name.split(".").pop();
          const filePath = `${purchaseOrderId}/${crypto.randomUUID()}.${fileExt}`;

          // First upload the file to storage
          const { error: uploadError } = await supabase.storage
            .from(STORAGE_BUCKET_PO)
            .upload(filePath, file, {
              cacheControl: "3600",
              upsert: false,
            });

          if (uploadError) {
            console.error("Error uploading file to storage:", uploadError);
            toast.error(
              `Failed to upload ${file.name}: ${uploadError.message}`
            );
            uploadErrors++;
            continue;
          }

          console.log(
            `Successfully uploaded file to ${filePath}, now recording metadata`
          );

          // Then record the attachment metadata using RPC
          const { data: attachmentData, error: attachmentError } =
            await supabase.rpc("create_purchase_order_attachment", {
              p_purchase_order_id: purchaseOrderId,
              p_file_name: file.name,
              p_file_path: filePath,
              p_content_type: file.type,
              p_size: file.size,
              p_doc_type: "PO_FILE", // <-- Add this field
            });

          if (attachmentError) {
            console.error(
              "Error recording attachment metadata:",
              attachmentError
            );
            toast.error(
              `Failed to record metadata for ${file.name}: ${attachmentError.message}`
            );
            uploadErrors++;
          } else {
            console.log(
              `Successfully recorded metadata for ${file.name}:`,
              attachmentData
            );
            successfulUploads++;
          }
        }

        for (const file of otherfiles) {
          console.log(
            `Uploading file: ${file.name} (${file.size} bytes, type: ${file.type})`
          );
          const fileExt = file.name.split(".").pop();
          const filePath = `${purchaseOrderId}/${crypto.randomUUID()}.${fileExt}`;

          // First upload the file to storage
          const { error: uploadError } = await supabase.storage
            .from(STORAGE_BUCKET_PO)
            .upload(filePath, file, {
              cacheControl: "3600",
              upsert: false,
            });

          if (uploadError) {
            console.error("Error uploading file to storage:", uploadError);
            toast.error(
              `Failed to upload ${file.name}: ${uploadError.message}`
            );
            uploadErrors++;
            continue;
          }

          console.log(
            `Successfully uploaded file to ${filePath}, now recording metadata`
          );

          // Then record the attachment metadata using RPC
          const { data: attachmentData, error: attachmentError } =
            await supabase.rpc("create_purchase_order_attachment", {
              p_purchase_order_id: purchaseOrderId,
              p_file_name: file.name,
              p_file_path: filePath,
              p_content_type: file.type,
              p_size: file.size,
              p_doc_type: "Others", // <-- Add this field
            });

          if (attachmentError) {
            console.error(
              "Error recording attachment metadata:",
              attachmentError
            );
            toast.error(
              `Failed to record metadata for ${file.name}: ${attachmentError.message}`
            );
            uploadErrors++;
          } else {
            console.log(
              `Successfully recorded metadata for ${file.name}:`,
              attachmentData
            );
            successfulUploads++;
          }
        }

        if (uploadErrors === files.length) {
          throw new Error("Failed to upload any files. Please try again.");
        }

        return "Purchase order submitted successfully";
      } catch (error: any) {
        console.error("Error in purchase order submission:", error);
        throw error;
      }
    },
    onSuccess: (result) => {
      console.log("Purchase order submission successful, resetting form...");

      // Reset all form values
      setFiles([]);
      setOtherFiles([]);
      setPoNumber("");
      setNotes("");

      // Reset selection states with debug logging
      console.log("Resetting customer selection...", setSelectedCustomer);
      if (setSelectedCustomer) {
        setSelectedCustomer("");
        console.log("Customer selection reset");
      }

      console.log("Resetting enquiry selection...", setSelectedEnquiry);
      if (setSelectedEnquiry) {
        setSelectedEnquiry("");
        console.log("Enquiry selection reset");
      }

      // Show success message
      toast.success("Purchase order submitted successfully");

      if (onClose) {
        console.log("Calling onClose callback");
        onClose();
      }

      if (onSuccess) {
        console.log("Calling onSuccess callback");
        onSuccess();
      }
    },
    onError: (error: any) => {
      let errorMessage = "Failed to submit purchase order. ";

      if (error.message) {
        errorMessage += error.message;
      }

      toast.error(errorMessage);
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    submitPurchaseOrderMutation.mutate();
  };

  return {
    notes,
    setNotes,
    files,
    setFiles,
    otherfiles,
    setOtherFiles,
    ponumber,
    setPoNumber,
    isSubmitting: submitPurchaseOrderMutation.isPending,
    handleSubmit,
  };
};
