import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export const useEnquirySampleStatus = (enquiryId: string | undefined) => {
  const { data, isLoading } = useQuery({
    queryKey: ["enquiry-sample-status", enquiryId],
    enabled: !!enquiryId,
    queryFn: async () => {
      if (!enquiryId) return null;
      
      // First, find the sample_requested status history item
      const { data: statusHistory, error: historyError } = await supabase
        .from("enquiry_status_history")
        .select("id")
        .eq("enquiry_id", enquiryId)
        .eq("status", "sample_requested")
        .order("created_at", { ascending: false })
        .limit(1)
        .single();
      
      if (historyError && historyError.code !== "PGRST116") {
        console.error("Error fetching sample request status history:", historyError);
        return null;
      }
      
      if (!statusHistory) return null;
      
      // Then, fetch the sample request data
      const { data: sampleRequest, error: sampleError } = await supabase
        .from("sample_requests")
        .select("*")
        .eq("status_history_id", statusHistory.id)
        .order("created_at", { ascending: false })
        .limit(1)
        .single();
      
      if (sampleError && sampleError.code !== "PGRST116") {
        console.error("Error fetching sample request:", sampleError);
        return null;
      }
      
      // Get the current status of the sample
      const { data: enquiryData, error: enquiryError } = await supabase
        .from("enquiries")
        .select("current_status")
        .eq("id", enquiryId)
        .single();
      
      if (enquiryError) {
        console.error("Error fetching enquiry status:", enquiryError);
        return { sampleRequestData: sampleRequest, sampleStatus: null };
      }
      
      // Determine the sample status based on the enquiry status
      let sampleStatus = "Requested";
      
      if (enquiryData.current_status === "sample_available") {
        sampleStatus = "Available";
      } else if (enquiryData.current_status === "sample_in_transit") {
        sampleStatus = "In Transit";
      } else if (enquiryData.current_status === "sample_delivered" || 
                 enquiryData.current_status === "sample_accepted" || 
                 enquiryData.current_status === "sample_rejected" || 
                 enquiryData.current_status === "sample_redo") {
        sampleStatus = "Delivered";
      }
      
      return { 
        sampleRequestData: sampleRequest, 
        sampleStatus,
        statusHistoryId: statusHistory.id
      };
    }
  });
  
  return {
    sampleRequestData: data?.sampleRequestData || null,
    sampleStatus: data?.sampleStatus || null,
    statusHistoryId: data?.statusHistoryId || null,
    isLoading
  };
};
