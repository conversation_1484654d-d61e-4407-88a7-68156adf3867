import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";

type EnquiryLifecycleStatus = Database["public"]["Enums"]["enquiry_lifecycle_status"];

// Define a type for the feedback data
export type FeedbackData = {
  id: string;
  type: string;
  response: string;
  reason: string;
  created_at: string;
};

export const useFeedbackData = (enquiryId?: string) => {
  const { data, isLoading: isLoadingFeedback } = useQuery({
    queryKey: ["feedback-data", enquiryId],
    enabled: !!enquiryId,
    queryFn: async () => {
      if (!enquiryId) return { sampleFeedback: null, quotationFeedback: null };

      console.log("Fetching feedback data for enquiry:", enquiryId);

      // Fetch Sample Feedback
      const { data: sampleData, error: sampleError } = await supabase
        .from("sample_feedback")
        .select("*")
        .eq("enquiry_id", enquiryId)
        .order("created_at", { ascending: false })
        .limit(1)
        .single();

      if (sampleError && sampleError.code !== "PGRST116") {
        console.error("Error fetching sample feedback:", sampleError);
      }

      // Fetch Quotation Feedback
      const { data: quotationData, error: quotationError } = await supabase
        .from("quotation_feedback")
        .select("*")
        .eq("enquiry_id", enquiryId)
        .order("created_at", { ascending: false })
        .limit(1)
        .single();

      console.log("hereeee",quotationData)

      if (quotationError && quotationError.code !== "PGRST116") {
        console.error("Error fetching quotation feedback:", quotationError);
      }

      // Process both feedbacks
      const sampleFeedback: FeedbackData | null = sampleData
        ? {
            id: sampleData.id,
            type: sampleData.type || "sample",
            response: sampleData.response,
            reason: sampleData.reason,
            created_at: sampleData.created_at,
          }
        : null;

      const quotationFeedback: FeedbackData | null = quotationData
        ? {
            id: quotationData.id,
            type: "quotation",
            response: quotationData.response,
            reason: quotationData.reason,
            created_at: quotationData.created_at,
          }
        : null;

      console.log("Fetched Sample Feedback:", sampleFeedback);
      console.log("Fetched Quotation Feedback:", quotationFeedback);

      return {
        sampleFeedback,
        quotationFeedback,
      };
    },
  });

  return {
    isLoadingFeedback,
    sampleFeedback: data?.sampleFeedback,
    quotationFeedback: data?.quotationFeedback,
    hasSampleFeedback: !!data?.sampleFeedback,
    hasQuotationFeedback: !!data?.quotationFeedback,
  };
};
