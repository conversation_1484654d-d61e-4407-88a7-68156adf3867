
import { supabase, STORAGE_BUCKETS } from "@/integrations/supabase/client";
import { toast } from "sonner";

export const uploadFeedbackAttachments = async (
  feedbackId: string, 
  files: File[],
  type: string
): Promise<{ success: boolean, error?: string }> => {
  try {
    console.log("Starting attachment upload for feedback ID:", feedbackId);
    console.log("Number of files to upload:", files.length);
    console.log("Feedback type:", type);
    
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      console.error("No authenticated session available for file uploads");
      return { success: false, error: "Authentication required for file uploads" };
    }

    console.log("Authenticated user:", session.user.id);
    
    // Ensure we're using the correct bucket based on feedback type
    let bucketName = '';
    if (type === 'quotation') {
      bucketName = STORAGE_BUCKETS.QUOTATION_DOCUMENTS;
    } else if (type === 'sample') {
      bucketName = STORAGE_BUCKETS.SAMPLE_DOCUMENTS;
    } else if (type === 'enquiry') {
      bucketName = STORAGE_BUCKETS.ENQUIRY_DOCUMENTS;
    }

    for (const file of files) {
      console.log(`Processing file: ${file.name}`);
      
      const fileExt = file.name.split('.').pop();
      const fileName = `${crypto.randomUUID()}.${fileExt}`;
      // Create a proper path structure for organization
      const filePath = `${type}_feedback/${feedbackId}/${fileName}`;

      console.log(`Generated file path: ${filePath}`);

      // Upload file to storage
      const { error: uploadError, data: uploadData } = await supabase.storage
        .from(bucketName)
        .upload(filePath, file);

      if (uploadError) {
        console.error('File upload error:', uploadError);
        toast.error(`Failed to upload ${file.name}: ${uploadError.message}`);
        continue;
      }
      
      console.log(`File uploaded successfully: ${filePath}`, uploadData);
    
      // Determine which table to use based on feedback type
      let attachmentTable;

      if (type === "quotation") {
        attachmentTable = "quotation_feedback_attachments";
      } else if (type === "sample") {
        attachmentTable = "sample_feedback_attachments";
      } else if (type === "enquiry") {
        attachmentTable = "enquiry_documents";
        continue;
      }

      console.log(`Saving metadata to ${attachmentTable} table`);

      // Save attachment metadata
      const { error: metadataError, data: metadataData } = await supabase
        .from(attachmentTable)
        .insert({
          feedback_id: feedbackId,
          file_name: file.name,
          file_path: filePath,
          content_type: file.type,
          size: file.size
        })
        .select();

      if (metadataError) {
        console.error('Metadata save error:', metadataError);
        toast.error(`Failed to save file metadata: ${metadataError.message}`);
        continue;
      }
      
      console.log(`Metadata saved for file: ${file.name}`, metadataData);
    }
    
    console.log("All file uploads completed");
    return { success: true };
  } catch (error) {
    console.error("Unexpected error in uploadFeedbackAttachments:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error during file upload" 
    };
  }
};

// Add a function to get file URLs for viewing/downloading
export const getFeedbackAttachmentUrl = async (filePath: string, type: string): Promise<string | null> => {
  try {
    // Determine which bucket to use based on the type
    let bucketToUse;
    if (type === 'quotation') {
      bucketToUse = STORAGE_BUCKETS.QUOTATION_DOCUMENTS;
    } else if (type === 'sample') {
      // Use the dedicated sample documents bucket
      bucketToUse = STORAGE_BUCKETS.SAMPLE_DOCUMENTS;
    } else {
      bucketToUse = STORAGE_BUCKETS.ENQUIRY_DOCUMENTS;
    }
    
    const { data } = await supabase.storage
      .from(bucketToUse)
      .getPublicUrl(filePath);
    
    return data?.publicUrl || null;
  } catch (error) {
    console.error("Error getting file URL:", error);
    return null;
  }
};