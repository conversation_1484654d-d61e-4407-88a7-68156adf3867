import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export const useSampleRequestStatus = (enquiryId: string | undefined) => {
  return useQuery({
    queryKey: ["sample-request-status", enquiryId],
    enabled: !!enquiryId,
    queryFn: async () => {
      if (!enquiryId) return null;

      console.log("helloabove!!!",enquiryId)
      
      const { data, error } = await supabase
        .from("sample_requests")
        .select("*")  // Select all fields instead of just status and id
        .eq("enquiry_id", enquiryId) // Fetch based on status_history_id
        .order("created_at", { ascending: false })
        .limit(1)
        .single();

        console.log("hellohere!!!",data)
      
      if (error) {
        console.error("Error fetching sample request status:", error);
        return null;
      }
      
      return data as any;
    },
  });
};
