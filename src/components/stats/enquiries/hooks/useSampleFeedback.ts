import { useEffect, useState, useMemo } from "react";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { uploadFeedbackAttachments } from "./useFeedbackAttachments";
import { formatDate, getDaysDifference } from "@/lib/utils";

type SampleResponse = "sample_accepted" | "sample_rejected" | "sample_redo";

// Add rejection reasons constant
const SAMPLE_REJECTION_REASONS = [
  "Quality not matching",
  "Specification mismatch",
  "Testing failed",
  "Customer on hold",
  "Others",
] as const;

const responseMapping = {
  "Accept Sample": "sample_accepted",
  "Reject Sample": "sample_rejected",
  "Request New Sample": "sample_redo",
};

export const useSampleFeedback = (
  enquiryId?: string,
  onClose?: () => void,
  onSuccess?: () => void,
  enquiryStatusId?: string,
  sampleDeliveredDate?: string
) => {
  const [selectedResponse, setSelectedResponse] = useState<"" | SampleResponse>(
    ""
  );
  const [reason, setReason] = useState<string>("");
  const [rejectionReason, setRejectionReason] = useState<string>("");
  const [files, setFiles] = useState<File[]>([]);
  const [snoozeEntries, setSnoozeEntries] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sampleFeedback, setSampleFeedback] = useState<any[]>();
  const [sampleFollowUp, setSampleFollowUp] = useState<any[]>();
  const [formEnabledFlags, setFormEnabledFlags] = useState({
    delivery_confirmation: false,
    testing_start: false,
    feedback: false,
    follow_up: false
  });
  const [formStates, setFormStates] = useState({
    delivery_confirmation: { deliveryDate: "", targetQualification: "" },
    testing_start: { remarks: "", testingStarted: "" },
    feedback: { responseType: "", reason: "", remarks: "", attachment: null },
    follow_up: { remarks: "", feedbackDate: "" }
  });
  const [stageStatuses, setStageStatuses] = useState({
    delivery_confirmation: "pending",
    testing_start: "pending",
    feedback: "pending",
  });

  const actionHandlers = {
    handleSubmit: (stageId, formData, meta) => {
      console.log(`Requesting info for stage ${stageId} with data:`, formData);

      setSelectedResponse(responseMapping[formData.responseType]);
      setReason(formData.remarks);
      setRejectionReason(formData.reason);
      setFiles([formData.attachment]);
      handleSubmit(formData);
    },

    delivery_confirmation: (stageId, formData, meta) => {
      console.log("this is delivery confirmation", formData);
    },
    submit_handler: (stageId, formData, meta) => {
      console.log("this is testing start", formData);
      submitHandler(stageId, formData);
    },
    snooze: (stageId, formData, meta) => {
      console.log(
        "this is delivery snooze",
        formData,
        "delivery snooze",
        stageId
      );
      handleDeliverySnooze(stageId);
    },
  };

  useEffect(() => {
    // Skip if enquiryStatusId is not available
    if (!enquiryStatusId) return;

    fetchSampleFeedback();
    fetchSnoozeEntries();
  }, [enquiryStatusId]);

  const fetchSnoozeEntries = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Query the sample_feedback_snooze table for entries matching the enquiryStatusId
      const { data, error } = await supabase
        .from("sample_feedback_snooze")
        .select("*")
        .eq("sample_request_id", enquiryStatusId)
        .order("snooze_started_at", { ascending: false });

      if (error) {
        throw error;
      }

      setSnoozeEntries(data || []);
    } catch (err) {
      console.error("Error fetching snooze entries:", err);
      setError("Failed to load snooze history");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSmpleFollowUp = async (id) => {
    setIsLoading(true);
    setError(null);

    try {
      // Query the sample_feedback_snooze table for entries matching the enquiryStatusId
      const { data: followupData, error: followupError } = await supabase
        .from("sample_feedback_followup")
        .select("*")
        .eq("sample_feedback_id", id)
        .order("created_at", { ascending: false })

      if (error) {
        throw error;
      }

      setSampleFollowUp(followupData || []);
    } catch (err) {
      console.error("Error fetching sample followup:", err);
      setError("Failed to load Sample Followup");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSampleFeedback = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Query the sample_feedback table for entries matching the enquiryStatusId
      const { data, error } = await supabase
        .from("sample_feedback")
        .select("*")
        .eq("sample_request_id", enquiryStatusId)
        .order("created_at", { ascending: false })
        .limit(1);

      if (error) {
        throw error;
      }
      // Store the feedback data in state
      if (!data || data.length === 0) {
        // If no data exists, only enable delivery confirmation
        setFormEnabledFlags({
          delivery_confirmation: true,
          testing_start: true,
          feedback: true,
          follow_up: false
        });
      } else {

        fetchSmpleFollowUp(data[0].id)

        const latestFeedback = data[0];

        // Check if delivery confirmation date exists
        const hasDeliveryDate = !!latestFeedback.delivery_confirmation_date;

        // Check if testing start date exists
        const hasTestingStartDate = !!latestFeedback.testing_confirmation_date;

        setStageStatuses({
          delivery_confirmation: hasDeliveryDate ? "completed" : "pending",
          testing_start: hasTestingStartDate ? "completed" : "pending",
          feedback: "pending", // Feedback is always pending until submitted
        });
        // Set flags based on the conditions
        setFormEnabledFlags({
          // Delivery confirmation is enabled if no data exists (handled above)
          delivery_confirmation: !hasDeliveryDate,

          // Testing start is enabled if delivery date exists but testing date doesn't
          testing_start: !hasTestingStartDate,

          // Feedback is enabled if both delivery and testing dates exist
          feedback: true,

          follow_up: hasDeliveryDate || hasTestingStartDate
        });
        if (hasDeliveryDate) {
          setFormStates((prev) => ({
            ...prev,
            delivery_confirmation: {
              ...prev.delivery_confirmation,
              deliveryDate:
                formatDateForInput(latestFeedback.delivery_confirmation_date) ||
                "",
              targetQualification:
                formatDateForInput(latestFeedback.tentative_feedback_date) || "",
            },
          }));
        }

        if (hasTestingStartDate) {
          setFormStates((prev) => ({
            ...prev,
            testing_start: {
              ...prev.testing_start,
              remarks: latestFeedback.testing_remarks || "",
              testingStarted:
                formatDateForInput(latestFeedback.testing_confirmation_date) ||
                "",
            },
          }));
        }

        // If feedback exists, populate those fields too
        if (latestFeedback.response) {
          setFormStates((prev) => ({
            ...prev,
            feedback: {
              ...prev.feedback,
              responseType: latestFeedback.response || "",
              reason: latestFeedback.reason || "",
              remarks: latestFeedback.remarks || "",
            },
          }));
        }
      }
      setSampleFeedback(data);
    } catch (err) {
      console.error("Error fetching sample feedback:", err);
      setError("Failed to load sample feedback history");
    } finally {
      setIsLoading(false);
    }
  };

  const formatDateForInput = (dateString) => {
    if (!dateString) return "";
    // Extract just the YYYY-MM-DD part from the ISO string
    return dateString.split("T")[0];
  };

  const submitFeedbackMutation = useMutation({
    mutationFn: async () => {
      console.log("Starting sample feedback submission process");
      console.log("Enquiry ID:", enquiryId);
      console.log("Selected response:", selectedResponse);
      console.log("Reason:", reason);
      console.log("Files count:", files.length);
      console.log("Files", files);

      if (!enquiryId || !selectedResponse) {
        const missingFields = [];
        if (!enquiryId) missingFields.push("enquiryId");
        if (!selectedResponse) missingFields.push("selectedResponse");

        console.error(
          "Missing required fields for sample feedback:",
          missingFields.join(", ")
        );
        throw new Error(`Missing required fields: ${missingFields.join(", ")}`);
      }

      try {
        // Check authentication status
        const { data: authData } = await supabase.auth.getSession();
        console.log(
          "Authentication status:",
          authData.session ? "Authenticated" : "Not authenticated"
        );

        if (!authData.session) {
          console.error("User is not authenticated");
          throw new Error(
            "Authentication required. Please log in to submit feedback."
          );
        }

        // Submit feedback data to Supabase
        console.log(
          "Inserting sample feedback record into Supabase ###",
          enquiryStatusId
        );

        // 1. Check if entry exists
        const { data: existing, error: fetchErrorSample } = await supabase
          .from("sample_feedback")
          .select("id")
          .eq("enquiry_id", enquiryId)
          .eq("sample_request_id", enquiryStatusId)
          .single();

        if (fetchErrorSample && fetchErrorSample.code !== "PGRST116") {
          // Only throw if it's not a "no rows found" error
          throw fetchErrorSample;
        }

        let data, error;
        if (existing) {
          // 2a. Update if exists
          ({ data, error } = await supabase
            .from("sample_feedback")
            .update({
              response: selectedResponse,
              remarks: reason,
              reason: rejectionReason,
              submitted_by: authData.session?.user?.email || null,
              type: "sample", // Use the new type column
            })
            .eq("enquiry_id", enquiryId)
            .eq("sample_request_id", enquiryStatusId)
            .select()
            .single());
        } else {
          // 2b. Insert if not exists
          const formatted = new Date().toISOString().split('T')[0] + ' 00:00:00+00';

          ({ data, error } = await supabase
            .from("sample_feedback")
            .insert({
              enquiry_id: enquiryId,
              sample_request_id: enquiryStatusId,
              response: selectedResponse,
              reason: rejectionReason,
              submitted_by: authData.session?.user?.email || null,
              type: "sample",
              remarks: reason,
            })
            .select()
            .single());
        }

        if (error) {
          // handle error
          throw error;
        }
        // const { data, error } = await supabase
        //   .from("sample_feedback")
        //   .update({
        //     response: selectedResponse,
        //     remarks: reason,
        //     reason: rejectionReason,
        //     submitted_by: authData.session?.user?.email || null,
        //     type: "sample", // Use the new type column
        //   })
        //   .eq("enquiry_id", enquiryId)
        //   .eq("sample_request_id", enquiryStatusId)
        //   .select()
        //   .single();

        if (error) {
          console.error("Error submitting sample feedback to Supabase:", error);
          console.error("Error details:", JSON.stringify(error, null, 2));
          throw new Error(
            `Database error: ${
              error.message || error.details || "Unknown error"
            }`
          );
        }

        console.log("Sample feedback submitted successfully:", data);

        // 1️⃣ Fetch the latest sample request (if any) based on enquiry_id
        const { data: existingRequest, error: fetchError } = await supabase
          .from("sample_requests")
          .select("*")
          .eq("enquiry_id", enquiryId)
          .eq("id", enquiryStatusId)
          .single();

        const { error: updateError } = await supabase
          .from("sample_requests")
          .update({ status: selectedResponse,last_status_change: new Date().toISOString() })
          .eq("id", existingRequest.id);

        if (fetchError) {
          console.error(
            "Error fetching existing sample request:",
            fetchError.message
          );
        }

        if (selectedResponse === "sample_redo") {

          const {
            data: { session },
          } = await supabase.auth.getSession();

          const { data: sampleRequestNumber, error: sampleRequestNumberError } =
            await supabase
              .rpc("generate_sample_request_number", {
                p_enquiry_id: enquiryId,
              })
              .single();

          if (sampleRequestNumberError) {
            throw new Error(
              `Failed to generate sample request number: ${sampleRequestNumberError.message}`
            );
          }

          // 2️⃣ Manually construct the new sample request with existing values
          const newSampleRequest = {
            id: sampleRequestNumber,
            enquiry_id: enquiryId,
            status_history_id: undefined,
            quantity: existingRequest?.quantity,
            sample_poc: existingRequest?.sample_poc,
            contact_email: existingRequest?.contact_email,
            contact_phone: existingRequest?.contact_phone,
            quantity_unit: existingRequest?.quantity_unit,
            delivery_address: existingRequest?.delivery_address,
            delivery_city: existingRequest?.delivery_city,
            delivery_country: existingRequest?.delivery_country,
            delivery_postal_code: existingRequest?.delivery_postal_code,
            remarks: existingRequest?.remarks,
            created_by: existingRequest?.created_by,
            sales_team_member: existingRequest?.sales_team_member,
            status: "sample_requested",
            last_status_change: new Date().toISOString()
          };

          // 3️⃣ Insert the new sample request
          const { data: sampleRequestData, error: insertError } = await supabase
            .from("sample_requests")
            .insert([newSampleRequest])
            .select()
            .single();

          // Then create the initial status history entry
          const { error: statusHistoryError } = await supabase
            .from("sample_status_history")
            .insert({
              sample_request_id: sampleRequestData?.id,
              enquiry_id: enquiryId,
              sample_status: "sample_requested",
              changed_at: new Date().toISOString(),
            });

          if (insertError) {
            throw new Error(
              `Failed to create sample request: ${insertError.message}`
            );
          }
        } else {
          // const { error: enquiryError } = await supabase
          //   .from("enquiries")
          //   .update({
          //     current_status: selectedResponse,
          //     last_status_change: new Date().toISOString(),
          //   })
          //   .eq("id", enquiryId);
        }

        // Handle file uploads
        if (files.length > 0 && files[0] != null) {
          console.log("Uploading sample feedback attachments");
          // FIX: Remove the third argument (data.id) since uploadFeedbackAttachments only accepts two parameters
          const uploadResult = await uploadFeedbackAttachments(
            data.id,
            files,
            "sample"
          );
          console.log("Attachment upload result:", uploadResult);

          if (!uploadResult.success) {
            console.warn("File upload issues:", uploadResult.error);
            // Continue execution but warn about attachment issues
            return (
              "Sample feedback submitted but there were issues with file uploads: " +
              uploadResult.error
            );
          }
        }


        if(selectedResponse){
          await supabase
          .from("sample_status_history")
          .insert({
            sample_request_id: existingRequest.id,
            sample_status: selectedResponse,
            changed_at: new Date().toISOString(),
            enquiry_id: enquiryId, // Make sure enquiryId is a UUID string
          });
        }

        return "Sample feedback submitted successfully";
      } catch (error: any) {
        console.error("Exception in sample feedback submission:", error);
        if (error.message) console.error("Error message:", error.message);
        if (error.details) console.error("Error details:", error.details);
        if (error.hint) console.error("Error hint:", error.hint);
        if (error.code) console.error("Error code:", error.code);
        throw error;
      }
    },
    onSuccess: (result) => {
      console.log("Sample feedback mutation succeeded:", result);
      toast.success("Sample feedback submitted successfully");

      if (onClose) {
        console.log("Calling onClose callback");
        onClose();
      }

      // Call the onSuccess callback if provided
      if (onSuccess) {
        console.log("Calling onSuccess callback");
        onSuccess();
      }
    },
    onError: (error: any) => {
      console.error("Error in sample feedback mutation:", error);
      let errorMessage = "Failed to submit feedback. ";

      if (error.message) {
        console.error("Error message:", error.message);
        errorMessage += error.message;
      }

      if (error.details) {
        console.error("Error details:", error.details);
      }

      if (error.code) {
        console.error("Error code:", error.code);
        // Handle specific error codes
        if (error.code === "PGRST301") {
          errorMessage += " Authentication required. Please log in again.";
        } else if (error.code === "23505") {
          errorMessage += " A duplicate record exists.";
        } else if (error.code === "42501") {
          errorMessage +=
            " Permission denied. You don't have access to this action.";
        }
      }

      toast.error(errorMessage);
    },
  });

  const handleSubmit = async (e) => {
    console.log("Sample feedback form submitted", e);
    submitFeedbackMutation.mutate();
  };

  const handleDeliverySnooze = async (stageId) => {
    const today = new Date().toISOString();


    try {

      const daysFromSnooze = stageId === "feedback" ? 10 : 5;

      const newEndDate = new Date(today);
        newEndDate.setDate(newEndDate.getDate()+daysFromSnooze)

      // Create a new snooze entry
      const { data, error } = await supabase
        .from("sample_feedback_snooze")
        .insert({
          process_stage: stageId,
          snooze_started_at: today,
          sample_request_id: enquiryStatusId,
          snooze_ended_at: newEndDate.toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error("Error creating snooze entry:", error);
        toast.error("Failed to snooze delivery");
        return;
      }

      // Update the local state to include the new entry
      // setSnoozeEntries((prev) => [data, ...prev]);
      fetchSnoozeEntries();

      toast.success("Delivery snoozed successfully");
    } catch (error) {
      console.error("Error snoozing delivery:", error);
    }
  };

  const submitHandler = async (stageId, formData) => {
    try {
      const { data: authData } = await supabase.auth.getSession();
      console.log(
        "Authentication status:",
        authData.session ? "Authenticated" : "Not authenticated"
      );

      if (!authData.session) {
        console.error("User is not authenticated");
        throw new Error(
          "Authentication required. Please log in to submit feedback."
        );
      }

      // Check if entry exists
      const { data: existing, error: fetchErrorSample } = await supabase
        .from("sample_feedback")
        .select("id")
        .eq("enquiry_id", enquiryId)
        .eq("sample_request_id", enquiryStatusId)
        .single();

      if (fetchErrorSample && fetchErrorSample.code !== "PGRST116") {
        // Only throw if it's not a "no rows found" error
        throw fetchErrorSample;
      }

      if (stageId === "follow_up") {
        await addSampleFeedbackFollowUp(authData?.session?.user?.email, existing?.id, formData.remarks, formData.feedbackDate)
        fetchSmpleFollowUp(existing?.id)
        return;
      }

      let data, error;
      if (stageId === "delivery_confirmation") {
        if (existing) {
          // Update
          ({ data, error } = await supabase
            .from("sample_feedback")
            .update({
              delivery_confirmation_date: formData.deliveryDate,
              tentative_feedback_date: formData.targetQualification,
              submitted_by: authData.session?.user?.email || null,
              response: "sample_delivered",
            })
            .eq("enquiry_id", enquiryId)
            .eq("sample_request_id", enquiryStatusId)
            .select()
            .single());
        } else {
          // Insert
          ({ data, error } = await supabase
            .from("sample_feedback")
            .insert({
              enquiry_id: enquiryId,
              sample_request_id: enquiryStatusId,
              delivery_confirmation_date: formData.deliveryDate,
              tentative_feedback_date: formData.targetQualification,
              submitted_by: authData.session?.user?.email || null,
              response: "sample_delivered",
            })
            .select()
            .single());
        }
      } else {
        if (existing) {
          // Update
          ({ data, error } = await supabase
            .from("sample_feedback")
            .update({
              testing_confirmation_date: formData.testingStarted,
              testing_remarks: formData.remarks,
              submitted_by: authData.session?.user?.email || null,
            })
            .eq("enquiry_id", enquiryId)
            .eq("sample_request_id", enquiryStatusId)
            .select()
            .single());
        } else {
          // Insert
          ({ data, error } = await supabase
            .from("sample_feedback")
            .insert({
              enquiry_id: enquiryId,
              sample_request_id: enquiryStatusId,
              testing_confirmation_date: formData.testingStarted,
              testing_remarks: formData.remarks,
              submitted_by: authData.session?.user?.email || null,
              response: "sample_delivered",
            })
            .select()
            .single());
        }
      }
      if (error) {
        throw error;
      }
      fetchSampleFeedback();
    } catch (error) {
      console.log(error);
      throw new Error("Failed to submit feedback");
    }
  };

  const calculateStageDates = (
    sampleDeliveredDate: string,
    targetQualificationDays: string
  ) => {
    // For delivery confirmation, use sampleDeliveredDate
    const confirmedDeliveryDate = sampleFeedback && sampleFeedback.length !==0 ? sampleFeedback[0]?.delivery_confirmation_date : formStates.delivery_confirmation.deliveryDate;

    if (!sampleDeliveredDate && !confirmedDeliveryDate)
      return { delivery: "", testing: "", feedback: "" };

    // For delivery confirmation stage
    const deliveryDate = new Date(sampleDeliveredDate);

    // For testing and feedback stages, use confirmedDeliveryDate if available
    const actualDeliveryDate = confirmedDeliveryDate
      ? new Date(confirmedDeliveryDate)
      : deliveryDate;

    // 1. Delivery confirmation: sampleDeliveredDate + 48 hours
    const deliveryDueDate = new Date(deliveryDate);
    deliveryDueDate.setHours(deliveryDueDate.getHours() + 48);
    let daysDiff = 30;
    if (formStates.delivery_confirmation.targetQualification && formStates.delivery_confirmation.deliveryDate ) {
      daysDiff = getDaysDifference(
        formStates.delivery_confirmation.deliveryDate,
        formStates.delivery_confirmation.targetQualification
      );
    }
    // daysDiff will be positive if targetQualification is after delivery_confirmation_date

    // 2. Testing start: Min of (Target Qualification Time / 2 OR 15 days) after actual delivery
    const halfQualificationDays = Math.floor(daysDiff / 2);
    console.log(daysDiff,"daysDiff");
    const testingStartDays = Math.min(halfQualificationDays, 15);
    let testingStartDate = new Date(actualDeliveryDate);
    testingStartDate.setDate(testingStartDate.getDate() + testingStartDays);

    // // 3. Feedback: Earlier of (End of Target Qualification Time OR 30 days post-delivery)
    // const qualificationEndDate = new Date(actualDeliveryDate);
    // qualificationEndDate.setDate(qualificationEndDate.getDate() + targetQualificationDays);

    // const thirtyDaysDate = new Date(actualDeliveryDate);
    // thirtyDaysDate.setDate(thirtyDaysDate.getDate() + 30);

    let greaterDate = targetQualificationDays
      ? new Date(targetQualificationDays)
      : (() => {
        const d = new Date(deliveryDate);
        d.setDate(d.getDate() + 30);
        return d;
      })();

    if (sampleFollowUp && sampleFollowUp.length > 0) {
      const lastFollowUpDate = sampleFollowUp[0]?.feedback_date
        ? new Date(sampleFollowUp[0].feedback_date)
        : null;

      // const targetQualDate = formStates.delivery_confirmation.targetQualification
      //   ? new Date(formStates.delivery_confirmation.targetQualification)
      //   : null;
      greaterDate = lastFollowUpDate
      // > targetQualDate ? lastFollowUpDate : targetQualDate
    }

    testingStartDate =testingStartDays === 0 ? greaterDate : testingStartDate

    return {
      delivery: formatDate(deliveryDueDate, "pending"),
      testing: formatDate(testingStartDate, "pending"),
      feedback: formatDate(greaterDate, "pending")
    };
  };

  const targetQualificationDays = formStates.delivery_confirmation.targetQualification
  // ? parseInt(formStates.delivery_confirmation.targetQualification) 
  // : 30;
  const stageDates = useMemo(() => {
    return calculateStageDates(sampleDeliveredDate, targetQualificationDays);
  }, [sampleDeliveredDate, targetQualificationDays, sampleFollowUp,formStates, formStates.testing_start.testingStarted // <-- add this
  ]);

  const handleFieldChange = (stageId: string, field: string, value: any) => {
    setFormStates((prev) => {
      // Create the updated state
      const updatedState = {
        ...prev,
        [stageId]: {
          ...prev[stageId],
          [field]: value,
        },
      };

      // If we're changing the responseType in the feedback stage
      if (stageId === "feedback" && field === "responseType") {
        // Reset the reason field to empty (which will show the placeholder "Select a reason")
        updatedState[stageId]["reason"] = "";
      }

      return updatedState;
    });
  };

  const handleActionClick = (stageId: string, action: any) => {
    const stageFormData = formStates[stageId];
    const actionType =
      action.type || action.label.toLowerCase().replace(/\s+/g, "_");
    const handler = actionHandlers[actionType];

    if (handler) {
      handler(stageId, stageFormData, action.meta);
    } else {
      console.warn(`No handler defined for action type: ${actionType}`);
    }
  };

  const generateAlerts = () => {
    if (!snoozeEntries || snoozeEntries.length === 0) return {};

    const alerts = {};

    snoozeEntries.forEach((entry) => {
      // if (!entry.snooze_ended_at) {
      const stage = entry.process_stage;
      const startDate = new Date(entry.snooze_started_at);

      const snoozeCount = snoozeEntries.filter(
        e => e.process_stage === stage
      ).length;

      let baseDate;
      if (stage === "delivery_confirmation") {
        baseDate = stageDates?.delivery;
      } else if (stage === "testing_start") {
        baseDate = stageDates?.testing;
      } else if (stage === "feedback") {
        baseDate = stageDates?.feedback;
      }

      // Calculate the adjusted date
      const daysPerSnooze = stage === "feedback" ? 10 : 5;
      const adjustedDate = adjustDateForSnoozes(baseDate, snoozeCount, daysPerSnooze);

      // Format the start date for display
      const formattedStartDate = startDate.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      });

      // Create alert message
      alerts[stage] = `${
        stage.replace("_", " ").charAt(0).toUpperCase() +
        stage.replace("_", " ").slice(1)
        } awaited on ${adjustedDate} (Snoozed on: ${formattedStartDate})`;
      // }
    });

    return alerts;
  };

  const adjustDateForSnoozes = (dateString, snoozeCount, daysPerSnooze) => {
    if (!dateString || snoozeCount <= 0) return dateString;

    // Parse the date (assuming format like "15 May 2025")
    const dateParts = dateString.split(' ');
    const day = parseInt(dateParts[0]);
    const monthStr = dateParts[1];
    const year = parseInt(dateParts[2]);

    // Convert month name to month index
    const months = {
      'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
      'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
    };

    // Create date object and add days
    const dateObj = new Date(year, months[monthStr], day);
    dateObj.setDate(dateObj.getDate() + (snoozeCount * daysPerSnooze));

    // Format the new date
    return dateObj.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric"
    });
  };


  async function addSampleFeedbackFollowUp(createdBy, sampleFeedbackId, remarks, feedbackDate) {
    const { data, error } = await (supabase as any)
      .from("sample_feedback_followup")
      .insert({
        created_by: createdBy,
        sample_feedback_id: sampleFeedbackId,
        remarks,
        feedback_date: feedbackDate,
      })
      .select()
      .single();

    setFormStates(prev => ({
      ...prev,
      "follow_up": {
        remarks: "",
        feedbackDate: ""
      }
    }));
    return { data, error };
  }

  return {
    selectedResponse,
    setSelectedResponse,
    reason,
    setReason,
    rejectionReason,
    setRejectionReason,
    files,
    setFiles,
    isSubmitting: submitFeedbackMutation.isPending,
    handleSubmit,
    handleDeliverySnooze,
    submitHandler,
    setFormStates,
    formStates,
    formEnabledFlags,
    snoozeEntries,
    stageStatuses,
    calculateStageDates,
    handleFieldChange,
    handleActionClick,
    generateAlerts,
    sampleFeedback,
    adjustDateForSnoozes,
    stageDates,
    sampleFollowUp
  };
};
