import { supabase, STORAGE_BUCKETS } from "@/integrations/supabase/client";
import { toast } from "sonner";

// Utility function to upload a quotation document
export const uploadQuotationDocument = async (enquiryId: string, file: File): Promise<{success: boolean, error?: string, quotationId?: string}> => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return { success: false, error: "Authentication required for quotation uploads" };
    }
    
    // Generate a unique filename
    const fileExt = file.name.split('.').pop();
    const fileName = `${enquiryId}/${crypto.randomUUID()}.${fileExt}`;
    const filePath = fileName;

    // Upload the file to the quotation_documents bucket
    const { error: uploadError } = await supabase.storage
      .from(STORAGE_BUCKETS.QUOTATION_DOCUMENTS)
      .upload(filePath, file);

    if (uploadError) {
      console.error('Error uploading quotation document:', uploadError);
      return { success: false, error: uploadError.message };
    }
    
    console.log(`Successfully uploaded quotation document ${file.name} to ${filePath}`);
    
    // Create a record in the customer_quotations table
    const { data: quotationData, error: quotationError } = await supabase
      .from('customer_quotations')
      .insert({
        created_by: session.user.email || session.user.id,
        document_file_path: filePath,
        document_file_name: file.name
      })
      .select()
      .single();
    
    if (quotationError) {
      console.error('Error creating quotation record:', quotationError);
      return { success: false, error: quotationError.message };
    }
    
    if (!quotationData) {
      return { success: false, error: "Failed to create quotation record" };
    }
    
    // Link the quotation to the enquiry
    const { error: linkError } = await supabase
      .from('enquiry_quotations')
      .insert({
        enquiry_id: enquiryId,
        quotation_id: quotationData.id,
        is_current: true
      });
    
    if (linkError) {
      console.error('Error linking quotation to enquiry:', linkError);
      return { success: false, error: linkError.message };
    }
    
    return { 
      success: true, 
      quotationId: quotationData.id
    };
  } catch (error) {
    console.error("Error in uploadQuotationDocument:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : String(error) 
    };
  }
};

// Utility function to get the latest quotation for an enquiry
export const getLatestQuotation = async (enquiryId: string): Promise<{
  id: string;
  file_name: string;
  file_path: string;
  created_at: string;
  created_by: string;
} | null> => {
  try {
    const { data, error } = await supabase
      .from('enquiry_quotations')
      .select(`
        quotation_id,
        customer_quotations (
          id,
          document_file_name,
          document_file_path,
          created_at,
          created_by
        )
      `)
      .eq('enquiry_id', enquiryId)
      .eq('is_current', true)
      .single();
    
    if (error) {
      console.error('Error fetching quotation:', error);
      return null;
    }
    
    if (!data || !data.customer_quotations) {
      return null;
    }
    
    return {
      id: data.customer_quotations.id,
      file_name: data.customer_quotations.document_file_name,
      file_path: data.customer_quotations.document_file_path,
      created_at: data.customer_quotations.created_at,
      created_by: data.customer_quotations.created_by
    };
  } catch (error) {
    console.error("Error in getLatestQuotation:", error);
    return null;
  }
};

// Utility function to generate a download URL for a quotation
export const getQuotationDownloadUrl = async (filePath: string): Promise<string | null> => {
  try {
    // If the path is already a URL (like Google Drive), return it directly
    if (filePath.startsWith('http')) {
      return filePath;
    }
    
    // Otherwise generate a signed URL from storage
    const { data, error } = await supabase.storage
      .from(STORAGE_BUCKETS.QUOTATION_DOCUMENTS)
      .createSignedUrl(filePath, 60); // URL valid for 60 seconds
    
    if (error || !data) {
      console.error('Error generating download URL:', error);
      return null;
    }
    
    return data.signedUrl;
  } catch (error) {
    console.error("Error in getQuotationDownloadUrl:", error);
    return null;
  }
};
