import { useState } from "react";
import { toast } from "sonner";
import { getLatestQuotation, getQuotationDownloadUrl } from "./useQuotationManagement";

export const useQuotationViewer = () => {
  const [isLoadingQuotation, setIsLoadingQuotation] = useState(false);
  const [quotationUrl, setQuotationUrl] = useState<string | null>(null);
  const [showPdfViewer, setShowPdfViewer] = useState(false);
  const [quotationFileName, setQuotationFileName] = useState<string | null>(null);
  const [isExternalUrl, setIsExternalUrl] = useState(false);

  const handleViewQuotation = async (enquiryId?: string) => {
    if (!enquiryId) {
      toast.error("No enquiry ID available to fetch quotation");
      return;
    }

    try {
      setIsLoadingQuotation(true);
      toast.info("Retrieving quotation document...");
      
      // Get the latest quotation
      const quotation = await getLatestQuotation(enquiryId);
      
      if (!quotation) {
        toast.error("No quotation document found for this enquiry");
        return;
      }
      
      // Check if file_path is an external URL (like Google Drive)
      const isExternal = quotation.file_path.startsWith('http');
      setIsExternalUrl(isExternal);
      
      let downloadUrl;
      if (isExternal) {
        // If it's already a URL (like a Google Drive link), use it directly
        downloadUrl = quotation.file_path;
      } else {
        // Otherwise, generate a download URL from Supabase Storage
        downloadUrl = await getQuotationDownloadUrl(quotation.file_path);
      }
      
      if (!downloadUrl) {
        toast.error("Failed to generate download link for quotation");
        return;
      }
      
      // Store URL and filename for use in the viewer
      setQuotationUrl(downloadUrl);
      setQuotationFileName(quotation.file_name);
      
      // Open the PDF viewer for storage files, or open external URL in new tab
      if (isExternal) {
        window.open(downloadUrl, '_blank');
      } else {
        setShowPdfViewer(true);
      }
      
    } catch (error) {
      console.error("Error fetching quotation:", error);
      toast.error("Failed to retrieve quotation document");
    } finally {
      setIsLoadingQuotation(false);
    }
  };

  return {
    isLoadingQuotation,
    quotationUrl,
    showPdfViewer,
    setShowPdfViewer,
    quotationFileName,
    isExternalUrl,
    handleViewQuotation
  };
};
