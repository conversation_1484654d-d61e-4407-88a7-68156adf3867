
import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export type ClarificationQuery = {
  id: string;
  enquiry_id: string;
  query: string;
  response: string | null;
  created_at: string;
  resolved_at: string | null;
  created_by: string;
  status: 'pending' | 'resolved' | 'rejected';
};

export const useClarificationQueries = (enquiryId: string | undefined) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const queryClient = useQueryClient();

  const { data: clarificationQueries, isLoading, error } = useQuery({
    queryKey: ['clarification-queries', enquiryId],
    enabled: !!enquiryId,
    queryFn: async () => {
      if (!enquiryId) return [];
      
      const { data, error } = await supabase
        .from('enquiry_clarifications')
        .select('*')
        .eq('enquiry_id', enquiryId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as ClarificationQuery[];
    }
  });

  const updateClarificationMutation = useMutation({
    mutationFn: async ({ 
      id, 
      response, 
      status 
    }: { 
      id: string; 
      response?: string; 
      status?: 'pending' | 'resolved' | 'rejected';
    }) => {
      const updateData: any = {};
      if (response !== undefined) updateData.response = response;
      if (status !== undefined) {
        updateData.status = status;
        if (status === 'resolved') updateData.resolved_at = new Date().toISOString();
      }
      
      const { data, error } = await supabase
        .from('enquiry_clarifications')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast.success("Clarification updated successfully");
      queryClient.invalidateQueries({ queryKey: ['clarification-queries', enquiryId] });
    },
    onError: (error) => {
      console.error("Error updating clarification:", error);
      toast.error("Failed to update clarification");
    }
  });

  const addClarificationMutation = useMutation({
    mutationFn: async ({ query, createdBy }: { query: string; createdBy: string; }) => {
      setIsSubmitting(true);
      
      const { data, error } = await supabase
        .from('enquiry_clarifications')
        .insert({
          enquiry_id: enquiryId,
          query,
          created_by: createdBy,
          status: 'pending'
        })
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      setIsSubmitting(false);
      toast.success("Clarification query added successfully");
      queryClient.invalidateQueries({ queryKey: ['clarification-queries', enquiryId] });
    },
    onError: (error) => {
      setIsSubmitting(false);
      console.error("Error adding clarification query:", error);
      toast.error("Failed to add clarification query");
    }
  });

  return {
    clarificationQueries: clarificationQueries || [],
    isLoading,
    error,
    isSubmitting,
    addClarificationQuery: addClarificationMutation.mutate,
    updateClarificationQuery: updateClarificationMutation.mutate
  };
};
