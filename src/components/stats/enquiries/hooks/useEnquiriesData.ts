
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";

export type EnquiryType = Database['public']['Views']['get_enquiry_board']['Row'] & {
  enquiry_documents?: Database['public']['Tables']['enquiry_documents']['Row'][]; // Made optional since we're not fetching in useEnquiriesData
  status_history?: Database['public']['Tables']['enquiry_status_history']['Row'][];
  customer_poc?: string;
  customer_phone?: string;
  customer_email?: string;
  packaging_type?: string;
  qty_per_packaging?: number;
  qty_per_packaging_unit?: string;
};

export type SortDirection = 'asc' | 'desc';

export const useEnquiriesData = (type: "total" | "recent", sortDirection: SortDirection, role: string, category?: string[]) => {
  const queryResult = useQuery({
    queryKey: ["enquiries", type, sortDirection, role, category],
    retry: 3, // Retry failed requests up to 3 times
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff strategy
    queryFn: async () => {
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      // Get the current session and refresh if needed
      let { data: { session } } = await supabase.auth.getSession();

      // If session exists but might be close to expiring, refresh it
      if (session) {
        const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
        if (!refreshError && refreshData.session) {
          session = refreshData.session;
        } else if (refreshError) {
          console.warn("Failed to refresh session:", refreshError);
          // Continue with the existing session
        }
      }

      if (!session?.user) return null;


      // Step 1: Get total count of matching rows
      const limit = 1000;
      let from = 0;
      let allData = [];
      let totalCount = 0;

      let query = supabase
        .from("get_enquiry_board")
        .select("*")
        .eq("is_new", true); // Only fetch enquiries


      if (type === "recent") {
        query = query.gte('created_at', sevenDaysAgo.toISOString());
      }

      if (role === "sales") {
        query = query.eq("sales_team_member", session?.user?.email) // Filter by sales team member

        // Step 1: Get total count
        const { count } = await supabase
          .from("get_enquiry_board")
          .select("*", { count: "exact", head: true })
          .eq("sales_team_member", session?.user?.email)
          .eq("is_new", true)

        console.log("Enquiries data: *** 1", count);

        totalCount = count;
      } else if (role === "bu_head") {
        query = query.in("category", category) // Filter by bu_head

        const { count } = await supabase
          .from("get_enquiry_board")
          .select("*", { count: "exact", head: true })
          .in("category", category)
          .eq("is_new", true);

        console.log("Enquiries data: *** 2", count);
        totalCount = count;
      } else if (role === "admin") {
        query = query

        const { count } = await supabase
          .from("get_enquiry_board")
          .select("*", { count: "exact", head: true })
          .eq("is_new", true);

        console.log("Enquiries data: *** 3", count);
        totalCount = count;
      }


      while (from < totalCount) {
        const { data, error } = await query.range(from, from + limit - 1);

        if (error) {
          console.error("Error fetching batch:", error);
          break;
        }

        // No longer fetching documents here as requested
        // Documents will be fetched in EnquiryLifecycleDialog only

        // Add data to our collection without documents
        allData = allData.concat(data);
        from += limit;
      }


      // Custom sorting: prioritize clarification_needed and then sort by date
      const sortedData = [...(allData as EnquiryType[])];
      sortedData.sort((a, b) => {
        // First, prioritize clarification_needed status
        if (a.current_status === 'clarification_needed' && b.current_status !== 'clarification_needed') {
          return -1; // a comes first
        }
        if (a.current_status !== 'clarification_needed' && b.current_status === 'clarification_needed') {
          return 1; // b comes first
        }

        // Then sort by date
        const dateA = new Date(a.created_at || '').getTime();
        const dateB = new Date(b.created_at || '').getTime();

        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
      });

      return sortedData;
    },
  });

  return {
    ...queryResult,
    refetch: queryResult.refetch
  };
};

export const useStatusHistory = (enquiryId: string | undefined) => {
  return useQuery({
    queryKey: ["enquiry-status-history", enquiryId],
    enabled: !!enquiryId,
    retry: 3, // Retry failed requests up to 3 times
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff strategy
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("enquiry_status_history")
          .select("*")
          .eq("enquiry_id", enquiryId)
          .order("created_at", { ascending: false });

        console.log("data here", data)
        if (error) throw error;
        return data;
      } catch (fetchError) {
        console.error("Error fetching status history:", fetchError);
        throw fetchError; // Re-throw to trigger retry mechanism
      }
    },
  });
};

// New hook for fetching enquiry documents
export const useEnquiryDocuments = (enquiryId: string | undefined) => {
  return useQuery({
    queryKey: ["enquiry-documents", enquiryId],
    enabled: !!enquiryId,
    retry: 3, // Retry failed requests up to 3 times
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff strategy
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("enquiry_documents")
          .select("*")
          .eq("enquiry_id", enquiryId);

        if (error) throw error;
        return data || [];
      } catch (fetchError) {
        console.error("Error fetching enquiry documents:", fetchError);
        throw fetchError; // Re-throw to trigger retry mechanism
      }
    },
  });
};
