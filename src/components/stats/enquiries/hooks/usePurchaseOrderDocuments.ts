import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase, STORAGE_BUCKETS } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";

export type PurchaseOrderDocument = {
  id: string;
  file_name: string;
  file_path: string;
  content_type: string;
  url: string;
  size?: number;
  doc_type: 'PO_FILE' | 'Others';
  po_number?: string;
  notes?: string;
  created_at: string;
  po_created_at: string;
};

// Define specific types for the purchase order
type PurchaseOrder = Database['public']['Tables']['purchase_orders']['Row'];

export const usePurchaseOrderDocuments = (enquiryStatusId?: string) => {
  const [documents, setDocuments] = useState<PurchaseOrderDocument[]>([]);

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ["purchase-order-documents", enquiryStatusId],
    queryFn: async () => {
      if (!enquiryStatusId) return null;

      try {
        // STEP 1: Get the purchase order for this enquiry
        const { data: poData, error: poError } = await supabase
          .from('purchase_orders')
          .select<'purchase_orders', Database['public']['Tables']['purchase_orders']['Row']>('id, po_number, notes, created_at, created_by, status')
          .eq('history_status_id', enquiryStatusId)
          .single();

        if (poError) {
          console.error("[ERROR] Error fetching purchase order:", poError);
          throw new Error(`Failed to fetch purchase order: ${poError.message}`);
        }

        if (!poData) {
          console.log("[INFO] No purchase order found for status history:", enquiryStatusId);
          return [];
        }

        // STEP 2: Get the attachments
        const { data: attachments, error: attachmentsError } = await supabase
          .from('purchase_order_attachments')
          .select('id, file_name, file_path, content_type, size, doc_type, created_at')
          .eq('purchase_order_id', poData.id)
          .order('doc_type', { ascending: false })  // PO_FILE first, then Others
          .order('created_at', { ascending: false });

        if (attachmentsError) {
          throw new Error(`Failed to fetch attachments: ${attachmentsError.message}`);
        }

        // Add URLs and PO details to attachments
        const documentsWithUrls = (attachments || []).map(attachment => {
          const { data } = supabase.storage
            .from(STORAGE_BUCKETS.PURCHASE_ORDER_DOCUMENTS)
            .getPublicUrl(attachment.file_path);

          return {
            ...attachment,
            url: data?.publicUrl || '',
            po_number: poData.po_number,  // Include PO number from poData
            notes: poData.notes || '',    // Include notes from poData
            po_created_at: poData.created_at,
          } as PurchaseOrderDocument;
        });

        // Sort documents by type (PO_FILE first, then Others)
        const sortedDocuments = documentsWithUrls.sort((a, b) => {
          if (a.doc_type === 'PO_FILE' && b.doc_type !== 'PO_FILE') return -1;
          if (a.doc_type !== 'PO_FILE' && b.doc_type === 'PO_FILE') return 1;
          return 0;
        });

        return sortedDocuments;

      } catch (error: any) {
        console.error("[ERROR] Error in usePurchaseOrderDocuments:", error);
        throw error;
      }
    },
    enabled: !!enquiryStatusId,
  });

  useEffect(() => {
    if (data) {
      setDocuments(data);
    }
  }, [data]);

  return {
    documents,
    isLoading,
    error,
    refetch,
  };
};
