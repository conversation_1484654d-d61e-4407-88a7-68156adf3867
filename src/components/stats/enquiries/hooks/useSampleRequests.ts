import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

// Define the sample request type
export interface SampleRequest {
  id: string;
  enquiry_id: string;
  created_at: string;
  created_by: string;
  delivery_address?: string;
  delivery_city?: string;
  delivery_country?: string;
  delivery_postal_code?: string;
  quantity?: number;
  quantity_unit?: string;
  sample_description?: string;
  status?: string;
  status_history_id?: string;
  remarks?: string; // This will be used as notes
}

/**
 * Custom hook to fetch sample requests for a specific enquiry
 * @param enquiryId The ID of the enquiry to fetch sample requests for
 * @returns Object containing sample requests data, loading state, and refetch function
 */
export const useSampleRequests = (enquiryId: string | undefined) => {
  const {
    data: sampleRequests,
    isLoading,
    refetch,
  } = useQuery<SampleRequest[]>({
    queryKey: ["sample-requests", enquiryId],
    enabled: !!enquiryId,
    queryFn: async () => {
      if (!enquiryId) return [];

      const { data, error } = await supabase
        .from("sample_requests")
        .select("*")
        .eq("enquiry_id", enquiryId)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching sample requests:", error);
        throw error;
      }

      return data || [];
    },
  });

  return {
    sampleRequests: sampleRequests || [],
    isLoading,
    refetch,
    hasSampleRequests: sampleRequests && sampleRequests.length > 0,
  };
};
