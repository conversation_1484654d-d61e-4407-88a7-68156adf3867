
import { useState } from "react";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { uploadFeedbackAttachments } from "./useFeedbackAttachments";

interface ValidationError {
  type: 'response' | 'reason';
  message: string;
}

export const useQuotationFeedback = (
  enquiryId?: string,
  onClose?: () => void,
  onSuccess?: () => void,
  enquiryStatusId?: string
) => {
  const [selectedResponse, setSelectedResponse] = useState<string>("");
  const [reason, setReason] = useState<string>("");
  const [rejectionReason, setRejectionReason] = useState<string>("");
  const [files, setFiles] = useState<File[]>([]);
  const [validationError, setValidationError] = useState<ValidationError | null>(null);
  
  const submitFeedbackMutation = useMutation({
    mutationFn: async () => {
      // Clear any previous validation errors
      setValidationError(null);

      
      if (!enquiryId) {
        console.error("Missing enquiry ID");
        throw new Error("Missing enquiry ID");
      }
      
      console.log("Starting feedback submission:", {
        enquiryStatusId,
        enquiryId,
        selectedResponse,
        reason,
        filesCount: files.length
      });
      
      // Get the current user session
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error("User must be authenticated to submit feedback");
      }
      
      // Submit feedback to Supabase
      const { data: feedbackData, error: feedbackError } = await supabase
        .from("quotation_feedback")
        .insert({
          enquiry_id: enquiryId,
          pricing_quote_id: enquiryStatusId,
          response: selectedResponse,
          remarks: reason,
          reason: rejectionReason,
          submitted_by: session.user.email,
        })
        .select('id')
        .single();
      
      if (feedbackError) {
        console.error("Error submitting feedback:", feedbackError);
        throw feedbackError;
      }
      
      if (!feedbackData) {
        throw new Error("Failed to get feedback ID after submission");
      }
      
      console.log("Feedback submitted successfully, ID:", feedbackData.id);
      
      // Handle file uploads if any
      if (files.length > 0) {
        console.log("Starting file uploads for feedback:", feedbackData.id);
        const uploadResult = await uploadFeedbackAttachments(feedbackData.id, files, "quotation");
        
        if (!uploadResult.success) {
          console.warn("File upload issues:", uploadResult.error);
          toast.error(`Some files failed to upload: ${uploadResult.error}`);
        } else {
          console.log("Files uploaded successfully");
          toast.success("Files uploaded successfully");
        }
      }
      
      return "Feedback submitted successfully";
    },
    onSuccess: () => {
      console.log("Feedback submission completed successfully");
      toast.success("Quotation feedback submitted successfully");
      
      if (onSuccess) {
        console.log("Calling onSuccess callback");
        onSuccess();
      }
      
      if (onClose) {
        console.log("Calling onClose callback");
        onClose();
      }
    },
    onError: (error: Error) => {
      console.error("Error in feedback submission:", error);
      toast.error(error.message);
    }
  });
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    submitFeedbackMutation.mutate();
  };
  
  return {
    selectedResponse,
    setSelectedResponse,
    reason,
    setReason,
    rejectionReason,
    setRejectionReason,
    files,
    setFiles,
    isSubmitting: submitFeedbackMutation.isPending,
    handleSubmit,
    validationError
  };
};
