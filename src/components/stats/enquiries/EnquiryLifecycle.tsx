
import { useFeedbackData } from "@/components/stats/enquiries/hooks/useFeedbackData";
import EnquiryTimeline from "./components/EnquiryTimeline";
import { Database } from "@/integrations/supabase/types";

type StatusHistoryItem = Database["public"]["Tables"]["enquiry_status_history"]["Row"];
type EnquiryLifecycleStatus = Database["public"]["Enums"]["enquiry_lifecycle_status"] | string;

interface EnquiryLifecycleProps {
  enquiryId: string;
  currentStatus: EnquiryLifecycleStatus;
  statusHistory: StatusHistoryItem[];
  onRefresh?: () => void; // Refresh callback prop
}

const EnquiryLifecycle = ({ 
  enquiryId, 
  currentStatus, 
  statusHistory, 
  onRefresh 
}: EnquiryLifecycleProps) => {
  const { hasSampleFeedback,hasQuotationFeedback, isLoadingFeedback, sampleFeedback,quotationFeedback } = useFeedbackData(enquiryId);

  console.log("EnquiryLifecycle component rendering:", {
    enquiryId,
    currentStatus,
    statusHistoryCount: statusHistory?.length || 0,
    sampleFeedback,
    quotationFeedback,
    isLoadingFeedback
  });

  // Ensure statusHistory is always an array
  const safeStatusHistory = Array.isArray(statusHistory) ? statusHistory : [];

  // Update to include valid quote-related statuses
  const quoteRelatedStatuses = [
    'pricing_quotation_generated', 
    'quote_rejected', 
    'quote_redo',
    'quote_accepted'
  ];
  
  // Update to include valid sample-related statuses
  const sampleRelatedStatuses = [
    'sample_requested',
    'sample_delivered',
    'sample_accepted',
    'sample_rejected',
    'sample_redo'
  ];

  // Find any quote-related statuses in the history
  const quoteStatusEntries = safeStatusHistory.filter(item => 
    quoteRelatedStatuses.includes(item.status as string)
  );
  
  // Find any sample-related statuses in the history
  const sampleStatusEntries = safeStatusHistory.filter(item => 
    sampleRelatedStatuses.includes(item.status as string)
  );

  console.log("Feedback status check in EnquiryLifecycle:", { 
    enquiryId, 
    // hasFeedback, 
    // feedbackData,
    currentStatus,
    // feedbackType: feedbackData?.type,
    // feedbackResponse: feedbackData?.response,
    statusesWithFeedback: [...quoteStatusEntries, ...sampleStatusEntries],
    hasQuotationStatus: quoteStatusEntries.length > 0,
    hasSampleStatus: sampleStatusEntries.length > 0,
    isPricingQuotationGenerated: safeStatusHistory.some(item => item.status === 'pricing_quotation_generated'),
    isSampleDelivered: safeStatusHistory.some(item => item.status === 'sample_delivered') || currentStatus === 'sample_delivered'
  });

  return (
    <div className="px-2 py-3">
      <EnquiryTimeline
        enquiryId={enquiryId}
        currentStatus={currentStatus}
        statusHistory={safeStatusHistory}
        sampleFeedback={sampleFeedback}
        quotationFeedback={quotationFeedback}
        hasSampleFeedback={hasSampleFeedback}
        hasQuotationFeedback={hasQuotationFeedback}
        onSampleRequestSuccess={onRefresh} // Pass the refresh callback
      />
    </div>
  );
};

export default EnquiryLifecycle;
