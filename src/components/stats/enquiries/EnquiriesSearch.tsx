
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

interface EnquiriesSearchProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
}

const EnquiriesSearch = ({ searchTerm, onSearchChange }: EnquiriesSearchProps) => {
  return (
    <div className="relative mt-4 animate-fade-in">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
      <Input
        placeholder="Search enquiries..."
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
        className="pl-10 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-600 transition-colors focus-visible:ring-primary-400 dark:focus-visible:ring-primary-600"
      />
    </div>
  );
};

export default EnquiriesSearch;
