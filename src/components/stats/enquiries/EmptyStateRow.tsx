
import { TableCell, TableRow } from "@/components/ui/table";
import { FileX } from "lucide-react";

interface EmptyStateRowProps {
  colSpan: number;
}

const EmptyStateRow = ({ colSpan }: EmptyStateRowProps) => {
  return (
    <TableRow>
      <TableCell colSpan={colSpan} className="h-32 text-center">
        <div className="flex flex-col items-center justify-center space-y-2 text-gray-500 dark:text-gray-400">
          <FileX className="h-8 w-8 opacity-50" />
          <p className="text-sm font-medium">No enquiries found</p>
        </div>
      </TableCell>
    </TableRow>
  );
};

export default EmptyStateRow;
