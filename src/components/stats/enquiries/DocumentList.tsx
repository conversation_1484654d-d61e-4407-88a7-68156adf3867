
import { Button } from "@/components/ui/button";
import { Database } from "@/integrations/supabase/types";
import { FileIcon, Download } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

type DocumentType = Database['public']['Tables']['enquiry_documents']['Row'];

interface DocumentListProps {
  documents: DocumentType[];
}

const DocumentList = ({ documents }: DocumentListProps) => {
  const handleDownload = async (filePath: string, fileName: string) => {
    try {
      const { data, error } = await supabase.storage
        .from('enquiry_documents')
        .download(filePath);

      if (error) {
        throw error;
      }

      const url = window.URL.createObjectURL(data);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
      toast.error('Failed to download file');
    }
  };

  return (
    <div className="space-y-2 animate-fade-in">
      {documents.map((doc) => (
        <div 
          key={doc.id} 
          className="flex items-center justify-between p-3 rounded-lg border border-gray-100 dark:border-gray-700 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:bg-white dark:hover:bg-gray-700/50 transition-all duration-200 transform hover:scale-[1.01]"
        >
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-full bg-primary-50 dark:bg-primary-900/20">
              <FileIcon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700 dark:text-gray-200">{doc.file_name}</p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {doc.size ? `${(doc.size / 1024 / 1024).toFixed(2)} MB` : 'Size unknown'}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDownload(doc.file_path, doc.file_name)}
            className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 hover:bg-primary-50 dark:hover:bg-primary-900/20"
          >
            <Download className="h-4 w-4" />
          </Button>
        </div>
      ))}
    </div>
  );
};

export default DocumentList;
