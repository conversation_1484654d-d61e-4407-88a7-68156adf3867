
import { EnquiryType } from "./hooks/useEnquiriesData";
import EnquiryCard from "./EnquiryCard";

interface EnquiryGridProps {
  filteredEnquiries: EnquiryType[] | undefined;
  type: "total" | "recent";
  selectedEnquiryId: string | null;
  onSelectEnquiry: (enquiry: EnquiryType) => void;
}

const EnquiryGrid = ({
  filteredEnquiries,
  type,
  selectedEnquiryId,
  onSelectEnquiry
}: EnquiryGridProps) => {

  console.log("Rendering EnquiryGrid with:", {
    filteredEnquiriesCount: filteredEnquiries?.length || 0,
    selectedEnquiryId,
  });

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      {filteredEnquiries?.map((enquiry) => (
        <EnquiryCard
          key={`${enquiry.id}-${enquiry.current_status}-${enquiry.enquiry_id || ''}`}
          enquiry={enquiry}
          type={type}
          isSelected={selectedEnquiryId === enquiry.id}
          onClick={() => onSelectEnquiry(enquiry)}
          hideCancel={true}
          onRefresh={() => onSelectEnquiry(enquiry)}
        />
      ))}
      {filteredEnquiries?.length === 0 && (
        <div className="col-span-full text-center py-8 bg-white/50 rounded-lg border border-[#E5DEFF]">
          <p className="text-gray-500">No enquiries found</p>
        </div>
      )}
    </div>
  );
};

export default EnquiryGrid;
