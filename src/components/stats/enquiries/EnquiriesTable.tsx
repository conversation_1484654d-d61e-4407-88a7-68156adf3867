
import { Table, TableBody } from "@/components/ui/table";
import { Database } from "@/integrations/supabase/types";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import DocumentList from "./DocumentList";
import EnquiriesTableHeader from "./EnquiriesTableHeader";
import EnquiriesTableRow from "./EnquiriesTableRow";
import EmptyStateRow from "./EmptyStateRow";

type EnquiryType = Database['public']['Views']['get_enquiry_board']['Row'] & {
  enquiry_documents: Database['public']['Tables']['enquiry_documents']['Row'][];
  status_history?: Database['public']['Tables']['enquiry_status_history']['Row'][];
};

// We need to define a draft type without referencing non-existent tables
type DraftEnquiryType = {
  id: string;
  created_at?: string;
  customer_full_name?: string;
  current_status?: string;
  enquiry_id?: string;
  chemical_name?: string;
  brand?: string;
  product?: string;
  country?: string;
  quantity?: number;
  target_quotation_date?: string;
  enquiry_documents: Database['public']['Tables']['enquiry_documents']['Row'][];
};

interface EnquiriesTableProps {
  type: "total" | "recent" | "drafts";
  enquiries: (EnquiryType | DraftEnquiryType)[];
  expandedRow: string | null;
  onExpandRow: (id: string | null) => void;
  onShowLifecycle?: (enquiry: EnquiryType) => void;
  selectedEnquiryId?: string | null;
}

const EnquiriesTable = ({
  type,
  enquiries: rawEnquiries,
  expandedRow,
  onExpandRow,
  onShowLifecycle,
  selectedEnquiryId
}: EnquiriesTableProps) => {
  // Sort enquiries to prioritize clarification_needed and then by date (newest first)
  const enquiries = [...rawEnquiries].sort((a, b) => {
    // Check if items are EnquiryType (have current_status)
    const aIsEnquiry = 'current_status' in a;
    const bIsEnquiry = 'current_status' in b;

    // If both are enquiries, prioritize clarification_needed status
    if (aIsEnquiry && bIsEnquiry) {
      const aStatus = (a as EnquiryType).current_status;
      const bStatus = (b as EnquiryType).current_status;

      if (aStatus === 'clarification_needed' && bStatus !== 'clarification_needed') {
        return -1; // a comes first
      }
      if (aStatus !== 'clarification_needed' && bStatus === 'clarification_needed') {
        return 1; // b comes first
      }
    }

    // If only one is an enquiry with clarification_needed, prioritize it
    if (aIsEnquiry && (a as EnquiryType).current_status === 'clarification_needed' && (!bIsEnquiry || (b as EnquiryType).current_status !== 'clarification_needed')) {
      return -1;
    }
    if (bIsEnquiry && (b as EnquiryType).current_status === 'clarification_needed' && (!aIsEnquiry || (a as EnquiryType).current_status !== 'clarification_needed')) {
      return 1;
    }

    // Then sort by date (newest first)
    const dateA = new Date(a.created_at || '').getTime();
    const dateB = new Date(b.created_at || '').getTime();

    return dateB - dateA; // Newest first
  });
  const handleRequestSample = async (enquiryId: string) => {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      toast.error('You must be logged in to request a sample');
      return;
    }

    try {
      // Update the enquiry status
      const { error: statusError } = await supabase
        .from('enquiries')
        .update({
          current_status: 'sample_requested',
          last_status_change: new Date().toISOString(),
          sample_requested_at: new Date().toISOString()
        })
        .eq('id', enquiryId);

      if (statusError) throw statusError;

      // Add status history entry
      const { error: historyError } = await supabase
        .from('enquiry_status_history')
        .insert({
          enquiry_id: enquiryId,
          status: 'sample_requested',
          changed_by: session.user.email,
          sales_agent_email: session.user.email || '',
          notes: 'Sample requested by customer'
        });

      if (historyError) throw historyError;

      toast.success('Sample request submitted successfully');
    } catch (error) {
      console.error('Error requesting sample:', error);
      toast.error('Failed to submit sample request');
    }
  };

  const handleCancelEnquiry = async (enquiryId: string) => {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      toast.error('You must be logged in to cancel an enquiry');
      return;
    }

    try {
      // Update the enquiry status
      const { error: statusError } = await supabase
        .from('enquiries')
        .update({
          current_status: 'cancelled',
          last_status_change: new Date().toISOString()
        })
        .eq('id', enquiryId);

      if (statusError) throw statusError;

      // Add status history entry
      const { error: historyError } = await supabase
        .from('enquiry_status_history')
        .insert({
          enquiry_id: enquiryId,
          status: 'cancelled',
          changed_by: session.user.email,
          sales_agent_email: session.user.email || '',
          notes: 'Enquiry cancelled by customer'
        });

      if (historyError) throw historyError;

      toast.success('Enquiry cancelled successfully');
    } catch (error) {
      console.error('Error cancelling enquiry:', error);
      toast.error('Failed to cancel enquiry');
    }
  };

  return (
    <Table>
      <EnquiriesTableHeader type={type} />
      <TableBody>
        {enquiries.map((enquiry) => (
          <>
            <EnquiriesTableRow
              key={`${enquiry.id}-${enquiry?.current_status}-${enquiry?.enquiry_id || ''}`}
              enquiry={enquiry}
              type={type}
              expandedRow={expandedRow}
              onExpandRow={onExpandRow}
              onRequestSample={type !== 'drafts' ? handleRequestSample : undefined}
              onCancelEnquiry={type !== 'drafts' ? handleCancelEnquiry : undefined}
              isSelected={selectedEnquiryId === enquiry.id}
            />
            {expandedRow === enquiry.id && enquiry.enquiry_documents && (
              <tr>
                <td colSpan={type === 'drafts' ? 6 : 7} className="bg-gray-50 py-4">
                  <DocumentList documents={enquiry.enquiry_documents} />
                </td>
              </tr>
            )}
          </>
        ))}
        {enquiries.length === 0 && (
          <EmptyStateRow colSpan={type === 'drafts' ? 6 : 7} />
        )}
      </TableBody>
    </Table>
  );
};

export default EnquiriesTable;
