/* Timeline styles */
.timeline-container {
  max-height: 90vh;
  overflow-y: auto;
}

.timeline-header {
  border-bottom: 1px solid #e5e7eb;
  padding: 1.5rem;
}

.timeline-body {
  padding: 1.5rem;
}

.timeline-item {
  display: flex;
  margin-bottom: 2rem;
}

.timeline-item:last-child .timeline-line {
  display: none;
}

.timeline-icon {
  position: relative;
  margin-right: 1rem;
  flex-shrink: 0;
}

.timeline-icon-circle {
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.timeline-line {
  position: absolute;
  top: 2rem;
  bottom: -2rem;
  left: 50%;
  width: 1px;
  background-color: #e5e7eb;
  transform: translateX(-50%);
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.timeline-title h4 {
  font-weight: 500;
  font-size: 1rem;
  color: #111827;
}

.timeline-date {
  font-size: 0.875rem;
  color: #6b7280;
}

.timeline-description {
  font-size: 0.875rem;
  color: #4b5563;
  margin-bottom: 0.75rem;
}

.timeline-tracking {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.timeline-tracking-item {
  text-align: center;
}

.timeline-tracking-title {
  font-size: 0.75rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.timeline-tracking-date {
  font-size: 0.75rem;
  color: #6b7280;
}

.timeline-tracking-time {
  font-size: 0.75rem;
  color: #6b7280;
}

.timeline-details {
  margin-top: 0.75rem;
  background-color: #f9fafb;
  border-radius: 0.375rem;
  padding: 0.75rem;
}

.timeline-details-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
  font-size: 0.875rem;
}

.timeline-details-label {
  color: #6b7280;
}

.timeline-details-value {
  font-weight: 500;
}

.timeline-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 0.75rem;
}

.timeline-feedback {
  margin-top: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.timeline-feedback-label {
  font-size: 0.875rem;
  color: #4b5563;
}

.timeline-status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.timeline-status-badge.accepted {
  background-color: #ecfdf5;
  color: #047857;
}

.timeline-status-badge.rejected {
  background-color: #fef2f2;
  color: #b91c1c;
}

.timeline-status-badge.redo {
  background-color: #fffbeb;
  color: #b45309;
}

/* Custom scrollbar for the timeline */
.timeline-container::-webkit-scrollbar {
  width: 6px;
}

.timeline-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.timeline-container::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 10px;
}

.timeline-container::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
