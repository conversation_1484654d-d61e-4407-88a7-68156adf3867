import {
  <PERSON>ert<PERSON>riangle,
  CheckCircle2,
  CircleDot,
  ClipboardCheck,
  FileX,
  Hourglass,
  Package,
  PackageCheck,
  PackageSearch,
  PenTool,
  Quote,
  Redo2,
  Send,
  ShoppingCart,
  TestTubes,
  Truck,
  HelpCircle,
  FileWarning,
  FileText,
} from "lucide-react";

// Standard lifecycle states - updated to include the new value
export const LIFECYCLE_STATES = [
  "enquiry_created",
  "enquiry_assigned",
  "regret",
  "clarification_needed",
  "pricing_quotation_generated",
  "quote_redo",
  "quote_rejected",
  "quote_accepted",
  "sample_requested",
  "sample_available",
  "sample_in_transit",
  "sample_delivered",
  "sample_accepted",
  "sample_redo",
  "sample_rejected",
  "po_raised",
  "cancelled"
];

// Legacy states that might still exist in data but not part of current enum
export const LEGACY_STATES = [
  "new_quote_wip",
  "quote_shared",
  "lost_no_response",
  "sample_shared",
  "sample_approved",
  "order_received",
  "order_fulfilled",
  "new_enquiry" // This is now a legacy state that we keep for backwards compatibility
];

// Combined states for backward compatibility with existing data
export const ALL_POSSIBLE_STATES = [
  ...LIFECYCLE_STATES,
  ...LEGACY_STATES
];

export const getStatusIcon = (status: string) => {
  switch (status) {
    case "enquiry_created":
      return <CircleDot className="h-5 w-5 text-[#9b87f5]" />;
    case "enquiry_assigned":
      return <PenTool className="h-5 w-5 text-[#9b87f5]" />;
    case "regret":
      return <FileWarning className="h-5 w-5 text-red-500" />;
    case "clarification_needed":
      return <HelpCircle className="h-5 w-5 text-amber-500" />;
    case "pricing_quotation_generated":
      return <FileText className="h-5 w-5 text-emerald-500" />;
    case "quote_redo":
      return <Redo2 className="h-5 w-5 text-amber-500" />;
    case "quote_rejected":
      return <FileX className="h-5 w-5 text-red-500" />;
    case "quote_accepted":
      return <CheckCircle2 className="h-5 w-5 text-emerald-500" />;
    case "sample_requested":
      return <TestTubes className="h-5 w-5 text-[#7E69AB]" />;
    case "sample_available":
      return <Package className="h-5 w-5 text-[#7E69AB]" />;
    case "sample_in_transit":
      return <Truck className="h-5 w-5 text-[#7E69AB]" />;
    case "sample_delivered":
      return <PackageCheck className="h-5 w-5 text-[#7E69AB]" />;
    case "sample_accepted":
      return <ClipboardCheck className="h-5 w-5 text-emerald-500" />;
    case "sample_redo":
      return <Redo2 className="h-5 w-5 text-amber-500" />;
    case "sample_rejected":
      return <AlertTriangle className="h-5 w-5 text-red-500" />;
    case "po_raised":
      return <ShoppingCart className="h-5 w-5 text-emerald-500" />;
    case "cancelled":
      return <FileX className="h-5 w-5 text-red-500" />;
    // Handle legacy values that might exist in the database
    case "new_quote_wip":
    case "quote_shared":
      return <Quote className="h-5 w-5 text-amber-500" />;
    case "lost_no_response":
      return <FileX className="h-5 w-5 text-red-500" />;
    case "sample_shared":
    case "sample_approved":
      return <ClipboardCheck className="h-5 w-5 text-emerald-500" />;
    case "order_received":
    case "order_fulfilled":
    case "new_enquiry": // Handle legacy status by mapping it to the new status icon
      return <CircleDot className="h-5 w-5 text-[#9b87f5]" />; // Same icon as enquiry_created
    default:
      return <CircleDot className="h-5 w-5 text-gray-500" />;
  }
};

export const getStatusColor = (status: string, isActive: boolean) => {
  const baseColors: Record<string, string> = {
    enquiry_created: "bg-[#E5DEFF] border-[#9b87f5]",
    enquiry_assigned: "bg-[#E5DEFF] border-[#9b87f5]",
    regret: "bg-red-50 border-red-300",
    clarification_needed: "bg-amber-50 border-amber-300",
    pricing_quotation_generated: "bg-green-50 border-green-300",
    quote_redo: "bg-amber-50 border-amber-300",
    quote_rejected: "bg-red-50 border-red-300",
    quote_accepted: "bg-green-50 border-green-300",
    sample_requested: "bg-[#E5DEFF] border-[#7E69AB]",
    sample_available: "bg-[#E5DEFF] border-[#7E69AB]",
    sample_in_transit: "bg-[#E5DEFF] border-[#7E69AB]",
    sample_delivered: "bg-[#E5DEFF] border-[#7E69AB]",
    sample_accepted: "bg-green-50 border-green-300",
    sample_redo: "bg-amber-50 border-amber-300",
    sample_rejected: "bg-red-50 border-red-300",
    po_raised: "bg-emerald-50 border-emerald-300",
    cancelled: "bg-red-50 border-red-300",
    // Legacy statuses
    new_quote_wip: "bg-amber-50 border-amber-300",
    quote_shared: "bg-amber-50 border-amber-300",
    lost_no_response: "bg-red-50 border-red-300",
    sample_shared: "bg-green-50 border-green-300",
    sample_approved: "bg-green-50 border-green-300",
    order_received: "bg-emerald-50 border-emerald-300",
    order_fulfilled: "bg-emerald-50 border-emerald-300",
    new_enquiry: "bg-[#E5DEFF] border-[#9b87f5]", // Map legacy status to same color as enquiry_created
  };

  const colorClass = baseColors[status] || "bg-gray-50 border-gray-300";
  return colorClass;
};

export const formatStatus = (status: string) => {
  const specialCases: Record<string, string> = {
    "clarification_needed": "Clarification Needed",
    "po_raised": "Purchase Order Raised",
    "pricing_quotation_generated": "Pricing Quotation Generated",
    "quote_redo": "Quotation Redo Requested",
    "quote_accepted": "Quotation Accepted",
    "quote_rejected": "Quotation Rejected",
    // Legacy statuses
    "new_quote_wip": "New Quote In Progress",
    "quote_shared": "Quote Shared",
    "lost_no_response": "Lost - No Response",
    "sample_shared": "Sample Shared",
    "sample_approved": "Sample Approved",
    "order_received": "Order Received",
    "order_fulfilled": "Order Fulfilled",
    "new_enquiry": "New Enquiry", // Keep this for displaying legacy status values
  };
  
  if (specialCases[status]) {
    return specialCases[status];
  }
  
  return status
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};
