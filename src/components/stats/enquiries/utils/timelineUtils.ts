
import { Database } from "@/integrations/supabase/types";

type StatusHistoryItem = Database["public"]["Tables"]["enquiry_status_history"]["Row"];

export const getStatusDetails = (status: string, statusHistory: StatusHistoryItem[]) => {
  const historyItem = statusHistory.find(item => item.status === status);
  return {
    isCompleted: !!historyItem,
    date: historyItem?.created_at,
    notes: historyItem?.notes,
    procurementPOC: historyItem?.procurement_poc
  };
};

export const sortStatusHistory = (statusHistory: StatusHistoryItem[]) => {
  return [...statusHistory].sort((a, b) => 
    new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
  );
};

export const formatTimeDifference = (currentDate: Date, previousDate: Date | null) => {
  if (!previousDate) return '';
  const days = Math.max(0, Math.floor((currentDate.getTime() - previousDate.getTime()) / (1000 * 60 * 60 * 24)));
  if (days === 0) return 'Same day';
  if (days === 1) return '+1 day';
  return `+${days} days`;
};
