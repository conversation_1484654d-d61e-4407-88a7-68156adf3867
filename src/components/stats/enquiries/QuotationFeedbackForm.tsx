
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { CheckCircle, XCircle, RefreshCw } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import CompactDocumentUpload from "@/components/enquiries/components/CompactDocumentUpload";
import FormControls from "./components/FormControls";
import { useQuotationFeedback } from "./hooks/useQuotationFeedback";
import { useEffect } from "react";
import { toast } from "sonner";

interface QuotationFeedbackFormProps {
  onClose: () => void;
  enquiryId?: string;
  onSuccess?: () => void;
  enquiryStatusId?:string;
}

// Add rejection reasons
const QUOTE_REJECT_REASONS = [
  "Price mismatch",
  "Packaging mismatch",
  "TDS/COA mismatch",
  "Others"
] as const;

const QUOTE_REDO_REASONS = [
  "Price mismatch",
  "Packaging mismatch",
  "TDS/COA mismatch",
  "On hold by customer",
  "Others"
] as const;

// Define the rejection reason type
type RejectionReason = typeof QUOTE_REJECT_REASONS[number] | typeof QUOTE_REDO_REASONS[number];

const QuotationFeedbackForm = ({ onClose, enquiryId, onSuccess, enquiryStatusId }: QuotationFeedbackFormProps) => {
  const {
    selectedResponse,
    setSelectedResponse,
    reason,
    setReason,
    rejectionReason,
    setRejectionReason,
    files,
    setFiles,
    isSubmitting,
    handleSubmit,
    validationError
  } = useQuotationFeedback(enquiryId, onClose, onSuccess, enquiryStatusId);

  // Show dropdown for reject/redo responses
  const showRejectionReason = selectedResponse === 'quote_rejected' || selectedResponse === 'quote_redo';

  // Clear rejection reason when response type changes to non-reject/redo
  useEffect(() => {
    if (!showRejectionReason && rejectionReason) {
      setRejectionReason('');
    }
  }, [selectedResponse, showRejectionReason, rejectionReason]);

  // Check if enquiryId exists
  useEffect(() => {
    if (!enquiryId) {
      console.error("Missing enquiry ID in QuotationFeedbackForm");
      toast.error("Cannot submit feedback: Missing enquiry ID");
      onClose();
    }
  }, [enquiryId, onClose]);

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (showRejectionReason && !rejectionReason) {
      toast.error("Please select a reason");
      return;
    }

    if (showRejectionReason && !reason) {
      toast.error("Please provide remarks for the selected reason");
      return;
    }

    if (!selectedResponse) {
      toast.error("Please select a response option");
      return;
    }

    handleSubmit(e);
  };

  // Get the appropriate reasons based on selected response
  const rejectionReasons = selectedResponse === 'quote_rejected'
    ? QUOTE_REJECT_REASONS
    : QUOTE_REDO_REASONS;

  return (
    <Card className="p-4 bg-white/80 backdrop-blur-md border border-[#E5DEFF] rounded-lg">
      {/* <div className="flex justify-between items-center mb-4">
        <h3 className="text-sm font-medium text-[#7E69AB]">Quotation Feedback</h3>
        <Button
          variant="ghost"
          size="sm"
          className="h-7 w-7 p-0 rounded-full hover:bg-gray-100"
          onClick={onClose}
        >
          <X className="h-4 w-4 text-gray-500" />
        </Button>
      </div> */}

      <form onSubmit={onSubmit} className="space-y-4">
        {/* Compact row for Response, Reason, Remarks, and Attachment */}
        <div className="grid grid-cols-12 gap-3">
          {/* Response dropdown */}
          <div className="space-y-1 col-span-12 sm:col-span-3">
            <Label htmlFor="responseType" className="text-12x text-gray-700">Response <span className="text-red-500">*</span></Label>
            <Select
              value={selectedResponse}
              onValueChange={setSelectedResponse}
            >
              <SelectTrigger className="w-full h-8 text-xs">
                <SelectValue placeholder="Select response" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="quote_accepted">
                  <div className="flex items-center gap-1.5">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Accept Quotation</span>
                  </div>
                </SelectItem>
                <SelectItem value="quote_rejected">
                  <div className="flex items-center gap-1.5">
                    <XCircle className="h-4 w-4 text-red-600" />
                    <span>Reject Quotation</span>
                  </div>
                </SelectItem>
                <SelectItem value="quote_redo">
                  <div className="flex items-center gap-1.5">
                    <RefreshCw className="h-4 w-4 text-amber-600" />
                    <span>Request New Quotation</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            {validationError?.type === 'response' && (
              <p className="text-xs text-red-500 mt-1">{validationError.message}</p>
            )}
          </div>

          {/* Rejection Reason Dropdown - Only shown for reject/redo */}
          <div className="space-y-1 col-span-12 sm:col-span-3">
            <Label htmlFor="rejectionReason" className="text-12x text-gray-700">
              Reason {showRejectionReason && <span className="text-red-500">*</span>}
            </Label>
            {showRejectionReason ? (
              <Select
                value={rejectionReason}
                onValueChange={(value) => setRejectionReason(value)}
              >
                <SelectTrigger className="w-full h-8 text-xs">
                  <SelectValue placeholder="Select a reason" />
                </SelectTrigger>
                <SelectContent>
                  {rejectionReasons.map((reason: RejectionReason) => (
                    <SelectItem key={reason} value={reason}>
                      {reason}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <Select disabled>
                <SelectTrigger className="w-full h-8 text-xs">
                  <SelectValue placeholder="N/A" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="na">N/A</SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>

          {/* Reason field */}
          <div className="space-y-1 col-span-12 sm:col-span-4">
            <Label htmlFor="reason" className="text-12x text-gray-700">
              Remarks {showRejectionReason && <span className="text-red-500">*</span>}
            </Label>
            <Input
              id="reason"
              placeholder="Provide remarks..."
              value={reason}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setReason(e.target.value)}
              className="h-8 text-xs"
            />
            {validationError?.type === 'reason' && (
              <p className="text-xs text-red-500 mt-1">{validationError.message}</p>
            )}
          </div>

          {/* File attachment */}
          <div className="space-y-1 col-span-12 sm:col-span-2">
            <Label className="text-12x text-gray-700">Attachment</Label>
            <CompactDocumentUpload
              onFilesChange={setFiles}
              files={files}
              className="min-h-[60px] h-auto pb-2"
            />
          </div>
        </div>

        {/* Form controls */}
        <FormControls onClose={onClose} isSubmitting={isSubmitting} />
      </form>
    </Card>
  );
};

export default QuotationFeedbackForm;
