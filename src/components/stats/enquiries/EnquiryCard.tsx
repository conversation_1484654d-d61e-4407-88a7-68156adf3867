import { useState, useEffect } from "react";
import { format } from "date-fns";
import { Database } from "@/integrations/supabase/types";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  User,
  Package,
  Tag,
  Info,
  Globe,
  Hash,
  Paperclip,
  Edit,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { formatStatus } from "./utils/statusUtils";
import { Input } from "antd";
import { STORAGE_BUCKETS, supabase } from "@/integrations/supabase/client";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { Eye, Download, FileIcon } from "lucide-react";

type EnquiryLifecycleStatus =
  Database["public"]["Enums"]["enquiry_lifecycle_status"];

type EnquiryType = Database["public"]["Tables"]["enquiries"]["Row"] & {
  enquiry_documents: Database["public"]["Tables"]["enquiry_documents"]["Row"][];
  status_history?: Database["public"]["Tables"]["enquiry_status_history"]["Row"][];
  sample_requested?: boolean;
  sample_requested_at?: string;
  enquiry_id?: string;
};

interface EnquiryCardProps {
  enquiry: EnquiryType;
  type: "total" | "recent";
  isSelected?: boolean;
  onClick?: () => void;
  onRefresh?: () => void;
  hideCancel?: boolean;
}

const statusColors: Record<string, { bg: string; text: string }> = {
  enquiry_created: { bg: "bg-[#E6EAE9]", text: "text-[#294d48]" },
  enquiry_assigned: { bg: "bg-[#E6EAE9]", text: "text-[#294d48]" },
  pricing_quotation_generated: { bg: "bg-amber-100", text: "text-amber-700" },
  regret: { bg: "bg-red-100", text: "text-red-700" },
  clarification_needed: { bg: "bg-amber-100", text: "text-amber-700" },
  quote_accepted: { bg: "bg-green-100", text: "text-green-700" },
  quote_redo: { bg: "bg-amber-100", text: "text-amber-700" },
  quote_rejected: { bg: "bg-red-100", text: "text-red-700" },
  sample_requested: { bg: "bg-[#E6EAE9]", text: "text-[#294d48]" },
  sample_available: { bg: "bg-[#E6EAE9]", text: "text-[#294d48]" },
  sample_in_transit: { bg: "bg-[#E6EAE9]", text: "text-[#294d48]" },
  sample_delivered: { bg: "bg-[#E6EAE9]", text: "text-[#294d48]" },
  sample_accepted: { bg: "bg-green-100", text: "text-green-700" },
  sample_redo: { bg: "bg-amber-100", text: "text-amber-700" },
  sample_rejected: { bg: "bg-red-100", text: "text-red-700" },
  po_raised: { bg: "bg-emerald-100", text: "text-emerald-700" },
};

// Get criticality color
const getCriticalityColor = (criticality: string | null) => {
  switch (criticality) {
    case "high":
      return "border-red-200 bg-red-50/30";
    case "medium":
      return "border-amber-200 bg-amber-50/30";
    case "low":
      return "border-green-200 bg-green-50/30";
    default:
      return "border-[#E6EAE9]";
  }
};

const EnquiryCard = ({
  enquiry,
  isSelected,
  onClick,
  onRefresh,
  hideCancel = false,
}: EnquiryCardProps) => {
  const hasDocuments =
    enquiry.enquiry_documents && enquiry.enquiry_documents.length > 0;
  const [status, SetStatus] = useState(enquiry.current_status);
  const statusColor = statusColors[status] || {
    bg: "bg-gray-100",
    text: "text-gray-700",
  };

  const [isCancelVisible, setIsCancelVisible] = useState(false);
  const [cancelReason, setCancelReason] = useState("");
  const [isDocumentsOpen, setIsDocumentsOpen] = useState(false);


  // Format the date for display
  const formattedDate = enquiry.created_at
    ? format(new Date(enquiry.created_at), "MMM d, yyyy")
    : "Unknown date";

  // Format the last status change date
  const lastUpdated = enquiry.last_status_change
    ? format(new Date(enquiry.last_status_change), "MMM d, yyyy")
    : formattedDate;

  const criticality = enquiry.confidence || "medium";
  const criticalityColor = getCriticalityColor(criticality);

  const handleCancel = async () => {
    if (!cancelReason) {
      alert("Please provide a reason for cancellation.");
      return;
    }

    // Update the enquiry status to "cancelled" in the Supabase enquiries table
    const { error } = await supabase
      .from("enquiries")
      .update({ current_status: "cancelled" } as Partial<
        Database["public"]["Tables"]["enquiries"]["Row"]
      >)
      .eq("id", enquiry.id);

    const {
      data: { session },
    } = await supabase.auth.getSession();

    await supabase.from("enquiry_status_history").insert({
      enquiry_id: enquiry.id,
      status: "cancelled" as EnquiryLifecycleStatus,
      changed_by: session.user.email,
      sales_agent_email: session.user.email,
      notes: cancelReason,
    });

    SetStatus("cancelled");

    if (error) {
      console.error("Error updating enquiry status:", error);
      alert("Failed to cancel the enquiry. Please try again.");
      return;
    }

    if (error) {
      console.error("Error updating enquiry status:", error);
      alert("Failed to cancel the enquiry. Please try again.");
      return;
    }
    onRefresh && onRefresh();
    setIsCancelVisible(false);
  };

  // Function to handle file download
  const handleDownload = async (doc: EnquiryType["enquiry_documents"][0]) => {
    try {
      const { data, error } = await supabase.storage
        .from(STORAGE_BUCKETS.ENQUIRY_DOCUMENTS)
        .download(doc.file_path);

      if (error) {
        console.error('Download error:', error);
        alert('Failed to download file');
        return;
      }

      // Create a blob URL and trigger download
      const blob = new Blob([data], { type: data.type });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = doc.file_name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      alert('File downloaded successfully');
    } catch (error) {
      console.error('Download error:', error);
      alert('Failed to download file');
    }
  };

  // Create a new DocumentItem component
  const DocumentItem = ({
    doc,
  }: {
    doc: EnquiryType["enquiry_documents"][0];
  }) => {
    const [url, setUrl] = useState<string | null>(null);

    useEffect(() => {
      const getSignedUrl = async () => {
        try {
          const { data, error } = await supabase.storage
            .from(STORAGE_BUCKETS.ENQUIRY_DOCUMENTS)
            .createSignedUrl(doc.file_path, 3600);

          if (error) {
            console.error("Error getting signed URL:", error);
            return;
          }

          setUrl(data.signedUrl);
        } catch (error) {
          console.error("Error:", error);
        }
      };

      getSignedUrl();
    }, [doc.file_path]);

    return (
      <div
        key={doc.id}
        className="flex justify-between items-center p-3 bg-gray-100 rounded-md mb-2"
      >
        <div className="text-sm truncate flex-1 flex items-center">
          <FileIcon className="w-4 h-4 mr-2" />
          {doc.file_name}
        </div>
        <div className="flex space-x-2">
          {url ? (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(url, "_blank")}
              >
                <Eye className="w-4 h-4 mr-1" /> View
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDownload(doc)}
              >
                <Download className="w-4 h-4 mr-1" /> Download
              </Button>
            </>
          ) : (
            <span className="text-sm text-red-500">URL not available</span>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      <div
        onClick={onClick}
        className={cn(
          "bg-white/80 backdrop-blur-sm rounded-lg border transition-all duration-200 cursor-pointer group hover:shadow-md",
          criticalityColor,
          isSelected ? "border-[#294d48] shadow-md" : "hover:border-[#294d48]"
        )}
      >
        <div className="p-4 space-y-3">
          {/* Header with customer name, date, and status */}
          <div className="flex justify-between items-start">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-[#294d48]" />
              <div>
                <h3 className="font-medium text-gray-900 line-clamp-1">
                  {enquiry.customer_full_name || "No name provided"}
                </h3>
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Hash className="h-3 w-3" />
                  <span>{enquiry.enquiry_id}</span>
                </div>
              </div>
            </div>
            <div className="flex flex-col items-end">
              <Badge
                variant="secondary"
                className={cn("font-medium", statusColor.bg, statusColor.text)}
              >
                {formatStatus(status)}
              </Badge>
              {!isCancelVisible &&
                !hideCancel &&
                status != "cancelled" &&
                status != "po_raised" &&
                status != "regret" &&
                status != "quote_rejected" &&
                status != "sample_rejected" && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsCancelVisible(!isCancelVisible);
                    }}
                    className="text-sm mt-2 bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded-lg transition"
                  >
                    Cancel Enquiry
                  </button>
                )}
            </div>
          </div>

          {/* Cancel Reason Modal */}
          {isCancelVisible && (
            <>
              <div className="space-y-4">
                {/* Textarea */}
                <Input.TextArea
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                  placeholder="Enter reason for cancellation..."
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-400"
                  rows={2} // Makes textarea taller and nicer
                />

                {/* Buttons */}
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => setIsCancelVisible(false)}
                    className="bg-gray-500 hover:bg-gray-600 text-white px-2 py-1 rounded-lg transition"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCancel}
                    className="bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded-lg transition"
                  >
                    Confirm Cancel
                  </button>
                </div>
              </div>
            </>
          )}
          {/* Product information */}
          <div className="space-y-2">
            <div className="flex items-start gap-2">
              <Package className="h-4 w-4 mt-0.5 text-[#294d48]" />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-800 line-clamp-2">
                  {enquiry.chemical_name ||
                    enquiry.product ||
                    `${enquiry.brand || "Unknown"} product`}
                </p>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-8 gap-y-2">
                  {enquiry.cas_number && (
                    <p className="text-xs text-gray-500">
                      CAS: {enquiry.cas_number}
                    </p>
                  )}
                  {enquiry.application && (
                    <p className="text-xs text-gray-500">
                      Applications: {enquiry.application}
                    </p>
                  )}
                  {enquiry.category && (
                    <p className="text-xs text-gray-500">
                      Category/BU: {enquiry.category}
                    </p>
                  )}
                  {enquiry.procurement_volume && enquiry.procurement_unit && (
                    <p className="text-xs text-gray-500">
                      Procurement Value: {enquiry.procurement_volume}{" "}
                      {enquiry.procurement_unit}
                    </p>
                  )}
                  {enquiry.incoterms && (
                    <p className="text-xs text-gray-500">
                      Incoterms: {enquiry.incoterms}
                    </p>
                  )}
                  <p className="text-xs text-gray-500">
                    Quantity: {enquiry.quantity} {enquiry.quantity_unit}
                  </p>
                  {enquiry.target_price && (
                    <p className="text-xs text-gray-500">
                      Target Price: {enquiry.target_price}{" "}
                      {enquiry.target_price_currency}
                    </p>
                  )}
                  {enquiry.expected_procurement_volume &&
                    enquiry.expected_procurement_unit && (
                      <p className="text-xs text-gray-500">
                        Expected Revenue: {enquiry.expected_procurement_volume}{" "}
                        {enquiry.expected_procurement_unit}
                      </p>
                    )}
                </div>
              </div>
            </div>

            {/* Location information */}
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2 w-1/2">
                <Globe className="h-4 w-4 text-[#294d48]" />
                <span className="text-sm text-gray-700 truncate">
                  {enquiry.country || "Unknown location"} {enquiry.city}
                </span>
              </div>
            </div>

            {/* Additional details */}
            <div className="flex items-center justify-between text-xs text-gray-600">
              <div className="flex items-center gap-1">
                <Tag className="h-3.5 w-3.5 text-[#294d48]" />
                <span className="capitalize">{criticality} priority</span>
              </div>
            </div>
          </div>
          {/* Footer with update date and documents */}
          <div className="flex justify-between items-center pt-1 mt-auto">
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <Calendar className="h-3.5 w-3.5 text-[#294d48]" />
              <span>{lastUpdated}</span>
            </div>

            {hasDocuments && (
              <>
                <div
                  className="flex items-center gap-1 text-xs text-gray-500 cursor-pointer hover:text-[#294d48]"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsDocumentsOpen(true);
                  }}
                >
                  <Paperclip className="w-4 h-4 cursor-pointer hover:text-gray-700" />
                  <span>
                    {enquiry.enquiry_documents.length} doc
                    {enquiry.enquiry_documents.length !== 1 ? "s" : ""}
                  </span>
                </div>
                <Dialog
                  open={isDocumentsOpen}
                  onOpenChange={setIsDocumentsOpen}
                >
                  <DialogContent className="max-w-md">
                    <div className="flex justify-between items-center mb-4">
                      <DialogTitle>Enquiry Documents</DialogTitle>
                    </div>

                    <DialogDescription className="text-sm text-gray-500">
                      Click "View" to open or "Download" to save the file.
                    </DialogDescription>

                    <ScrollArea className="mt-4 max-h-[300px]">
                      {enquiry.enquiry_documents.map((doc) => (
                        <DocumentItem key={doc.id} doc={doc} />
                      ))}
                    </ScrollArea>
                  </DialogContent>
                </Dialog>
              </>
            )}
          </div>
          {onClick && (
            <div className="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
              <div className="h-6 w-6 rounded-full bg-[#294d48]/10 flex items-center justify-center hover:bg-[#294d48]/20">
                <Info className="h-3.5 w-3.5 text-[#294d48]" />
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default EnquiryCard;
