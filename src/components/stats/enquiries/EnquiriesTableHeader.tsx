
import {
  TableH<PERSON>,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface EnquiriesTableHeaderProps {
  type: "total" | "recent" | "drafts";
}

const EnquiriesTableHeader = ({ type }: EnquiriesTableHeaderProps) => {
  return (
    <TableHeader className="bg-gray-50/50 dark:bg-gray-800/50 backdrop-blur-sm">
      <TableRow>
        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 py-2 px-2 text-xs">Date</TableHead>
        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 py-2 px-2 text-xs">Enquiry ID</TableHead>
        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 py-2 px-2 text-xs">Customer</TableHead>
        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 py-2 px-2 text-xs">Product</TableHead>
        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 py-2 px-2 text-xs">Location</TableHead>
        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 py-2 px-2 text-xs">Qty</TableHead>
        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 py-2 px-2 text-xs">Target Quotation Date</TableHead>
        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 py-2 px-2 text-xs">Status</TableHead>
        {type !== 'drafts' && <TableHead className="font-semibold text-gray-700 dark:text-gray-300 py-2 px-2 text-xs">Actions</TableHead>}
      </TableRow>
    </TableHeader>
  );
};

export default EnquiriesTableHeader;
