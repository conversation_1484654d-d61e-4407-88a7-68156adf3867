
import { DialogHeader, DialogTitle } from "@/components/ui/dialog";
import EnquiriesSearch from "../enquiries/EnquiriesSearch";
import EnquiriesFilter, { FilterOptions } from "../enquiries/EnquiriesFilter";

interface EnquiriesModalHeaderProps {
  title: string;
  searchTerm: string;
  onSearchChange: (value: string) => void;
  appliedFilters: FilterOptions;
  onFilterChange: (filters: FilterOptions) => void;
  countries: string[];
  statuses: string[];
  sortDirection: "asc" | "desc";
  toggleSortDirection: () => void;
}

const EnquiriesModalHeader = ({ 
  title, 
  searchTerm, 
  onSearchChange, 
  appliedFilters, 
  onFilterChange, 
  countries, 
  statuses, 
  sortDirection, 
  toggleSortDirection 
}: EnquiriesModalHeaderProps) => {
  return (
    <DialogHeader className="space-y-4 pb-6 border-b border-gray-100">
      <DialogTitle className="text-2xl font-semibold text-[#294d48]">
        {title}
      </DialogTitle>
      <div className="flex flex-col space-y-4">
        <div className="flex justify-between items-center gap-4">
          <EnquiriesSearch
            searchTerm={searchTerm}
            onSearchChange={onSearchChange}
          />
          <button
            onClick={toggleSortDirection}
            className="px-3 py-1.5 text-sm font-medium text-[#294d48] bg-white hover:bg-[#E6EAE9] rounded-md border border-gray-100 transition-all duration-200 flex items-center gap-2"
          >
            Date {sortDirection === 'asc' ? '↑' : '↓'}
          </button>
        </div>
        
        <EnquiriesFilter
          appliedFilters={appliedFilters}
          onFilterChange={onFilterChange}
          countries={countries}
          statuses={statuses}
        />
      </div>
    </DialogHeader>
  );
};

export default EnquiriesModalHeader;
