import { useState, useMemo } from "react";
import {
  EnquiryType,
  SortDirection,
  useEnquiriesData,
} from "../enquiries/hooks/useEnquiriesData";
import { FilterOptions } from "../enquiries/EnquiriesFilter";

export type EnquiriesFilterType =
  | "total"
  | "open"
  | "closed"
  | "pending";

export const useFilteredEnquiries = (
  type: "total" | "recent",
  filter: EnquiriesFilterType = "total",
  sortDirection: SortDirection
) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [appliedFilters, setAppliedFilters] = useState<FilterOptions>({});

  const { data: enquiries, isLoading } = useEnquiriesData(type, sortDirection);

  // Extract unique countries and statuses for filter dropdowns
  const uniqueValues = useMemo(() => {
    if (!enquiries) return { countries: [], statuses: [] };

    const countries = [
      ...new Set(enquiries.map((e) => e.country).filter(Boolean)),
    ];
    const statuses = [
      ...new Set(enquiries.map((e) => e.current_status).filter(Boolean)),
    ];

    return {
      countries: countries.sort() as string[],
      statuses: statuses.sort() as string[],
    };
  }, [enquiries]);

  const filterEnquiriesByType = (enquiries: EnquiryType[] | undefined) => {
    if (!enquiries) return [];

    switch (filter) {
      case "open":
        return enquiries.filter((enquiry) => {
          const status = enquiry.current_status;
          return (
            status &&
            ![
              "po_raised",
              "cancelled",
              "regret",
              "sample_rejected",
              "quote_rejected",
            ].includes(status) &&
            !status.includes("reject")
          );
        });
      case "closed":
        return enquiries.filter((enquiry) => {
          const status = enquiry.current_status;
          return (
            status &&
            (status === "sample_requested" ||
              status === "sample_available" ||
              status === "sample_in_transit" ||
              status === "sample_delivered" ||
              status === "sample_redo")
          );
        });

      case "pending":
        return enquiries.filter(
          (enquiry) =>
            enquiry.current_status === "clarification_needed" ||
            enquiry.current_status === "pricing_quotation_generated" ||
            enquiry.current_status === "quote_accepted" ||
            enquiry.current_status === "sample_accepted" ||
            enquiry.current_status === "sample_delivered"
        );
      case "total":
      default:
        return enquiries;
    }
  };

  const filterEnquiriesBySearchTerm = (
    enquiries: EnquiryType[] | undefined
  ) => {
    if (!enquiries) return [];
    const searchLower = searchTerm.toLowerCase();
    return enquiries.filter((enquiry) => {
      // Handle ID search with improved logic
      let searchId = searchLower;
      // If search starts with "ENQ-", remove it
      if (searchId.startsWith("enq-")) {
        searchId = searchId.substring(4); // Remove "enq-" prefix
      }

      // Get the first 5 chars of the UUID for comparison
      const idFirstFiveChars = enquiry.id ? enquiry.id.toString().toLowerCase().substring(0, 5) : "";

      return enquiry.customer_full_name?.toLowerCase().includes(searchLower) ||
        enquiry.chemical_name?.toLowerCase().includes(searchLower) ||
        enquiry.brand?.toLowerCase().includes(searchLower) ||
        enquiry.product?.toLowerCase().includes(searchLower) ||
        enquiry.country?.toLowerCase().includes(searchLower) ||
        idFirstFiveChars.includes(searchId); // Search by first 5 chars of ID
    });
  };

  const filterEnquiriesByCustomFilters = (enquiries: EnquiryType[]) => {
    if (
      !enquiries ||
      !appliedFilters ||
      Object.keys(appliedFilters).length === 0
    )
      return enquiries;

    return enquiries.filter((enquiry) => {
      // Filter by country
      if (
        appliedFilters.country &&
        enquiry.country !== appliedFilters.country
      ) {
        return false;
      }

      // Filter by criticality/priority
      if (
        appliedFilters.criticality &&
        enquiry.confidence !== appliedFilters.criticality
      ) {
        return false;
      }

      // Filter by status
      if (
        appliedFilters.status &&
        enquiry.current_status !== appliedFilters.status
      ) {
        return false;
      }

      return true;
    });
  };

  // Custom sorting function to prioritize clarification_needed and then sort by date
  const applySorting = (data: EnquiryType[]) => {
    return [...data].sort((a, b) => {
      // First, prioritize clarification_needed status
      if (a.current_status === 'clarification_needed' && b.current_status !== 'clarification_needed') {
        return -1; // a comes first
      }
      if (a.current_status !== 'clarification_needed' && b.current_status === 'clarification_needed') {
        return 1; // b comes first
      }

      // Then sort by date (newest first by default)
      const dateA = new Date(a.created_at || '').getTime();
      const dateB = new Date(b.created_at || '').getTime();

      return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
    });
  };

  // Apply all filters in sequence
  const filteredByType = filterEnquiriesByType(enquiries as EnquiryType[]);
  const filteredBySearch = filterEnquiriesBySearchTerm(filteredByType);
  const filteredByCustom = filterEnquiriesByCustomFilters(filteredBySearch);

  // Apply the custom sorting to the filtered results
  const filteredEnquiries = applySorting(filteredByCustom);

  return {
    filteredEnquiries,
    searchTerm,
    setSearchTerm,
    appliedFilters,
    setAppliedFilters,
    uniqueValues,
    isLoading,
  };
};
