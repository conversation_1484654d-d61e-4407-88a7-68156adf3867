
import { useState } from "react";
import { Loader2 } from "lucide-react";
import { EnquiryType } from "../enquiries/hooks/useEnquiriesData";
import RegularEnquiriesView from "../enquiries/RegularEnquiriesView";

interface EnquiriesModalContentProps {
  isLoading: boolean;
  filteredEnquiries: EnquiryType[] | undefined;
  type: "total" | "recent";
}

const EnquiriesModalContent = ({
  isLoading,
  filteredEnquiries,
  type
}: EnquiriesModalContentProps) => {
  const [selectedEnquiry, setSelectedEnquiry] = useState<EnquiryType | null>(null);
  // We no longer need to fetch status history here as it's handled in the EnquiryLifecycleDialog

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 text-[#294d48] opacity-70 animate-spin" />
      </div>
    );
  }

  return (
    <div className="p-6">
      <RegularEnquiriesView
        filteredEnquiries={filteredEnquiries}
        type={type}
        selectedEnquiry={selectedEnquiry}
        setSelectedEnquiry={setSelectedEnquiry}
      />
      {/* Add extra bottom padding to ensure the last card is fully scrollable */}
      <div className="h-24"></div>
    </div>
  );
};

export default EnquiriesModalContent;
