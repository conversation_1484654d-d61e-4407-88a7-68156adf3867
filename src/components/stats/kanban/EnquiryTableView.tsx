import React, { useState, useEffect, useRef, useMemo } from "react";
import { EnquiryType } from "../enquiries/hooks/useEnquiriesData";
import { But<PERSON> } from "@/components/ui/button";
import {
  ArrowLeft,
  Filter,
  X,
  Calendar,
  Search,
  DollarSign,
} from "lucide-react";
import { formatDate } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { format } from "date-fns";
import { supabase } from "@/integrations/supabase/client";

interface EnquiryTableViewProps {
  stageName: string;
  enquiries: EnquiryType[];
  onBack: () => void;
  onEnquiryClick: (enquiry: EnquiryType) => void;
}

const CHUNK_SIZE = 100; // Number of items to process at once

const EnquiryTableView: React.FC<EnquiryTableViewProps> = ({
  stageName,
  enquiries,
  onBack,
  onEnquiryClick,
}) => {
  // Filter states
  const [showFilters, setShowFilters] = useState(false);
  const [filteredEnquiries, setFilteredEnquiries] = useState<EnquiryType[]>(enquiries);
  const [dateFilter, setDateFilter] = useState<Date | undefined>(undefined);
  const [customerFilter, setCustomerFilter] = useState("");
  const [chemicalFilter, setChemicalFilter] = useState("");
  const [criticalityFilter, setCriticalityFilter] = useState<string | undefined>(undefined);
  const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined);
  const [minValueFilter, setMinValueFilter] = useState<string>("");
  const [maxValueFilter, setMaxValueFilter] = useState<string>("");
  const [quoteData, setQuoteData] = useState<Record<string, { amount: number | null; currency: string | null }>>({});
  const [enquiryIdFilter, setEnquiryIdFilter] = useState("");
  const [isFiltering, setIsFiltering] = useState(false);
  const filterTimeout = useRef<number | null>(null);

  // Get unique values for select filters
  const uniqueStatuses = Array.from(
    new Set(enquiries.map((e) => e.current_status))
  );
  const criticalityOptions = ["High", "Medium", "Low"];

  // Define the order of statuses
  const statusOrder = [
    'enquiry_created',
    'enquiry_assigned',
    'clarification_needed',
    'regret',
    'pricing_quotation_generated',
    'quote_accepted',
    'quote_redo',
    'quote_rejected',
    'quote_revision_needed',
    'sample_requested',
    'sample_request_received',
    'in_transit_for_testing',
    'sample_ready',
    'in_transit_to_customer',
    'sample_delivered',
    'sample_accepted',
    'sample_redo',
    'sample_rejected',
    'po_raised',
    'cancelled',
    'unknown'
  ];

  // Create a map for status order
  const statusOrderMap = useMemo(() => {
    return statusOrder.reduce((acc, status, index) => {
      acc[status] = index;
      return acc;
    }, {} as Record<string, number>);
  }, []);

  // Sort enquiries by status order
  const sortedEnquiries = useMemo(() => {
    return [...enquiries].sort((a, b) => {
      const statusA = (a.current_status || 'unknown').toLowerCase().replace(/\s+/g, '_');
      const statusB = (b.current_status || 'unknown').toLowerCase().replace(/\s+/g, '_');
      return (statusOrderMap[statusA] ?? Infinity) - (statusOrderMap[statusB] ?? Infinity);
    });
  }, [enquiries, statusOrderMap]);

  // Standardize status format and get counts
  const statusCounts = useMemo(() => {
    const counts = sortedEnquiries.reduce((acc, enquiry) => {
      const status = enquiry.current_status || 'unknown';
      // Standardize status format
      const standardizedStatus = status.toLowerCase().replace(/\s+/g, '_');
      acc[standardizedStatus] = (acc[standardizedStatus] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Create ordered array of statuses with counts
    return statusOrder
      .map(status => ({
        value: status,
        label: `${status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} (${counts[status] || 0})`,
        count: counts[status] || 0
      }))
      .filter(status => status.count > 0);
  }, [sortedEnquiries]);

  // Fetch quote data for all enquiries
  useEffect(() => {
    const fetchQuoteData = async () => {
      try {
        const enquiryIds = enquiries.map(enquiry => enquiry.id);
        const chunks = chunkArray(enquiryIds, 500);
        let allQuoteGenerationData = [];

        for (const chunk of chunks) {
          const { data, error } = await supabase
            .from('quote_generation_details')
            .select('id, enquiry_id')
            .in('enquiry_id', chunk);

          if (error) {
            console.error('Error fetching quote generation details:', error);
            continue;
          }
          allQuoteGenerationData = allQuoteGenerationData.concat(data);
        }

        // Create a map to store quote generation IDs by enquiry ID
        const quoteGenerationMap: Record<string, string> = {};
        allQuoteGenerationData?.forEach(item => {
          quoteGenerationMap[item.enquiry_id] = item.id;
        });

        // Fetch quote options for all quote generation IDs
        const quoteGenerationIds = allQuoteGenerationData?.map(item => item.id) || [];

        if (quoteGenerationIds.length === 0) {
          return;
        }

        // Batch quoteGenerationIds into chunks of 500
        let allQuoteOptionsData = [];
        const quoteGenerationIdChunks = chunkArray(quoteGenerationIds, 500);
        for (const chunk of quoteGenerationIdChunks) {
          const { data, error } = await supabase
            .from('quote_generation_options' as any)
            .select('quote_generation_id, amount, currency')
            .in('quote_generation_id', chunk);
          if (error) {
            console.error('Error fetching quote options:', error);
            continue;
          }
          allQuoteOptionsData = allQuoteOptionsData.concat(data);
        }

        // Create a map to store amount and currency by enquiry ID
        const newQuoteData: Record<string, { amount: number | null; currency: string | null }> = {};

        // Use type assertion to bypass TypeScript errors
        const typedQuoteOptionsData = allQuoteOptionsData as any[] || [];

        // Group options by quote_generation_id to handle multiple options per generation
        const optionsByGenerationId: Record<string, any[]> = {};

        // Group all options by their quote_generation_id
        typedQuoteOptionsData.forEach(option => {
          const genId = option.quote_generation_id;
          if (!optionsByGenerationId[genId]) {
            optionsByGenerationId[genId] = [];
          }
          optionsByGenerationId[genId].push(option);
        });

        // For each quote generation ID, find the option with the minimum amount
        Object.keys(optionsByGenerationId).forEach(genId => {
          // Find the enquiry ID for this quote generation ID
          const enquiryId = Object.keys(quoteGenerationMap).find(
            key => quoteGenerationMap[key] === genId
          );

          if (enquiryId && optionsByGenerationId[genId].length > 0) {
            // Find the option with the minimum amount
            const options = optionsByGenerationId[genId];

            // Filter out options with null or undefined amounts
            const validOptions = options.filter(option =>
              option.amount !== null && option.amount !== undefined
            );

            if (validOptions.length > 0) {
              // Sort by amount in ascending order and take the first (minimum) option
              const minOption = validOptions.sort((a, b) => a.amount - b.amount)[0];

              newQuoteData[enquiryId] = {
                amount: minOption.amount,
                currency: minOption.currency
              };
            } else if (options.length > 0) {
              // If all options have null amounts, just take the first one
              const firstOption = options[0];
              newQuoteData[enquiryId] = {
                amount: firstOption.amount,
                currency: firstOption.currency
              };
            }
          }
        });

        setQuoteData(newQuoteData);
      } catch (error) {
        console.error('Error fetching quote data:', error);
      }
    };

    fetchQuoteData();
  }, [enquiries]);

  // Update the filtering logic to use sortedEnquiries
  useEffect(() => {
    if (filterTimeout.current) clearTimeout(filterTimeout.current);
    
    setIsFiltering(true);
    filterTimeout.current = window.setTimeout(() => {
      const processChunk = (chunk: EnquiryType[]) => {
        return chunk.filter(enquiry => {
          // Date filter
          if (dateFilter) {
            const filterDate = new Date(dateFilter);
            filterDate.setHours(0, 0, 0, 0);
            const enquiryDate = new Date(enquiry.created_at || "");
            enquiryDate.setHours(0, 0, 0, 0);
            if (enquiryDate.getTime() !== filterDate.getTime()) return false;
          }

          // Enquiry ID filter
          if (enquiryIdFilter) {
            const searchTerm = enquiryIdFilter.toLowerCase();
            const enquiryId = (enquiry.enquiry_id || "").toLowerCase();
            const idFirstFiveChars = enquiry.id
              ? enquiry.id.toString().toLowerCase().substring(0, 5)
              : "";
            if (!enquiryId.includes(searchTerm) && !idFirstFiveChars.includes(searchTerm)) return false;
          }

          // Customer filter
          if (customerFilter) {
            const searchTerm = customerFilter.toLowerCase();
            const customerName = (enquiry.customer_full_name || "").toLowerCase();
            if (!customerName.includes(searchTerm)) return false;
          }

          // Chemical filter
          if (chemicalFilter) {
            const searchTerm = chemicalFilter.toLowerCase();
            const chemicalName = (enquiry.chemical_name || enquiry.product || "").toLowerCase();
            if (!chemicalName.includes(searchTerm)) return false;
          }

          // Criticality filter
          if (criticalityFilter && criticalityFilter !== "all") {
            if ((enquiry.confidence || "low").toLowerCase() !== criticalityFilter.toLowerCase()) return false;
          }

          // Status filter
          if (statusFilter && statusFilter !== "all") {
            if (enquiry.current_status !== statusFilter) return false;
          }

          // Value range filter
          const price = Number(enquiry.target_price) || 0;
          if (minValueFilter && !isNaN(Number(minValueFilter))) {
            if (price < Number(minValueFilter)) return false;
          }
          if (maxValueFilter && !isNaN(Number(maxValueFilter))) {
            if (price > Number(maxValueFilter)) return false;
          }

          return true;
        });
      };

      // Process enquiries in chunks
      const chunks = chunkArray(sortedEnquiries, CHUNK_SIZE);
      let filtered: EnquiryType[] = [];
      
      // Process each chunk
      chunks.forEach(chunk => {
        const filteredChunk = processChunk(chunk);
        filtered = filtered.concat(filteredChunk);
      });

      setFilteredEnquiries(filtered);
      setIsFiltering(false);
    }, 200);

    return () => {
      if (filterTimeout.current) clearTimeout(filterTimeout.current);
    };
  }, [sortedEnquiries, dateFilter, enquiryIdFilter, customerFilter, chemicalFilter, criticalityFilter, statusFilter, minValueFilter, maxValueFilter]);

  // Reset all filters
  const resetFilters = () => {
    setDateFilter(undefined);
    setCustomerFilter("");
    setChemicalFilter("");
    setCriticalityFilter("all");
    setStatusFilter("all");
    setMinValueFilter("");
    setMaxValueFilter("");
    setEnquiryIdFilter("");
  };

  function chunkArray(array: any[], size: number) {
    const result = [];
    for (let i = 0; i < array.length; i += size) {
      result.push(array.slice(i, i + size));
    }
    return result;
  }

  return (
    <div>
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="p-4 flex justify-between items-center border-b border-gray-100">
          <div className="flex items-center gap-2">
            <h2 className="text-lg font-semibold text-[#294d48]">
              {stageName}
            </h2>
          </div>
          <div className="flex items-center gap-2">
            <div>
              <div className="flex flex-wrap gap-2 items-center">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs"
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <Filter className="h-3 w-3 mr-1" />
                  {showFilters ? "Hide Filters" : "Show Filters"}
                </Button>
                {showFilters && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs text-gray-500"
                    onClick={resetFilters}
                  >
                    <X className="h-3 w-3 mr-1" />
                    Reset
                  </Button>
                )}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Pipeline
            </Button>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            {showFilters && (
              <tr className="border-b border-gray-50">
                <th className="px-4 py-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-start text-left text-sm font-medium text-gray-600"
                      >
                        <Calendar className="h-3 w-3 mr-1" />
                        {dateFilter
                          ? format(dateFilter, "dd/MM/yyyy")
                          : "date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <CalendarComponent
                        mode="single"
                        selected={dateFilter}
                        onSelect={setDateFilter}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </th>
                <th className="px-4 py-2">
                  <div className="relative">
                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-500" />
                    <Input
                      placeholder="enquiry ID"
                      value={enquiryIdFilter}
                      onChange={(e) => setEnquiryIdFilter(e.target.value)}
                      className="pl-7 h-8 text-sm font-medium text-gray-600"
                    />
                  </div>
                </th>
                <th className="px-4 py-2">
                  <div className="relative">
                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-500" />
                    <Input
                      placeholder="customer"
                      value={customerFilter}
                      onChange={(e) => setCustomerFilter(e.target.value)}
                      className="pl-7 h-8 text-sm font-medium text-gray-600"
                    />
                  </div>
                </th>
                <th className="px-4 py-2">
                  <div className="relative">
                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-500" />
                    <Input
                      placeholder="chemical"
                      value={chemicalFilter}
                      onChange={(e) => setChemicalFilter(e.target.value)}
                      className="pl-7 h-8 text-sm font-medium text-gray-600"
                    />
                  </div>
                </th>
                <th className="px-4 py-2">
                  <Select
                    value={criticalityFilter}
                    onValueChange={setCriticalityFilter}
                  >
                    <SelectTrigger className="h-8 text-sm font-medium text-gray-600">
                      <SelectValue placeholder="criticality" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      {criticalityOptions.map((option) => (
                        <SelectItem
                          key={option.toLowerCase()}
                          value={option.toLowerCase()}
                        >
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </th>
                <th className="px-4 py-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="h-8 text-sm font-medium text-gray-600">
                      <SelectValue placeholder="status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      {statusCounts.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          {status.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </th>
                <th className="px-4 py-2">
                  <div className="flex items-center space-x-1">
                    <div className="relative flex-1">
                      <DollarSign className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-500" />
                      <Input
                        placeholder="Min"
                        type="number"
                        min="0"
                        value={minValueFilter}
                        onChange={(e) => setMinValueFilter(e.target.value)}
                        className="pl-7 h-8 text-sm font-medium text-gray-600"
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-600">-</span>
                    <div className="relative flex-1">
                      <DollarSign className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-500" />
                      <Input
                        placeholder="Max"
                        type="number"
                        min="0"
                        value={maxValueFilter}
                        onChange={(e) => setMaxValueFilter(e.target.value)}
                        className="pl-7 h-8 text-sm font-medium text-gray-600"
                      />
                    </div>
                  </div>
                </th>
              </tr>
            )}
            <tr className="border-b border-gray-100">
              <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">
                Date
              </th>
              <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">
                Enquiry ID
              </th>
              <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">
                Customer
              </th>
              <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">
                Chemical
              </th>
              <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">
                Criticality
              </th>
              <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">
                Status
              </th>
              <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">
                Expected Order Value
              </th>
            </tr>
          </thead>
          <tbody>
            {isFiltering ? (
              <tr>
                <td colSpan={7} className="px-4 py-8 text-center">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#294d48]"></div>
                    <span className="ml-2 text-gray-500">Filtering...</span>
                  </div>
                </td>
              </tr>
            ) : filteredEnquiries.length > 0 ? (
              filteredEnquiries.map((enquiry) => (
                <tr
                  key={`${enquiry.id}-${enquiry.current_status}-${enquiry.enquiry_id || ''}`}
                  className="border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                  onClick={() => onEnquiryClick(enquiry)}
                >
                  <td className="px-4 py-3 text-sm">{formatDate(enquiry.created_at || '')}</td>
                  <td className="px-4 py-3 text-sm">{enquiry.enquiry_id || 'N/A'}</td>
                  <td className="px-4 py-3 text-sm">{enquiry.customer_full_name || "N/A"}</td>
                  <td className="px-4 py-3 text-sm">{enquiry.chemical_name || enquiry.product || "N/A"}</td>
                  <td className="px-4 py-3 text-sm">
                    <Badge
                      className={`${
                        enquiry.confidence === "high"
                          ? "bg-green-100 text-green-800"
                          : enquiry.confidence === "medium"
                          ? "bg-amber-100 text-amber-800"
                          : "bg-blue-100 text-blue-800"
                      }`}
                    >
                      {enquiry.confidence
                        ? enquiry.confidence.charAt(0).toUpperCase() + enquiry.confidence.slice(1)
                        : "Low"}
                    </Badge>
                  </td>
                  <td className="px-4 py-3 text-sm">
                    {enquiry.current_status 
                      ? enquiry.current_status.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
                      : "N/A"}
                  </td>
                  <td className="px-4 py-3 text-sm">
                    {quoteData[enquiry.id]?.amount 
                      ? `${quoteData[enquiry.id]?.amount} ${quoteData[enquiry.id]?.currency || ''}` 
                      : 'N/A'}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={7}
                  className="px-4 py-8 text-center text-gray-500 italic"
                >
                  {enquiries.length > 0
                    ? "No enquiries match the current filters"
                    : "No enquiries found in this stage"}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default EnquiryTableView;
