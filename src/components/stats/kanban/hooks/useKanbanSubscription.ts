
import { useEffect } from 'react';
import { supabase } from "@/integrations/supabase/client";

export const useKanbanSubscription = (onUpdate: () => void) => {
  useEffect(() => {
    // Create a channel to listen for all changes to the enquiries table
    const enquiriesChannel = supabase
      .channel('kanban-enquiries-changes')
      .on(
        'postgres_changes',
        { 
          event: '*', 
          schema: 'public', 
          table: 'enquiries'
        },
        (payload) => {
          console.log('Enquiries table changed, refreshing kanban board...', payload);
          onUpdate();
        }
      )
      .on(
        'postgres_changes',
        { 
          event: '*', 
          schema: 'public', 
          table: 'enquiry_documents'
        },
        (payload) => {
          console.log('Documents changed, refreshing kanban board...', payload);
          onUpdate();  // Same onUpdate callback used here
        }
      )
      .subscribe();

    // Also listen for status history changes
    const statusHistoryChannel = supabase
      .channel('kanban-status-changes')
      .on(
        'postgres_changes',
        { 
          event: '*', 
          schema: 'public', 
          table: 'enquiry_status_history'
        },
        (payload) => {
          console.log('Status history changed, refreshing kanban board...', payload);
          onUpdate();  // Same onUpdate callback used here
        }
      )
      .subscribe();

    // Cleanup subscriptions
    return () => {
      supabase.removeChannel(enquiriesChannel);
      supabase.removeChannel(statusHistoryChannel);
    };
  }, [onUpdate]);
};
