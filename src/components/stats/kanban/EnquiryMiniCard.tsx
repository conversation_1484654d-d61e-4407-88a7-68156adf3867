
import React from "react";
import { EnquiryType } from "../enquiries/hooks/useEnquiriesData";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, User, Package, InfoIcon, ExternalLink,Tag } from "lucide-react";
import { format } from "date-fns";

interface EnquiryMiniCardProps {
  enquiry: EnquiryType;
  onClick?: () => void;
}

const EnquiryMiniCard: React.FC<EnquiryMiniCardProps> = ({ enquiry, onClick }) => {
  const hasDocuments = enquiry.enquiry_documents && enquiry.enquiry_documents.length > 0;


  // Format the last status change date
  const lastUpdated = enquiry.last_status_change
    ? format(new Date(enquiry.last_status_change), "dd/MM/yyyy")
    : enquiry.created_at
      ? format(new Date(enquiry.created_at), "dd/MM/yyyy")
      : "Unknown";

  // Color coding based on criticality for borders and backgrounds
  const getCriticalityColor = () => {
    // If the quote is expired, return a light pink/red background
    if (enquiry?.is_expired) {
      return 'border-red-300 hover:border-red-400 bg-red-100 hover:bg-red-100';
    }
    
    return ;
    const criticality = enquiry.confidence || "medium";

    switch(criticality) {
      case "high":
        return 'border-red-200 hover:border-red-300 bg-red-50/70 hover:bg-red-50';
      case "medium":
        return 'border-amber-200 hover:border-amber-300 bg-amber-50/70 hover:bg-amber-50';
      case "low":
        return 'border-green-200 hover:border-green-300 bg-green-50/70 hover:bg-green-50';
      default:
        return 'border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50/50';
    }
  };

  // Color coding for text based on criticality
  const getCriticalityTextColor = () => {
    const criticality = enquiry.confidence || "medium";

    switch(criticality) {
      case "high":
        return 'text-red-700';
      case "medium":
        return 'text-[rgb(245,200,10)]';
      case "low":
        return 'text-green-700';
      default:
        return 'text-gray-700';
    }
  };

  // Get criticality display text
  const getCriticalityText = () => {
    const criticality = enquiry.confidence || "medium";
    return criticality.charAt(0).toUpperCase() + criticality.slice(1);
  };

  return (
    <Card
      className={`p-3 backdrop-blur-sm shadow-sm hover:shadow transition-all duration-200 cursor-pointer rounded-md relative ${getCriticalityColor()}`}
      onClick={onClick}
    >
      <div className="space-y-2.5">
        <div className="flex flex-col justify-between items-start">
          <div className="flex items-center gap-1">
            <User className="h-3 w-3 text-[#294d48]" />
            <span className="font-medium text-sm text-gray-900 break-words mb-2">
              {enquiry.customer_full_name || "No Name"}
            </span>
          </div>
          <div className="flex items-center gap-1">
          <Tag className="h-3 w-3 text-[#294d48]" />
          <Badge
            variant="outline"
            className="text-12x font-normal px-1.5 py-0 h-5 bg-white/80"
          >
            {enquiry.enquiry_id}
          </Badge>
          </div>
        </div>

        <div className="space-y-1.5">
          <div className="flex items-center gap-1">
            <Package className="h-3 w-3 text-[#294d48]" />
            <span className="text-xs text-gray-700 truncate max-w-[220px]">
              {enquiry.chemical_name || enquiry.product || "No Product"}
            </span>
          </div>

          <div className="flex items-center justify-between flex-wrap">
            <div className="flex items-center gap-1">
              <InfoIcon className={`h-3 w-3 ${getCriticalityTextColor()}`} />
              <span className={`text-xs ${getCriticalityTextColor()}`}>
                Criticality: <span className={`font-medium`}>{getCriticalityText()}</span>
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-500 flex items-center">
                <Calendar className="h-3 w-3 mr-1 text-[#294d48]" />
                {lastUpdated}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Lifecycle indicator */}
      <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 hover:opacity-100 transition-opacity">
        <div className="h-5 w-5 rounded-full bg-[#294d48]/10 flex items-center justify-center hover:bg-[#294d48]/20">
          <ExternalLink className="h-3 w-3 text-[#294d48]" />
        </div>
      </div>
    </Card>
  );
};

export default EnquiryMiniCard;
