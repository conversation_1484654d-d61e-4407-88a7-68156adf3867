import React, { useState, useEffect } from "react";
import { EnquiryType } from "../enquiries/hooks/useEnquiriesData";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Filter, X, Calendar, Search, DollarSign } from "lucide-react";
import { formatDate } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { format } from "date-fns";

interface EnquiryTableViewProps {
  stageName: string;
  enquiries: EnquiryType[];
  onBack: () => void;
  onEnquiryClick: (enquiry: EnquiryType) => void;
}

const EnquiryTableView: React.FC<EnquiryTableViewProps> = ({
  stageName,
  enquiries,
  onBack,
  onEnquiryClick,
}) => {
  // Filter states
  const [showFilters, setShowFilters] = useState(false);
  const [filteredEnquiries, setFilteredEnquiries] = useState<EnquiryType[]>(enquiries);
  const [dateFilter, setDateFilter] = useState<Date | undefined>(undefined);
  const [customerFilter, setCustomerFilter] = useState("");
  const [chemicalFilter, setChemicalFilter] = useState("");
  const [criticalityFilter, setCriticalityFilter] = useState<string | undefined>(undefined);
  const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined);
  const [minValueFilter, setMinValueFilter] = useState<string>("");
  const [maxValueFilter, setMaxValueFilter] = useState<string>("");

  // Get unique values for select filters
  const uniqueStatuses = Array.from(new Set(enquiries.map(e => e.current_status)));
  const criticalityOptions = ["High", "Medium", "Low"];

  // Apply filters when any filter changes
  useEffect(() => {
    let result = [...enquiries];

    // Date filter
    if (dateFilter) {
      const filterDate = new Date(dateFilter);
      filterDate.setHours(0, 0, 0, 0);
      result = result.filter(enquiry => {
        const enquiryDate = new Date(enquiry.created_at || "");
        enquiryDate.setHours(0, 0, 0, 0);
        return enquiryDate.getTime() === filterDate.getTime();
      });
    }

    // Customer filter
    if (customerFilter) {
      result = result.filter(enquiry => 
        enquiry.customer_full_name?.toLowerCase().includes(customerFilter.toLowerCase())
      );
    }

    // Chemical filter
    if (chemicalFilter) {
      result = result.filter(enquiry => 
        (enquiry.chemical_name || enquiry.product || "")
          .toLowerCase()
          .includes(chemicalFilter.toLowerCase())
      );
    }

    // Criticality filter
    if (criticalityFilter && criticalityFilter !== "all") {
      result = result.filter(enquiry => 
        enquiry.confidence?.toLowerCase() === criticalityFilter.toLowerCase()
      );
    }

    // Status filter
    if (statusFilter && statusFilter !== "all") {
      result = result.filter(enquiry => enquiry.current_status === statusFilter);
    }

    // Value filter
    if (minValueFilter) {
      const minValue = parseFloat(minValueFilter);
      result = result.filter(enquiry => {
        const value = parseFloat(enquiry.target_price || "0");
        return value >= minValue;
      });
    }

    if (maxValueFilter) {
      const maxValue = parseFloat(maxValueFilter);
      result = result.filter(enquiry => {
        const value = parseFloat(enquiry.target_price || "0");
        return value <= maxValue;
      });
    }

    setFilteredEnquiries(result);
  }, [enquiries, dateFilter, customerFilter, chemicalFilter, criticalityFilter, statusFilter, minValueFilter, maxValueFilter]);

  // Reset all filters
  const resetFilters = () => {
    setDateFilter(undefined);
    setCustomerFilter("");
    setChemicalFilter("");
    setCriticalityFilter("all");
    setStatusFilter("all");
    setMinValueFilter("");
    setMaxValueFilter("");
  };
  
  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
      <div className="p-4 flex justify-between items-center border-b border-gray-100">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold text-[#294d48]">{stageName}</h2>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onBack}
          className="text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Pipeline
        </Button>
      </div>

      <div className="p-4 border-b border-gray-100">
        <div className="flex flex-wrap gap-2 items-center">
          <Button
            variant="outline"
            size="sm"
            className="text-xs"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-3 w-3 mr-1" />
            {showFilters ? "Hide Filters" : "Show Filters"}
          </Button>

          {showFilters && (
            <Button
              variant="ghost"
              size="sm"
              className="text-xs text-gray-500"
              onClick={resetFilters}
            >
              <X className="h-3 w-3 mr-1" />
              Reset
            </Button>
          )}

          {/* Active filter indicators */}
          {dateFilter && (
            <Badge variant="outline" className="text-xs bg-gray-100 gap-1">
              Date: {format(dateFilter, "dd/MM/yyyy")}
              <X className="h-3 w-3 cursor-pointer" onClick={() => setDateFilter(undefined)} />
            </Badge>
          )}

          {customerFilter && (
            <Badge variant="outline" className="text-xs bg-gray-100 gap-1">
              Customer: {customerFilter}
              <X className="h-3 w-3 cursor-pointer" onClick={() => setCustomerFilter("")} />
            </Badge>
          )}

          {chemicalFilter && (
            <Badge variant="outline" className="text-xs bg-gray-100 gap-1">
              Chemical: {chemicalFilter}
              <X className="h-3 w-3 cursor-pointer" onClick={() => setChemicalFilter("")} />
            </Badge>
          )}

          {criticalityFilter && criticalityFilter !== "all" && (
            <Badge variant="outline" className="text-xs bg-gray-100 gap-1">
              Criticality: {criticalityFilter.charAt(0).toUpperCase() + criticalityFilter.slice(1)}
              <X className="h-3 w-3 cursor-pointer" onClick={() => setCriticalityFilter("all")} />
            </Badge>
          )}

          {statusFilter && statusFilter !== "all" && (
            <Badge variant="outline" className="text-xs bg-gray-100 gap-1">
              Status: {statusFilter.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}
              <X className="h-3 w-3 cursor-pointer" onClick={() => setStatusFilter("all")} />
            </Badge>
          )}

          {(minValueFilter || maxValueFilter) && (
            <Badge variant="outline" className="text-xs bg-gray-100 gap-1">
              Value: {minValueFilter ? `$${minValueFilter}` : "$0"} - {maxValueFilter ? `$${maxValueFilter}` : "∞"}
              <X className="h-3 w-3 cursor-pointer" onClick={() => { setMinValueFilter(""); setMaxValueFilter(""); }} />
            </Badge>
          )}
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            {showFilters && (
              <tr className="border-b border-gray-50">
                <th className="px-4 py-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" size="sm" className="w-full justify-start text-left font-normal text-xs">
                        <Calendar className="h-3 w-3 mr-1" />
                        {dateFilter ? format(dateFilter, "dd/MM/yyyy") : "Filter date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <CalendarComponent
                        mode="single"
                        selected={dateFilter}
                        onSelect={setDateFilter}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </th>
                <th className="px-4 py-2">
                  <div className="relative">
                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-500" />
                    <Input
                      placeholder="Filter customer"
                      value={customerFilter}
                      onChange={(e) => setCustomerFilter(e.target.value)}
                      className="pl-7 h-8 text-xs"
                    />
                  </div>
                </th>
                <th className="px-4 py-2">
                  <div className="relative">
                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-500" />
                    <Input
                      placeholder="Filter chemical"
                      value={chemicalFilter}
                      onChange={(e) => setChemicalFilter(e.target.value)}
                      className="pl-7 h-8 text-xs"
                    />
                  </div>
                </th>
                <th className="px-4 py-2">
                  <Select value={criticalityFilter} onValueChange={setCriticalityFilter}>
                    <SelectTrigger className="h-8 text-xs">
                      <SelectValue placeholder="Filter criticality" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      {criticalityOptions.map(option => (
                        <SelectItem key={option.toLowerCase()} value={option.toLowerCase()}>{option}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </th>
                <th className="px-4 py-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="h-8 text-xs">
                      <SelectValue placeholder="Filter status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      {uniqueStatuses.map(status => (
                        <SelectItem key={status} value={status}>
                          {status.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </th>
                <th className="px-4 py-2">
                  <div className="flex items-center space-x-1">
                    <div className="relative flex-1">
                      <DollarSign className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-500" />
                      <Input
                        placeholder="Min"
                        type="number"
min="0"
                        value={minValueFilter}
                        onChange={(e) => setMinValueFilter(e.target.value)}
                        className="pl-7 h-8 text-xs"
                      />
                    </div>
                    <span className="text-xs">-</span>
                    <div className="relative flex-1">
                      <DollarSign className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-500" />
                      <Input
                        placeholder="Max"
                        type="number"
min="0"
                        value={maxValueFilter}
                        onChange={(e) => setMaxValueFilter(e.target.value)}
                        className="pl-7 h-8 text-xs"
                      />
                    </div>
                  </div>
                </th>
              </tr>
            )}
            <tr className="border-b border-gray-100">
              <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">
                Date
              </th>
              <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">
                Customer
              </th>
              <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">
                Chemical
              </th>
              <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">
                Criticality
              </th>
              <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">
                Status
              </th>
              <th className="px-4 py-2 text-left text-sm font-medium text-gray-600">
                Expected Order Value
              </th>
            </tr>
          </thead>
          <tbody>
            {filteredEnquiries.length > 0 ? (
              filteredEnquiries.map((enquiry) => (
                <tr
                  key={enquiry.id}
                  className="border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                  onClick={() => onEnquiryClick(enquiry)}
                >
                  <td className="px-4 py-3 text-sm">
                    {formatDate(enquiry.created_at)}
                  </td>
                  <td className="px-4 py-3 text-sm">
                    {enquiry.customer_full_name || "N/A"}
                  </td>
                  <td className="px-4 py-3 text-sm">
                    {enquiry.chemical_name || enquiry.product || "N/A"}
                  </td>
                  <td className="px-4 py-3 text-sm">
                    <Badge
                      className={`${
                        enquiry.confidence === "high"
                          ? "bg-green-100 text-green-800"
                          : enquiry.confidence === "medium"
                          ? "bg-amber-100 text-amber-800"
                          : "bg-blue-100 text-blue-800"
                      }`}
                    >
                      {enquiry.confidence
                        ? enquiry.confidence.charAt(0).toUpperCase() +
                          enquiry.confidence.slice(1)
                        : "Low"}
                    </Badge>
                  </td>
                  <td className="px-4 py-3 text-sm">
                    {enquiry.current_status
                      .replace(/_/g, " ")
                      .replace(/\b\w/g, (l) => l.toUpperCase())}
                  </td>
                  <td className="px-4 py-3 text-sm">
                    ${enquiry.target_price || "0"}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={6}
                  className="px-4 py-8 text-center text-gray-500 italic"
                >
                  {enquiries.length > 0 ? "No enquiries match the current filters" : "No enquiries found in this stage"}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      
      {selectedEnquiry && (
        <EnquiryLifecycleDialog
          enquiry={selectedEnquiry}
          isOpen={!!selectedEnquiry}
          onClose={handleCloseDialog}
        />
      )}
    </div>
  );
};

export default EnquiryTableView;
