
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useState, useEffect } from "react";
import { SortDirection } from "./enquiries/hooks/useEnquiriesData";
import EnquiriesModalHeader from "./modals/EnquiriesModalHeader";
import EnquiriesModalContent from "./modals/EnquiriesModalContent";
import { useFilteredEnquiries, EnquiriesFilterType } from "./modals/useFilteredEnquiries";

interface EnquiriesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: "total" | "recent";
  title: string;
  filter?: EnquiriesFilterType;
}

const EnquiriesModal = ({ open, onOpenChange, type, title, filter = "total" }: EnquiriesModalProps) => {
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  
  const { 
    filteredEnquiries, 
    searchTerm, 
    setSearchTerm, 
    appliedFilters, 
    setAppliedFilters, 
    uniqueValues, 
    isLoading 
  } = useFilteredEnquiries(type, filter, sortDirection);

  // Reset when modal is closed
  useEffect(() => {
    if (!open) {
      setSearchTerm("");
      setAppliedFilters({});
    }
  }, [open]);

  const toggleSortDirection = () => {
    setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
  };

  return (
    <Dialog 
      open={open} 
      onOpenChange={onOpenChange}
    >
      <DialogContent 
        className="max-w-7xl max-h-[90vh] overflow-hidden bg-white border-2 border-[#E6EAE9] rounded-lg shadow-sm flex flex-col"
        onInteractOutside={(e) => {
          // Prevent closing if clicking inside a nested dialog
          const target = e.target as HTMLElement;
          if (target.closest('[role="dialog"]') && 
              target.closest('[role="dialog"]') !== e.currentTarget) {
            e.preventDefault();
            e.stopPropagation(); // Stop event propagation
          }
        }}
      >
        <EnquiriesModalHeader
          title={title}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          appliedFilters={appliedFilters}
          onFilterChange={setAppliedFilters}
          countries={uniqueValues.countries}
          statuses={uniqueValues.statuses}
          sortDirection={sortDirection}
          toggleSortDirection={toggleSortDirection}
        />
        
        <ScrollArea className="flex-1">
          <EnquiriesModalContent
            isLoading={isLoading}
            filteredEnquiries={filteredEnquiries}
            type={type}
          />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default EnquiriesModal;
