
import { ReactNode } from 'react';
import { LucideIcon } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: number;
  icon: LucideIcon;
  onClick: () => void;
}

const StatsCard = ({ title, value, icon: Icon, onClick }: StatsCardProps) => {
  return (
    <div
      onClick={onClick}
      className="cursor-pointer group bg-white p-4 rounded-lg transition-all duration-300 border border-gray-200 hover:border-[#294d48]"
    >
      <div className="flex items-center justify-between mb-2">
        <p className="text-sm text-gray-600 font-medium">{title}</p>
        <Icon className="w-4 h-4 text-[#294d48] opacity-70 group-hover:opacity-100 transition-opacity" />
      </div>
      <p className="text-2xl font-bold text-[#294d48]">
        {value}
      </p>
    </div>
  );
};

export default StatsCard;
