import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ChevronDown, ChevronRight, ArrowUp, ArrowDown, Minus } from 'lucide-react';
import { useSalesProgress } from '@/hooks/use-sales-progress';
import { Skeleton } from '@/components/ui/skeleton';

export const SalesProgress = ({ 
  selectedCustomer = 'All',
  selectedAccountOwner = 'All',
  customerId = 'All'
}: { 
  selectedCustomer?: string;
  selectedAccountOwner?: string;
  customerId?: string;
}) => {
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString());
  const [selectedQuarter, setSelectedQuarter] = useState('All');
  const [compareMode, setCompareMode] = useState(false);
  const [compareQuarter1, setCompareQuarter1] = useState('Q1');
  const [compareQuarter2, setCompareQuarter2] = useState('Q2');
  
  const { quarterlyData, loading, error } = useSalesProgress({
    customerId: customerId,
    accountOwner: selectedAccountOwner
  });
  
  // Get unique years from the data
  const years = Array.from(new Set(quarterlyData.map(data => data.year)));
  
  // Get unique quarters
  const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];
  
  // Filter data based on selected customer, year, and quarter
  let filteredData = quarterlyData.filter(item => 
    (selectedCustomer === 'All' || item.customerId === customerId || item.customerId === 'All'));
  
  // Apply year filter
  if (selectedYear !== 'All') {
    filteredData = filteredData.filter(item => item.year === selectedYear);
  }
  
  // Apply quarter filter
  if (selectedQuarter !== 'All' && !compareMode) {
    filteredData = filteredData.filter(item => item.quarterNumber === selectedQuarter);
  }
  
  // Apply comparison filter if in compare mode
  if (compareMode) {
    filteredData = filteredData.filter(item => 
      item.quarterNumber === compareQuarter1 || item.quarterNumber === compareQuarter2);
  }

  // Get quarter data for comparison
  const quarter1Data = compareMode ? 
    filteredData.find(item => item.quarterNumber === compareQuarter1 && (selectedYear === 'All' || item.year === selectedYear)) : null;
  
  const quarter2Data = compareMode ? 
    filteredData.find(item => item.quarterNumber === compareQuarter2 && (selectedYear === 'All' || item.year === selectedYear)) : null;

  const toggleRow = (quarter: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [quarter]: !prev[quarter]
    }));
  };

  // Function to calculate variance between quarters
  const calculateVariance = (q1Value: number, q2Value: number) => {
    const diff = q2Value - q1Value;
    const percentage = q1Value !== 0 ? Math.round((diff / q1Value) * 100) : Infinity;
    
    return {
      diff,
      percentage,
      direction: diff > 0 ? 'increase' : diff < 0 ? 'decrease' : 'same'
    };
  };
  
  if (loading) {
    return (
      <Card className='mb-4'>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle>Overall Sales Progress</CardTitle>
          <Skeleton className="h-10 w-[300px]" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    );
  }
  
  if (error) {
    return (
      <Card className="mb-4">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle>Overall Sales Progress</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-4 bg-red-50 text-red-800 rounded-md">
            Error loading sales data. Please try again later.
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className="mb-4">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle>Overall Sales Progress</CardTitle>
        <div className="flex gap-2">
          <Select value={selectedYear} onValueChange={setSelectedYear}>
            <SelectTrigger className="w-24">
              <SelectValue placeholder="Year" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All Years</SelectItem>
              {years.map(year => (
                <SelectItem key={year} value={year}>{year}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {!compareMode ? (
            <>
              <Select value={selectedQuarter} onValueChange={setSelectedQuarter}>
                <SelectTrigger className="w-24">
                  <SelectValue placeholder="Quarter" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All Quarters</SelectItem>
                  {quarters.map(quarter => (
                    <SelectItem key={quarter} value={quarter}>{quarter}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {/* <Select value={timePeriod} onValueChange={setTimePeriod}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Time Period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="currentQuarter">Current Quarter</SelectItem>
                  <SelectItem value="previousQuarter">Previous Quarter</SelectItem>
                  <SelectItem value="ytd">YTD</SelectItem>
                </SelectContent>
              </Select> */}
            </>
          ) : (
            <>
              <Select value={compareQuarter1} onValueChange={setCompareQuarter1}>
                <SelectTrigger className="w-24">
                  <SelectValue placeholder="Quarter 1" />
                </SelectTrigger>
                <SelectContent>
                  {quarters.map(quarter => (
                    <SelectItem key={quarter} value={quarter}>{quarter}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={compareQuarter2} onValueChange={setCompareQuarter2}>
                <SelectTrigger className="w-24">
                  <SelectValue placeholder="Quarter 2" />
                </SelectTrigger>
                <SelectContent>
                  {quarters.map(quarter => (
                    <SelectItem key={quarter} value={quarter}>{quarter}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </>
          )}
          
          <div className="flex items-center gap-2">
            <input 
              type="checkbox" 
              id="compareMode" 
              checked={compareMode} 
              onChange={() => {
                setCompareMode(!compareMode);
                if (!compareMode) {
                  setSelectedQuarter('All');
                }
              }}
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <label htmlFor="compareMode" className="text-xs font-medium text-gray-700">
              Compare Quarters
            </label>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {compareMode && quarter1Data && quarter2Data ? (
          <div className="mb-6">
            <div className="mb-4 bg-muted/20 p-4 rounded-md">
              <h3 className="text-md font-semibold mb-2">Quarter Comparison: {quarter1Data.quarterNumber} vs {quarter2Data.quarterNumber} ({quarter1Data.year !== quarter2Data.year ? `${quarter1Data.year} vs ${quarter2Data.year}` : quarter1Data.year})</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Metric</TableHead>
                    <TableHead>{quarter1Data.quarterNumber} {quarter1Data.year}</TableHead>
                    <TableHead>{quarter2Data.quarterNumber} {quarter2Data.year}</TableHead>
                    <TableHead>Variance</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">Total Sales</TableCell>
                    <TableCell>${quarter1Data.actual?.toLocaleString()}</TableCell>
                    <TableCell>${quarter2Data.actual?.toLocaleString()}</TableCell>
                    <TableCell>
                      {(() => {
                        const variance = calculateVariance(quarter1Data.actual, quarter2Data.actual);
                        return (
                          <div className="flex items-center">
                            {variance.direction === 'increase' ? (
                              <ArrowUp className="h-4 w-4 text-green-500 mr-1" />
                            ) : variance.direction === 'decrease' ? (
                              <ArrowDown className="h-4 w-4 text-red-500 mr-1" />
                            ) : (
                              <Minus className="h-4 w-4 text-gray-500 mr-1" />
                            )}
                            ${Math.abs(variance.diff)?.toLocaleString()}
                          </div>
                        );
                      })()}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Target Achievement</TableCell>
                    <TableCell>
                      {(() => {
                       
                        const achievement1 = Math.round((quarter1Data.actual / quarter1Data.target) * 100)
                        return (
                          <div className="flex items-center">
                            {achievement1 === Infinity ? 'NA' : achievement1 + '%'}
                           
                          </div>
                        ); 
                      })()}
                    </TableCell>
                    <TableCell>{Math.round((quarter2Data.actual / quarter2Data.target) * 100)}%</TableCell>
                    <TableCell>
                      {(() => {
                        const variance = calculateVariance(
                          Math.round((quarter1Data.actual / quarter1Data.target) * 100),
                          Math.round((quarter2Data.actual / quarter2Data.target) * 100)
                        );
                        return (
                          <div className="flex items-center">
                            {variance.direction === 'increase' ? (
                              <ArrowUp className="h-4 w-4 text-green-500 mr-1" />
                            ) : variance.direction === 'decrease' ? (
                              <ArrowDown className="h-4 w-4 text-red-500 mr-1" />
                            ) : (
                              <Minus className="h-4 w-4 text-gray-500 mr-1" />
                            )}
                            {variance.percentage > 0 ? '+' : ''}{variance.percentage}%
                          </div>
                        );
                      })()}
                    </TableCell>
                  </TableRow>
                  
                  {/* Chemical level comparison */}
                  {quarter1Data.chemicals.map((chemical1, index) => {
                    const chemical2 = quarter2Data.chemicals.find(c => c?.name === chemical1?.name);
                    return (
                      <TableRow key={chemical1.name}>
                        <TableCell className="font-medium pl-6">{chemical1?.name}</TableCell>
                        <TableCell>{chemical1?.value?.toLocaleString()}</TableCell>
                        <TableCell>{chemical2?.value?.toLocaleString()}</TableCell>
                        <TableCell>
                          {(() => {
                            const variance = calculateVariance(chemical1?.value, chemical2?.value);
                            return (
                              <div className="flex items-center">
                                {variance.direction === 'increase' ? (
                                  <ArrowUp className="h-4 w-4 text-green-500 mr-1" />
                                ) : variance.direction === 'decrease' ? (
                                  <ArrowDown className="h-4 w-4 text-red-500 mr-1" />
                                ) : (
                                  <Minus className="h-4 w-4 text-gray-500 mr-1" />
                                )}
                                {variance.diff?.toLocaleString()}
                              </div>
                            );
                          })()}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </div>
        ) : null}
        
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[5%]"></TableHead>
              <TableHead className='font-semibold w-[15%]'>Quarter</TableHead>
              <TableHead className='font-semibold w-[20%]'>Revenue (E) ($)</TableHead>
              <TableHead className='font-semibold w-[20%]'>Target ($)</TableHead>
              <TableHead className='font-semibold w-[20%]'>Variance</TableHead>
              <TableHead className='font-semibold w-[20%]'>Performance</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.length > 0 ? (
              filteredData.map((row) => (
                <>
                  <TableRow 
                    key={row.quarter} 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => toggleRow(row.quarter)}
                  >
                    <TableCell>
                      {expandedRows[row.quarter] ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </TableCell>
                    <TableCell className="font-medium">{row.quarter}</TableCell>
                    <TableCell>{row.actual?.toLocaleString()}</TableCell>
                    <TableCell>{row.target?.toLocaleString()}</TableCell>
                    <TableCell>
                      {row.variance?.toLocaleString()}
                    </TableCell>
                    <TableCell>
                      <div className={`px-2 py-0.5 rounded-full text-xs inline-block text-center ${
                        row.actual >= row.target 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {row.performance}%
                      </div>
                    </TableCell>
                  </TableRow>
                  
                  {expandedRows[row.quarter] && (
                    <TableRow key={`${row.quarter}-details`} className="bg-muted/20">
                      <TableCell colSpan={6}>
                        <Table className="border-0">
                          <TableHeader>
                            <TableRow>
                              <TableHead className="text-xs w-[25%]">Chemical</TableHead>
                              <TableHead className="text-xs w-[15%]">Actual Sales ($)</TableHead>
                              <TableHead className="text-xs w-[15%]">Target ($)</TableHead>
                              <TableHead className="text-xs w-[15%]">Variance</TableHead>
                              <TableHead className="text-xs w-[15%]">% of Total</TableHead>
                              <TableHead className="text-xs w-[15%]">Performance</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {row.chemicals.map((chemical) => (
                              <TableRow key={`${row.quarter}-${chemical.name}`}>
                                <TableCell className="text-xs pl-6 w-[25%]">{chemical.name}</TableCell>
                                <TableCell className="text-xs w-[15%]">{chemical.value?.toLocaleString()}</TableCell>
                                <TableCell className="text-xs w-[15%]">{chemical.target?.toLocaleString()}</TableCell>
                                <TableCell className="text-xs w-[15%]">
                                  {(chemical.value - chemical.target)?.toLocaleString()}
                                </TableCell>
                                <TableCell className="text-xs w-[15%]">
                                  {chemical.achievement_percent ? `${chemical.achievement_percent}%` : '-'}
                                </TableCell>
                                <TableCell className="text-xs w-[15%]">
                                  {chemical.performance ? (
                                    <div className={`px-2 py-0.5 rounded-full text-xs inline-block text-center ${
                                      chemical.value >= chemical.target 
                                        ? 'bg-green-100 text-green-800' 
                                        : 'bg-red-100 text-red-800'
                                    }`}>
                                      {chemical.performance}%
                                    </div>
                                  ) : '-'}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableCell>
                    </TableRow>
                  )}
                </>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-4">
                  No data available for the selected filters
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};
