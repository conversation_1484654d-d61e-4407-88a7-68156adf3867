import React, { useState, useEffect } from 'react';
import { FileText, MessageSquare, ChevronDown, ChevronUp, TrendingUp, TrendingDown, Minus, FileSearch, FilePlus, Package, FileX, ChevronLeft, ChevronRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { cn } from '@/lib/utils';
import { useProjectsData } from '@/hooks/use-projects-data';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

type ProjectItem = {
  id: number;
  chemicalName: string;
  customerId: string;
  [key: string]: any; // Allow for other properties to vary between project types
};

type EnquirySample = ProjectItem & {
  potentialOrderValue: string;
  expectedQuarterlyProcurement: string;
  requestDate: string;
  conversionProbability: string;
  accountOwner: string;
};

type Order = ProjectItem & {
  orderValue: string;
  expectedQuarterlyProcurement: string;
  margin: string;
  orderDate: string;
  repeatFrequency: string;
  poNumber: string;
  chemicalName: string;
  salesOrderValue: string;
  deliveryDate: string;
  paymentTerms: string;
  accountOwner: string;
};

interface ChemicalInPO {
  chemical_name: string;
  margin: string;
  deliveryDate: string;
  paymentTerms: string;
  expectedQuarterlyProcurement: string;
  salesOrderValue: string;
  current_status: string;
  eta: string;
  customerName?: string;
  accountOwner?: string;
}

interface GroupedPO {
  poNumber: string;
  orderValue: string;
  orderDate: string;
  repeatFrequency: string;
  customerName: string;
  accountOwner: string;
  chemicals: ChemicalInPO[];
}

type ProjectStatProps = {
  title: string;
  count: number;
  // subtitle: string;
  change: {
    value: string;
    type: 'increase' | 'decrease' | 'neutral';
  };
  icon?: 'file' | 'message';
  data: ProjectItem[];
  selectedCustomer: string;
  selectedAccountOwner?: string;
  isExpanded: boolean | null;
  setExpanded: (title: string | null) => void;
};

const ProjectStat = ({ 
  title, 
  count, 
  change, 
  icon = 'file', 
  data, 
  selectedCustomer,
  selectedAccountOwner,
  isExpanded,
  setExpanded
}: ProjectStatProps) => {
  const filteredData = selectedCustomer === 'All' 
    ? data 
    : data.filter(item => item.customerId === selectedCustomer);
  
  return (
    <Collapsible
      open={isExpanded}
      onOpenChange={() => setExpanded(isExpanded ? null : title)}
      className="flex-1"
    >
      <CollapsibleTrigger className="w-full text-left">
        <Card className={`flex-1 cursor-pointer hover:bg-muted/20 transition-colors ${isExpanded ? 'bg-muted/30' : ''}`}>
          <CardContent className="pt-4 pb-3 px-4">
            <div className="flex justify-between items-center">
              <div className="flex items-baseline gap-3">
                <h3 className="text-sm font-bold">{title}</h3>
                <span className="text-2xl font-bold">{count}</span>
              </div>
              <div className="flex items-center gap-2">
                {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </div>
            </div>
          </CardContent>
        </Card>
      </CollapsibleTrigger>
    </Collapsible>
  );
};

type SortField = 
  | "potentialOrderValue" 
  | "expectedQuarterlyProcurement" 
  | "requestDate" 
  | "closureDate" 
  | "criticality"
  | "orderValue"
  | "orderDate"
  | "deliveryDate"
  | "eta"
  | "customerName"
  | "accountOwner";

type SortDirection = "asc" | "desc";

export const OpenProjects = ({ 
  selectedCustomer = 'All', 
  selectedAccountOwner = 'All' 
}: { 
  selectedCustomer?: string;
  selectedAccountOwner?: string;
}) => {
  const [expandedSection, setExpandedSection] = useState<string | null>(null);
  const { projectsData, loading, error } = useProjectsData();
  const [currentPage, setCurrentPage] = useState(1);
  const [sortField, setSortField] = useState<SortField>("requestDate");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const [samplesSortField, setSamplesSortField] = useState<SortField>("requestDate");
  const [samplesSortDirection, setSamplesSortDirection] = useState<SortDirection>("desc");
  const [enquiryFilter, setEnquiryFilter] = useState<'all' | 'open' | 'closed'>('open');
  const [poFilter, setPoFilter] = useState<'all' | 'open' | 'delivered'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const itemsPerPage = 10;
  const userRole = localStorage.getItem('userRole');
  const isAdmin = userRole === 'admin';

  // Add sorting functions
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return <ChevronDown size={16} className="opacity-30" />;
    return sortDirection === "asc" ? <ChevronUp size={16} /> : <ChevronDown size={16} />;
  };

  // Add sorting functions for samples
  const handleSamplesSort = (field: SortField) => {
    if (samplesSortField === field) {
      setSamplesSortDirection(samplesSortDirection === "asc" ? "desc" : "asc");
    } else {
      setSamplesSortField(field);
      setSamplesSortDirection("asc");
    }
  };

  const getSamplesSortIcon = (field: SortField) => {
    if (samplesSortField !== field) return <ChevronDown size={16} className="opacity-30" />;
    return samplesSortDirection === "asc" ? <ChevronUp size={16} /> : <ChevronDown size={16} />;
  };

  // Update the getSortedEnquiries function
  const getSortedEnquiries = (enquiries: EnquirySample[]) => {
    return [...enquiries].sort((a, b) => {
      const direction = sortDirection === "asc" ? 1 : -1;
      switch (sortField) {
        case "potentialOrderValue":
          return direction * (parseFloat(a.potentialOrderValue) - parseFloat(b.potentialOrderValue));
        case "expectedQuarterlyProcurement":
          return direction * (parseFloat(a.expectedQuarterlyProcurement) - parseFloat(b.expectedQuarterlyProcurement));
        case "requestDate":
          return direction * (new Date(a.requestDate).getTime() - new Date(b.requestDate).getTime());
        case "closureDate":
          return direction * (new Date(a.closureDate).getTime() - new Date(b.closureDate).getTime());
        case "criticality":
          return direction * (a.confidenceLevel - b.confidenceLevel);
        case "customerName":
          return direction * (a.customerName?.localeCompare(b.customerName || '') || 0);
        case "accountOwner":
          return direction * (a.accountOwner?.localeCompare(b.accountOwner || '') || 0);
        default:
          return 0;
      }
    });
  };

  // Update the getSortedSamples function
  const getSortedSamples = (samples: EnquirySample[]) => {
    return [...samples].sort((a, b) => {
      const direction = samplesSortDirection === "asc" ? 1 : -1;
      switch (samplesSortField) {
        case "potentialOrderValue":
          return direction * (parseFloat(a.potentialOrderValue) - parseFloat(b.potentialOrderValue));
        case "expectedQuarterlyProcurement":
          return direction * (parseFloat(a.expectedQuarterlyProcurement) - parseFloat(b.expectedQuarterlyProcurement));
        case "requestDate":
          return direction * (new Date(a.requestDate).getTime() - new Date(b.requestDate).getTime());
        case "closureDate":
          return direction * (new Date(a.closureDate).getTime() - new Date(b.closureDate).getTime());
        case "criticality":
          return direction * (a.confidenceLevel - b.confidenceLevel);
        case "customerName":
          return direction * (a.customerName?.localeCompare(b.customerName || '') || 0);
        case "accountOwner":
          return direction * (a.accountOwner?.localeCompare(b.accountOwner || '') || 0);
        default:
          return 0;
      }
    });
  };

  // Get data for the expanded section
  const getExpandedData = () => {
    if (!expandedSection || loading) return [];
    let data = [];
    switch (expandedSection) {
      case 'Active Enquiries':
        data = projectsData.enquiries.filter(item => {
          if (enquiryFilter === 'all') {
            return true; // Show all enquiries
          } else if (enquiryFilter === 'open') {
            return !item.quotationFeedback;
          } else {
            return item.quotationFeedback;
          }
        });
        // Apply sorting for enquiries
        data = getSortedEnquiries(data);
        break;
      case 'Open Samples':
        data = projectsData.samples.filter(item => {
          if (enquiryFilter === 'all') {
            return true; // Show all samples
          } else if (enquiryFilter === 'open') {
            return !item.sampleFeedback;
          } else {
            return item.sampleFeedback;
          }
        });
        // Apply sorting for samples
        data = getSortedSamples(data);
        break;
      case 'Purchase Orders':
        data = projectsData.orders.filter(po => {
          const isDelivered = po.deliveryDate && po.deliveryDate !== 'N/A';
          if (poFilter === 'all') return true;
          if (poFilter === 'open') return !isDelivered;
          if (poFilter === 'delivered') return isDelivered;
          return true;
        });
        // Apply sorting for POs
        data = [...data].sort((a, b) => {
          const direction = sortDirection === "asc" ? 1 : -1;
          switch (sortField) {
            case "orderValue":
              return direction * (parseFloat(a.orderValue) - parseFloat(b.orderValue));
            case "orderDate":
              return direction * (new Date(a.orderDate).getTime() - new Date(b.orderDate).getTime());
            case "deliveryDate":
              return direction * (new Date(a.deliveryDate).getTime() - new Date(b.deliveryDate).getTime());
            case "eta":
              return direction * (new Date(a.eta).getTime() - new Date(b.eta).getTime());
            default:
              return 0;
          }
        });
        break;
      default:
        data = [];
    }
    // Apply search filter
    if (searchTerm.trim() !== '') {
      const lowerSearch = searchTerm.toLowerCase();
      if (expandedSection === 'Active Enquiries' || expandedSection === 'Open Samples') {
        data = data.filter(item =>
          (item.chemicalName && item.chemicalName.toLowerCase().includes(lowerSearch)) ||
          (item.id && String(item.id).toLowerCase().includes(lowerSearch)) ||
          (item.current_status && item.current_status.toLowerCase().includes(lowerSearch))
        );
      } else if (expandedSection === 'Purchase Orders') {
        data = data.filter(po =>
          (po.poNumber && String(po.poNumber).toLowerCase().includes(lowerSearch)) ||
          (po.chemicalName && po.chemicalName.toLowerCase().includes(lowerSearch)) ||
          (po.orderValue && String(po.orderValue).toLowerCase().includes(lowerSearch)) ||
          (po.deliveryDate && String(po.deliveryDate).toLowerCase().includes(lowerSearch))
        );
      }
    }
    return data;
  };
  
  // Filter data based on selected customer and account owner
  const filteredData = getExpandedData().filter(item => {
    const customerMatch = selectedCustomer === 'All' || item.customerId === selectedCustomer;
    const ownerMatch = selectedAccountOwner === 'All' || item.accountOwner === selectedAccountOwner;
    return customerMatch && ownerMatch;
  });

  // Calculate totals for display in ProjectStat
  const getActiveEnquiriesCount = () => {
    return projectsData.enquiries.filter(item => {
      const matchesCustomer = selectedCustomer === 'All' || item.customerId === selectedCustomer;
      const matchesOwner = selectedAccountOwner === 'All' || item.accountOwner === selectedAccountOwner;
      return !item.quotation_feedback && matchesCustomer && matchesOwner;
    }).length;
  };

  const getOpenSamplesCount = () => {
    return projectsData.samples.filter(item => {
      const matchesCustomer = selectedCustomer === 'All' || item.customerId === selectedCustomer;
      const matchesOwner = selectedAccountOwner === 'All' || item.accountOwner === selectedAccountOwner;
      return !item.sample_feedback && matchesCustomer && matchesOwner;
    }).length;
  };

  const getPurchaseOrdersCount = () => {
    return projectsData.orders.filter(item => {
      const matchesCustomer = selectedCustomer === 'All' || item.customerId === selectedCustomer;
      const matchesOwner = selectedAccountOwner === 'All' || item.accountOwner === selectedAccountOwner;
      return matchesCustomer && matchesOwner;
    }).length;
  };

  // Calculate pagination
  const totalItems = filteredData.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  
  // Ensure current page is valid
  useEffect(() => {
    if (currentPage > totalPages) {
      setCurrentPage(totalPages || 1);
    }
  }, [currentPage, totalPages]);

  // Get current page data with strict item count
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
  const currentPageData = filteredData.slice(startIndex, endIndex);

  // Render columns based on the expanded section type
  const renderTableColumns = () => {
    // Search input for all expanded sections
    const showSearch = expandedSection === 'Active Enquiries' || expandedSection === 'Open Samples' || expandedSection === 'Purchase Orders';
    return (
      <>
        {(expandedSection === 'Active Enquiries' || expandedSection === 'Open Samples') && (
          <TableRow>
            <TableCell colSpan={isAdmin ? 11 : 10}>
              <div className="flex justify-between items-center mb-4">
                <div className="flex gap-2">
                  <Button
                    variant={enquiryFilter === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setEnquiryFilter('all')}
                  >
                    All {expandedSection === 'Active Enquiries' ? 'Enquiries' : 'Samples'}
                  </Button>
                  <Button
                    variant={enquiryFilter === 'open' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setEnquiryFilter('open')}
                  >
                    Open {expandedSection === 'Active Enquiries' ? 'Enquiries' : 'Samples'}
                  </Button>
                  <Button
                    variant={enquiryFilter === 'closed' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setEnquiryFilter('closed')}
                  >
                    Closed {expandedSection === 'Active Enquiries' ? 'Enquiries' : 'Samples'}
                  </Button>
                </div>
                {showSearch && (
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    placeholder="Search..."
                    className="border rounded px-2 py-2 text-base w-72 ml-auto"
                    style={{ marginLeft: 'auto' }}
                  />
                )}
              </div>
            </TableCell>
          </TableRow>
        )}
        {expandedSection === 'Purchase Orders' && (
          <TableRow>
            <TableCell colSpan={isAdmin ? 12 : 11}>
              <div className="flex justify-between items-center mb-4">
                <div className="flex gap-2">
                  <Button
                    variant={poFilter === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPoFilter('all')}
                  >
                    All POs
                  </Button>
                  <Button
                    variant={poFilter === 'open' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPoFilter('open')}
                  >
                    Open POs
                  </Button>
                  <Button
                    variant={poFilter === 'delivered' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPoFilter('delivered')}
                  >
                    Delivered POs
                  </Button>
                </div>
                {showSearch && (
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    placeholder="Search..."
                    className="border rounded px-2 py-2 text-base w-72 ml-auto"
                  />
                )}
              </div>
            </TableCell>
          </TableRow>
        )}
        {expandedSection === 'Active Enquiries' && (
          <>
            <TableHead className='font-semibold'>ENQ Number</TableHead>
            <TableHead className='font-semibold'>Chemical Name</TableHead>
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSort("customerName")}
            >
              <div className="flex items-center gap-1">
                Customer Name
                {getSortIcon("customerName")}
              </div>
            </TableHead>
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSort("accountOwner")}
            >
              <div className="flex items-center gap-1">
                Account Owner
                {getSortIcon("accountOwner")}
              </div>
            </TableHead>
            <TableHead className='font-semibold'>Current State</TableHead>
            <TableHead className='font-semibold'>Criticality</TableHead>
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSort("potentialOrderValue")}
            >
              <div className="flex items-center gap-1">
                Potential Order Value
                {getSortIcon("potentialOrderValue")}
              </div>
            </TableHead>
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSort("expectedQuarterlyProcurement")}
            >
              <div className="flex items-center gap-1">
                Exp. Quarterly Procurement
                {getSortIcon("expectedQuarterlyProcurement")}
              </div>
            </TableHead>
            {isAdmin && <TableHead className='font-semibold'>Margin</TableHead>}
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSort("requestDate")}
            >
              <div className="flex items-center gap-1">
                Request Date
                {getSortIcon("requestDate")}
              </div>
            </TableHead>
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSort("closureDate")}
            >
              <div className="flex items-center gap-1">
                Closure Date
                {getSortIcon("closureDate")}
              </div>
            </TableHead>
            <TableHead className='font-semibold'>Quotation Feedback</TableHead>
          </>
        )}
        {expandedSection === 'Open Samples' && (
          <>
            <TableHead className='font-semibold'>ENQ Number</TableHead>
            <TableHead className='font-semibold'>Chemical Name</TableHead>
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSamplesSort("customerName")}
            >
              <div className="flex items-center gap-1">
                Customer Name
                {getSamplesSortIcon("customerName")}
              </div>
            </TableHead>
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSamplesSort("accountOwner")}
            >
              <div className="flex items-center gap-1">
                Account Owner
                {getSamplesSortIcon("accountOwner")}
              </div>
            </TableHead>
            <TableHead className='font-semibold'>Current State</TableHead>
            <TableHead className='font-semibold'>Criticality</TableHead>
            <TableHead className='font-semibold'>ETA</TableHead>
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSamplesSort("potentialOrderValue")}
            >
              <div className="flex items-center gap-1">
                Potential Order Value
                {getSamplesSortIcon("potentialOrderValue")}
              </div>
            </TableHead>
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSamplesSort("expectedQuarterlyProcurement")}
            >
              <div className="flex items-center gap-1">
                Exp. Quarterly Procurement
                {getSamplesSortIcon("expectedQuarterlyProcurement")}
              </div>
            </TableHead>
            {isAdmin && <TableHead className='font-semibold'>Margin</TableHead>}
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSamplesSort("requestDate")}
            >
              <div className="flex items-center gap-1">
                Request Date
                {getSamplesSortIcon("requestDate")}
              </div>
            </TableHead>
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSamplesSort("closureDate")}
            >
              <div className="flex items-center gap-1">
                Closure Date
                {getSamplesSortIcon("closureDate")}
              </div>
            </TableHead>
            <TableHead className='font-semibold'>Sample Feedback</TableHead>
          </>
        )}
        {expandedSection === 'Purchase Orders' && (
          <>
            <TableHead className='font-semibold'>PO Number</TableHead>
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSort("customerName")}
            >
              <div className="flex items-center gap-1">
                Customer Name
                {getSortIcon("customerName")}
              </div>
            </TableHead>
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSort("accountOwner")}
            >
              <div className="flex items-center gap-1">
                Account Owner
                {getSortIcon("accountOwner")}
              </div>
            </TableHead>
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSort("orderValue")}
            >
              <div className="flex items-center gap-1">
                Order Value ($)
                {getSortIcon("orderValue")}
              </div>
            </TableHead>
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSort("orderDate")}
            >
              <div className="flex items-center gap-1">
                Order Date
                {getSortIcon("orderDate")}
              </div>
            </TableHead>
            <TableHead className='font-semibold'>Frequency days</TableHead>
            <TableHead className='font-semibold'>Chemical Name</TableHead>
            <TableHead className='font-semibold'>Current State</TableHead>
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSort("eta")}
            >
              <div className="flex items-center gap-1">
                ETA
                {getSortIcon("eta")}
              </div>
            </TableHead>
            <TableHead className='font-semibold'>Sales Order Value ($)</TableHead>
            <TableHead className='font-semibold'>Expected Quarterly Procurement</TableHead>
            {isAdmin && <TableHead className='font-semibold'>Margin</TableHead>}
            <TableHead 
              className='font-semibold cursor-pointer'
              onClick={() => handleSort("deliveryDate")}
            >
              <div className="flex items-center gap-1">
                Delivery Date
                {getSortIcon("deliveryDate")}
              </div>
            </TableHead>
            <TableHead className='font-semibold'>Payment Terms</TableHead>
          </>
        )}
      </>
    );
  };
  
  // Render table rows based on the expanded section type
  const renderTableContent = () => {
    if (loading) {
      return (
        <TableRow>
          <TableCell colSpan={isAdmin ? 9 : 8} className="text-center py-4">
            <div className="flex justify-center items-center space-x-2">
              <Skeleton className="h-4 w-4 rounded-full" />
              <Skeleton className="h-4 w-4 rounded-full" />
              <Skeleton className="h-4 w-4 rounded-full" />
            </div>
          </TableCell>
        </TableRow>
      );
    }
    
    if (!currentPageData || currentPageData.length === 0) {
      return (
        <TableRow>
          <TableCell colSpan={isAdmin ? 9 : 8} className="text-center py-4 text-muted-foreground">
            No current items
          </TableCell>
        </TableRow>
      );
    }
    
    if (expandedSection === 'Active Enquiries') {
      return currentPageData.map((item, index) => {
        const enquiry = item as EnquirySample;
        return (
          <TableRow key={`enquiry-${enquiry.id}-${startIndex + index}`}>
            <TableCell>
              <Button
                variant="link"
                className="p-0 h-auto font-normal text-[#294d48] hover:text-[#1a2f2c] hover:underline"
                onClick={() => window.open(`crm/?enquiryId=${enquiry.id}&activeTab=salesstack`, '_blank')}
              >
                {enquiry.id}
              </Button>
            </TableCell>
            <TableCell>{enquiry.chemicalName}</TableCell>
            <TableCell>{enquiry.customerName || '-'}</TableCell>
            <TableCell>{enquiry.accountOwner || '-'}</TableCell>
            <TableCell>
              {enquiry.current_status
                ? enquiry.current_status.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
                : "N/A"}
            </TableCell>
            <TableCell>
              <Badge
                className={`${
                  enquiry.conversionProbability?.toLowerCase() === "high"
                    ? "bg-red-100 text-red-800"
                    : enquiry.conversionProbability?.toLowerCase() === "medium"
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-green-100 text-green-800"
                }`}
              >
                {enquiry.conversionProbability
                  ? enquiry.conversionProbability.charAt(0).toUpperCase() +
                    enquiry.conversionProbability.slice(1)
                  : "Low"}
              </Badge>
            </TableCell>
            <TableCell>{enquiry.potentialOrderValue}</TableCell>
            <TableCell>{enquiry.expectedQuarterlyProcurement}</TableCell>
            {isAdmin && <TableCell>{enquiry.margin || '-'}</TableCell>}
            <TableCell>{enquiry.requestDate}</TableCell>
            <TableCell>{enquiry.closureDate || '-'}</TableCell>
            <TableCell>{enquiry.quotationFeedback || '-'}</TableCell>
          </TableRow>
        );
      });
    } else if (expandedSection === 'Open Samples') {
      return currentPageData.map((item, index) => {
        const sample = item as EnquirySample;
        return (
          <TableRow key={`sample-${sample.id}-${startIndex + index}`}>
            <TableCell>
              <Button
                variant="link"
                className="p-0 h-auto font-normal text-[#294d48] hover:text-[#1a2f2c] hover:underline"
                onClick={() => window.open(`crm/?enquiryId=${sample.id}&sampleId=${sample.id}&activeTab=salesstack`, '_blank')}
              >
                {sample.id}
              </Button>
            </TableCell>
            <TableCell>{sample.chemicalName}</TableCell>
            <TableCell>{sample.customerName || '-'}</TableCell>
            <TableCell>{sample.accountOwner || '-'}</TableCell>
            <TableCell>
              {sample.current_status
                ? sample.current_status.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
                : "N/A"}
            </TableCell>
            <TableCell>
              <Badge
                className={`${
                  sample.conversionProbability?.toLowerCase() === "high"
                    ? "bg-red-100 text-red-800"
                    : sample.conversionProbability?.toLowerCase() === "medium"
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-green-100 text-green-800"
                }`}
              >
                {sample.conversionProbability
                  ? sample.conversionProbability.charAt(0).toUpperCase() +
                    sample.conversionProbability.slice(1)
                  : "Low"}
              </Badge>
            </TableCell>
            <TableCell>
              {sample.eta || "-"}
              {sample.tracking_url && (
                <Button
                  variant="link"
                  className="p-0 h-auto font-normal text-[#294d48] hover:text-[#1a2f2c] hover:underline ml-1"
                  onClick={() => window.open(sample.tracking_url, '_blank')}
                >
                  (link)
                </Button>
              )}
            </TableCell>
            <TableCell>{sample.potentialOrderValue}</TableCell>
            <TableCell>{sample.expectedQuarterlyProcurement}</TableCell>
            {isAdmin && <TableCell>{sample.margin || '-'}</TableCell>}
            <TableCell>{sample.requestDate}</TableCell>
            <TableCell>{sample.closureDate || '-'}</TableCell>
            <TableCell>{sample.sampleFeedback || '-'}</TableCell>
          </TableRow>
        );
      });
    } else if (expandedSection === 'Purchase Orders') {
      // Group purchase orders by PO number
      const groupedPOs = currentPageData
        .filter(item => 'poNumber' in item)
        .reduce<Record<string, GroupedPO>>((acc, po) => {
          if (!acc[po.poNumber]) {
            acc[po.poNumber] = {
              poNumber: po.poNumber,
              orderValue: po.orderValue,
              orderDate: po.orderDate,
              repeatFrequency: po.repeatFrequency,
              customerName: po.customerName || '-',
              accountOwner: po.accountOwner || '-',
              chemicals: []
            };
          }
          acc[po.poNumber].chemicals.push({
            chemical_name: po.chemicalName,
            margin: po.margin,
            deliveryDate: po.deliveryDate,
            paymentTerms: po.paymentTerms,
            expectedQuarterlyProcurement: po.expectedQuarterlyProcurement,
            salesOrderValue: po.salesOrderValue,
            current_status: "-",
            eta: "N/A"
          });
          return acc;
        }, {});

      return Object.entries(groupedPOs).map(([poNumber, po]) => (
        <React.Fragment key={poNumber}>
          {po.chemicals.map((chem, index) => (
            <TableRow key={`${poNumber}-${index}`}>
              {index === 0 && (
                <>
                  <TableCell rowSpan={po.chemicals.length}>
                    <Button
                      variant="link"
                      className="p-0 h-auto font-normal text-[#294d48] hover:text-[#1a2f2c] hover:underline"
                    >
                      {po.poNumber}
                    </Button>
                  </TableCell>
                  <TableCell rowSpan={po.chemicals.length}>{po.customerName}</TableCell>
                  <TableCell rowSpan={po.chemicals.length}>{po.accountOwner}</TableCell>
                  <TableCell rowSpan={po.chemicals.length}>{po.orderValue}</TableCell>
                  <TableCell rowSpan={po.chemicals.length}>{po.orderDate}</TableCell>
                  <TableCell rowSpan={po.chemicals.length}>{po.repeatFrequency}</TableCell>
                </>
              )}
              <TableCell>{chem.chemical_name}</TableCell>
              <TableCell>{chem.current_status}</TableCell>
              <TableCell>{chem.eta}</TableCell>
              <TableCell>{chem.salesOrderValue}</TableCell>
              <TableCell>{chem.expectedQuarterlyProcurement}</TableCell>
              {isAdmin && <TableCell>{chem.margin}</TableCell>}
              <TableCell>{chem.deliveryDate}</TableCell>
              <TableCell>{chem.paymentTerms}</TableCell>
            </TableRow>
          ))}
        </React.Fragment>
      ));
    }
    
    return null;
  };
  
  return (
    <div className="space-y-4 mb-4">
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="flex-1">
              <CardContent className="pt-4 pb-3 px-4">
                <Skeleton className="h-8 w-full mb-2" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <ProjectStat
            title="Active Enquiries"
            count={getActiveEnquiriesCount()}
            change={{ value: "Same as last month", type: "neutral" }}
            icon="message"
            data={projectsData.enquiries.filter(item => !item.quotation_feedback)}
            selectedCustomer={selectedCustomer}
            selectedAccountOwner={selectedAccountOwner}
            isExpanded={expandedSection === 'Active Enquiries'}
            setExpanded={setExpandedSection}
          />
          
          <ProjectStat
            title="Open Samples"
            count={getOpenSamplesCount()}
            change={{ value: "8% from last month", type: "increase" }}
            data={projectsData.samples.filter(item => !item.sample_feedback)}
            selectedCustomer={selectedCustomer}
            selectedAccountOwner={selectedAccountOwner}
            isExpanded={expandedSection === 'Open Samples'}
            setExpanded={setExpandedSection}
          />
          
          <ProjectStat
            title="Purchase Orders"
            count={getPurchaseOrdersCount()}
            change={{ value: "12% from last month", type: "increase" }}
            data={projectsData.orders}
            selectedCustomer={selectedCustomer}
            selectedAccountOwner={selectedAccountOwner}
            isExpanded={expandedSection === 'Purchase Orders'}
            setExpanded={setExpandedSection}
          />
        </div>
      )}
      
      {expandedSection && (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {renderTableColumns()}
            </TableHeader>
            <TableBody>
              {renderTableContent()}
            </TableBody>
          </Table>
          {totalPages > 1 && (
            <div className="flex items-center justify-between px-4 py-3 border-t">
              <div className="text-sm text-muted-foreground">
                Showing {startIndex + 1} to {endIndex} of {totalItems} items
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(1)}
                  disabled={currentPage === 1}
                >
                  First
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="text-sm text-muted-foreground">
                  Page {currentPage} of {totalPages}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(totalPages)}
                  disabled={currentPage === totalPages}
                >
                  Last
                </Button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
