import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Check, Calendar, Users, X, Phone, Video, ChevronUp, ChevronDown, Clock, Edit, Save, ChevronLeft, ChevronRight, History, Eye, Plus } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useCustomerActivities, CustomerActivity } from '@/hooks/use-customer-activities';
import { Tabs, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

const timeFilterOptions = [
  { value: 'last7days', label: 'Last 7 Days' },
  { value: 'currentWeek', label: 'Current Week' },
  { value: 'lastWeek', label: 'Last Week' },
  { value: 'currentMonth', label: 'Current Month' },
  { value: 'lastMonth', label: 'Last Month' }
];

const ActivityTypeIcon = ({ type }: { type: string }) => {
  switch (type) {
    case 'In-person Meeting':
      return <Users className="h-4 w-4" />;
    case 'Virtual Meeting':
      return <Video className="h-4 w-4" />;
    case 'Call':
      return <Phone className="h-4 w-4" />;
    case 'Sample Request':
      return <Calendar className="h-4 w-4" />;
    case 'Enquiry':
      return <Calendar className="h-4 w-4" />;
    case 'Follow-up':
      return <Clock className="h-4 w-4" />;
    default:
      return <Calendar className="h-4 w-4" />;
  }
};

interface ChemicalInPO {
  chemical_name: string;
  margin: string;
  deliveryDate: string;
  paymentTerms: string;
  expectedQuarterlyProcurement: string;
  salesOrderValue: string;
}

interface GroupedPO {
  poNumber: string;
  orderValue: string;
  orderDate: string;
  repeatFrequency: string;
  chemicals: ChemicalInPO[];
}

// Map activity types to readable formats
const activityTypeMap: Record<string, string> = {
  'enquiry_created': 'Enquiry Created',
  'enquiry_updated': 'Enquiry Updated',
  'enquiry_closed': 'Enquiry Closed',
  'sample_created': 'Sample Created',
  'sample_updated': 'Sample Updated',
  'sample_closed': 'Sample Closed',
  'po_created': 'Purchase Order Created',
  'po_updated': 'Purchase Order Updated',
  'po_closed': 'Purchase Order Closed',
  'meeting_created': 'Meeting Created',
  'meeting_updated': 'Meeting Updated',
  'meeting_cancelled': 'Meeting Cancelled',
  'meeting_completed': 'Meeting Completed',
  'remark_added': 'Remark Added',
  'remark_updated': 'Remark Updated',
  'status_changed': 'Status Changed',
  'target_updated': 'Target Updated',
  'customer_created': 'Customer Created',
  'customer_updated': 'Customer Updated'
};

export const RecentActivity = ({ 
  selectedCustomer = 'All',
  selectedAccountOwner = 'All'
}: { 
  selectedCustomer?: string;
  selectedAccountOwner?: string;
}) => {
  const [timeFilter, setTimeFilter] = useState('last7days');
  const [editingRemarks, setEditingRemarks] = useState<string | null>(null);
  const [remarksEditForm, setRemarksEditForm] = useState<{ current: string; previous: string }>({
    current: '',
    previous: ''
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [noActivityPage, setNoActivityPage] = useState(1);
  const [activeTab, setActiveTab] = useState('recent');
  const activitiesPerPage = 10;
  const noActivityCustomersPerPage = 10;
  const navigate = useNavigate();
  
  // Use our custom hook to fetch and manage activities
  const { 
    activities, 
    customersWithoutActivity,
    loading, 
    error, 
    updateActivityRemarks,
    refreshActivities
  } = useCustomerActivities(selectedCustomer, selectedAccountOwner, timeFilter);
  
  // Group activities by customer and type
  const groupedActivities = activities.reduce((acc, activity) => {
    if (!acc[activity.customerId]) {
      acc[activity.customerId] = {
        customerName: activity.customerName,
        customerId: activity.customerId,
        activities: [],
        purchaseOrders: {} as Record<string, GroupedPO>,
        reviewRemarksCurrent: activity.reviewRemarksCurrent,
        reviewRemarksPrevious: activity.reviewRemarksPrevious,
        remarks_history: activity.remarks_history || []
      };
    }

    // If it's a purchase order, group it by PO number
    if (activity.type === 'Purchase Order') {
      const poData = JSON.parse(activity.description);
      if (!acc[activity.customerId].purchaseOrders[poData.po_number]) {
        acc[activity.customerId].purchaseOrders[poData.po_number] = {
          poNumber: poData.po_number,
          orderValue: poData.order_value,
          orderDate: poData.order_date,
          repeatFrequency: poData.repeat_frequency,
          chemicals: []
        };
      }
      acc[activity.customerId].purchaseOrders[poData.po_number].chemicals.push({
        chemical_name: poData.chemical_name,
        margin: poData.margin,
        deliveryDate: poData.delivery_date,
        paymentTerms: poData.payment_terms,
        expectedQuarterlyProcurement: poData.expected_quarterly_procurement,
        salesOrderValue: poData.sales_order_value
      });
    } else {
      acc[activity.customerId].activities.push(activity);
    }
    return acc;
  }, {} as Record<string, {
    customerName: string;
    customerId: string;
    activities: typeof activities;
    purchaseOrders: Record<string, GroupedPO>;
    reviewRemarksCurrent: string;
    reviewRemarksPrevious: string;
    remarks_history: Array<{
      remarks: string;
      created_at: string;
    }>;
  }>);

  // Convert grouped activities to array without sorting
  const groupedActivitiesArray = Object.values(groupedActivities);

  // Calculate pagination based on activities
  const getPaginatedActivities = () => {
    let currentActivityCount = 0;
    let currentPageCustomers: typeof groupedActivitiesArray = [];
    let allPages: typeof groupedActivitiesArray[] = [];
    let currentPage: typeof groupedActivitiesArray = [];

    groupedActivitiesArray.forEach(customerGroup => {
      const customerActivityCount = customerGroup.activities.length + Object.keys(customerGroup.purchaseOrders).length;
      
      // If adding this customer would exceed the limit and we already have some customers
      if (currentActivityCount + customerActivityCount > activitiesPerPage && currentPage.length > 0) {
        // Save current page and start a new one
        allPages.push(currentPage);
        currentPage = [customerGroup];
        currentActivityCount = customerActivityCount;
      } else {
        // Add customer to current page
        currentPage.push(customerGroup);
        currentActivityCount += customerActivityCount;
      }
    });

    // Add the last page if it has any customers
    if (currentPage.length > 0) {
      allPages.push(currentPage);
    }

    return allPages;
  };

  const allPages = getPaginatedActivities();
  const totalPages = allPages.length;
  const currentPageCustomers = allPages[currentPage - 1] || [];

  // Calculate total activities for display
  const totalActivities = activities.length;

  // Calculate pagination for no activity customers
  const totalNoActivityCustomers = customersWithoutActivity.length;
  const totalNoActivityPages = Math.ceil(totalNoActivityCustomers / noActivityCustomersPerPage);
  
  // Ensure no activity page is valid
  React.useEffect(() => {
    if (noActivityPage > totalNoActivityPages) {
      setNoActivityPage(totalNoActivityPages || 1);
    }
  }, [noActivityPage, totalNoActivityPages]);

  // Get current page data for no activity customers
  const noActivityStartIndex = (noActivityPage - 1) * noActivityCustomersPerPage;
  const noActivityEndIndex = Math.min(noActivityStartIndex + noActivityCustomersPerPage, totalNoActivityCustomers);
  const currentPageNoActivity = customersWithoutActivity.slice(noActivityStartIndex, noActivityEndIndex);

  const handleEditRemarks = (customerId: string, currentRemarks: string) => {
    setEditingRemarks(customerId);
    setRemarksEditForm({
      current: currentRemarks,
      previous: currentRemarks
    });
  };
  
  const handleSaveRemarks = async (customerId: string) => {
    const success = await updateActivityRemarks(
      customerId, 
      remarksEditForm.current,
      remarksEditForm.previous
    );
    
    if (success) {
      setEditingRemarks(null);
      await refreshActivities(); // Refresh the activities data
    }
  };
  
  const handleCancelEditRemarks = () => {
    setEditingRemarks(null);
  };
  
  const handleViewDetails = (activity: CustomerActivity) => {
    if(activity.type === "meetings") {
      navigate("/crm", { 
        state: { 
          activeTab: "meetings",
          selectedCustomer: activity.customerId
        }
      });
      return;
    }
    if (activity.enquiryId) {
      window.open(`crm/?enquiryId=${activity.enquiryId}&activeTab=salesstack`, '_blank');
    } 
    if(activity.enquiryId && (activity.type === "sample_delivered" || activity.type === "sample_request_recieved" ||  activity.type === "sample_delivery_confirmation_reminder" || activity.type === "sample_delivery_confirmation_received" ||  activity.type === "testing_started_confirmation_reminder" ||  activity.type === "testing_started_confirmation" || activity.type === "sample_feedback_received" ||  activity.type === "sample_feedback_follow-up_reminder")) {
      window.open(`crm/?enquiryId=${activity.enquiryId}&sampleId=${activity.enquiryId}&activeTab=salesstack`, '_blank');
    }
  };
  
  // Helper function to format activity type
  const formatActivityType = (type: string) => {
    return activityTypeMap[type] || type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const handleAddNewRemarks = (customerId: string) => {
    setEditingRemarks(customerId);
    setRemarksEditForm({
      current: "",
      previous: ""
    });
  };

  const renderTableColumns = () => {
    return (
      <TableRow>
        <TableHead className="w-[200px] font-semibold">Customer</TableHead>
        <TableHead className="w-[150px] font-semibold">Type</TableHead>
        <TableHead className="w-[300px] font-semibold">Description</TableHead>
        <TableHead className="w-[500px] font-semibold">Remarks</TableHead>
      </TableRow>
    );
  };

  const renderTableContent = () => {
    return currentPageCustomers.map((group) => (
      <React.Fragment key={group.customerId}>
        {/* Regular activities */}
        {group.activities.map((activity, activityIndex) => (
          <TableRow key={activity.id}>
            {activityIndex === 0 ? (
              <>
                <TableCell 
                  rowSpan={group.activities.length + Object.keys(group.purchaseOrders).length} 
                  className="text-sm font-medium border-r w-[200px] whitespace-nowrap"
                >
                  {activity.customerName}
                </TableCell>
              </>
            ) : null}
            <TableCell className="w-[150px] whitespace-nowrap">
              <div className="flex items-center">
                <span className="text-sm">{formatActivityType(activity.type)}</span>
              </div>
            </TableCell>
            <TableCell className="text-sm break-words w-[300px]">
              <a 
                href="#" 
                className="text-blue-600 hover:underline"
                onClick={(e) => {
                  e.preventDefault();
                  handleViewDetails(activity);
                }}
              >
                {activity.description}
              </a>
            </TableCell>
            {activityIndex === 0 && (
              <TableCell rowSpan={group.activities.length + Object.keys(group.purchaseOrders).length} className="w-[500px] align-middle">
                {editingRemarks === group.customerId ? (
                  <div className="flex items-center gap-2">
                    <Textarea
                      value={remarksEditForm.current}
                      onChange={(e) => setRemarksEditForm({ ...remarksEditForm, current: e.target.value })}
                      className="min-h-[100px] resize-y w-[400px]"
                      placeholder="Enter remarks..."
                    />
                    <div className="flex flex-col gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSaveRemarks(group.customerId)}
                      >
                        <Check className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingRemarks(null)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <span className="line-clamp-3 w-[400px]">
                      {group.reviewRemarksCurrent || "-"}
                    </span>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleAddNewRemarks(group.customerId)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setEditingRemarks(group.customerId);
                          setRemarksEditForm({
                            current: group.reviewRemarksCurrent || '',
                            previous: group.reviewRemarksCurrent || ''
                          });
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <History className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-[1000px]">
                          <DialogHeader>
                            <DialogTitle>Remarks History</DialogTitle>
                          </DialogHeader>
                          <div className="max-h-[400px] overflow-y-auto">
                            <div className="space-y-4">
                              {group.remarks_history?.length > 0 ? (
                                [...group.remarks_history].reverse().map((remark, index) => (
                                  <div key={index} className="border-b pb-3 last:border-b-0">
                                    <div className="flex justify-between items-start mb-1">
                                      <h4 className="text-sm font-medium">
                                        Remark {index + 1}
                                      </h4>
                                      <span className="text-xs text-muted-foreground">
                                        {new Date(remark.created_at).toLocaleDateString('en-GB', {
                                          day: '2-digit',
                                          month: '2-digit',
                                          year: 'numeric'
                                        })}
                                      </span>
                                    </div>
                                    <p className="text-sm text-muted-foreground">
                                      {remark.remarks || 'No remarks'}
                                    </p>
                                  </div>
                                ))
                              ) : (
                                <div className="text-sm text-muted-foreground">
                                  No previous remarks
                                </div>
                              )}
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                )}
              </TableCell>
            )}
          </TableRow>
        ))}
      </React.Fragment>
    ));
  };

  // Calculate activities count for current page
  const currentPageActivityCount = currentPageCustomers.reduce((count, customer) => {
    return count + customer.activities.length + Object.keys(customer.purchaseOrders).length;
  }, 0);

  // Calculate start and end activity numbers for display
  const startActivityNumber = allPages
    .slice(0, currentPage - 1)
    .reduce((count, page) => {
      return count + page.reduce((pageCount, customer) => {
        return pageCount + customer.activities.length + Object.keys(customer.purchaseOrders).length;
      }, 0);
    }, 0) + 1;
  const endActivityNumber = startActivityNumber + currentPageActivityCount - 1;

  // Calculate if only No Activity tab should be shown
  const isSpecificCustomerSelected = selectedCustomer && selectedCustomer !== 'All';
  const hasActivitiesForSelectedCustomer = isSpecificCustomerSelected
    ? activities.some(a => a.customerId === selectedCustomer)
    : true;
  const showOnlyNoActivityTab = isSpecificCustomerSelected && !hasActivitiesForSelectedCustomer;

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <CardTitle>Recent Activity</CardTitle>
          <div className="w-48">
            <Select value={timeFilter} onValueChange={setTimeFilter}>
              <SelectTrigger className="h-8">
                <SelectValue placeholder="Time Period" />
              </SelectTrigger>
              <SelectContent>
                {timeFilterOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent className="px-0">
        {isSpecificCustomerSelected ? (
          hasActivitiesForSelectedCustomer ? (
            // Show only the activity table for this customer
            loading ? (
              <div className="flex justify-center items-center p-8">
                <p className="text-muted-foreground">Loading activities...</p>
              </div>
            ) : error ? (
              <div className="flex justify-center items-center p-8">
                <p className="text-destructive">Error loading activities. Please try again.</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    {renderTableColumns()}
                  </TableHeader>
                  <TableBody>
                    {currentPageCustomers.length > 0 ? (
                      renderTableContent()
                    ) : (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center py-6 text-muted-foreground">
                          No activities found matching the current filters
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
                {totalPages > 1 && (
                  <div className="flex items-center justify-between px-4 py-3 border-t">
                    <div className="text-sm text-muted-foreground">
                      Showing {startActivityNumber} to {endActivityNumber} of {totalActivities} activities
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(1)}
                        disabled={currentPage === 1}
                      >
                        First
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <div className="text-sm text-muted-foreground">
                        Page {currentPage} of {totalPages}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage === totalPages}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(totalPages)}
                        disabled={currentPage === totalPages}
                      >
                        Last
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )
          ) : (
            // Show only the 'no recent activity' table/message for this customer
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[200px] font-semibold">Customer</TableHead>
                    <TableHead className="w-[150px] font-semibold">Account Owner</TableHead>
                    <TableHead className="w-[500px] font-semibold">Remarks</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-6 text-muted-foreground">
                        Loading customers without activity...
                      </TableCell>
                    </TableRow>
                  ) : error ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-6 text-destructive">
                        Error loading customers. Please try again.
                      </TableCell>
                    </TableRow>
                  ) : currentPageNoActivity.length > 0 ? (
                    currentPageNoActivity.map((customer) => (
                      <TableRow key={customer.id}>
                        <TableCell className="text-sm font-medium border-r w-[200px] whitespace-nowrap">
                          {customer.customer_full_name}
                        </TableCell>
                        <TableCell className="w-[150px] whitespace-nowrap">
                          {customer.account_owner}
                        </TableCell>
                        <TableCell className="w-[500px]">
                          {editingRemarks === customer.id ? (
                            <div className="flex items-center gap-2">
                              <Textarea
                                value={remarksEditForm.current}
                                onChange={(e) => setRemarksEditForm({ ...remarksEditForm, current: e.target.value })}
                                className="min-h-[100px] resize-y w-[400px]"
                                placeholder="Enter remarks..."
                              />
                              <div className="flex flex-col gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleSaveRemarks(customer.id)}
                                >
                                  <Check className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setEditingRemarks(null)}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <span className="line-clamp-3 w-[400px]">{customer.reviewRemarksCurrent || '-'}</span>
                              <div className="flex gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleAddNewRemarks(customer.id)}
                                >
                                  <Plus className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEditRemarks(customer.id, customer.reviewRemarksCurrent || '')}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Dialog>
                                  <DialogTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <History className="h-4 w-4" />
                                    </Button>
                                  </DialogTrigger>
                                  <DialogContent className="max-w-[1000px]">
                                    <DialogHeader>
                                      <DialogTitle>Remarks History</DialogTitle>
                                    </DialogHeader>
                                    <div className="max-h-[400px] overflow-y-auto">
                                      <div className="space-y-4">
                                        {customer.remarks_history?.length > 0 ? (
                                          [...customer.remarks_history].reverse().map((remark, index) => (
                                            <div key={index} className="border-b pb-3 last:border-b-0">
                                              <div className="flex justify-between items-start mb-1">
                                                <h4 className="text-sm font-medium">
                                                  Remark {index + 1}
                                                </h4>
                                                <span className="text-xs text-muted-foreground">
                                                  {new Date(remark.created_at).toLocaleDateString('en-GB', {
                                                    day: '2-digit',
                                                    month: '2-digit',
                                                    year: 'numeric'
                                                  })}
                                                </span>
                                              </div>
                                              <p className="text-sm text-muted-foreground">
                                                {remark.remarks || 'No remarks'}
                                              </p>
                                            </div>
                                          ))
                                        ) : (
                                          <div className="text-sm text-muted-foreground">
                                            No previous remarks
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </DialogContent>
                                </Dialog>
                              </div>
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-6 text-muted-foreground">
                        No customers found without recent activity
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
              {totalNoActivityPages > 1 && (
                <div className="flex items-center justify-between px-4 py-3 border-t">
                  <div className="text-sm text-muted-foreground">
                    Showing {noActivityStartIndex + 1} to {noActivityEndIndex} of {totalNoActivityCustomers} customers
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setNoActivityPage(1)}
                      disabled={noActivityPage === 1}
                    >
                      First
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setNoActivityPage(prev => Math.max(prev - 1, 1))}
                      disabled={noActivityPage === 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <div className="text-sm text-muted-foreground">
                      Page {noActivityPage} of {totalNoActivityPages}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setNoActivityPage(prev => Math.min(prev + 1, totalNoActivityPages))}
                      disabled={noActivityPage === totalNoActivityPages}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setNoActivityPage(totalNoActivityPages)}
                      disabled={noActivityPage === totalNoActivityPages}
                    >
                      Last
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )
        ) : (
          // Default: show tabs for all customers
          <Tabs value={showOnlyNoActivityTab ? 'no-activity' : activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4 ml-4">
              {!showOnlyNoActivityTab && <TabsTrigger value="recent">Customers with Recent Activity</TabsTrigger>}
              <TabsTrigger value="no-activity">Customers without Recent Activity</TabsTrigger>
            </TabsList>
            {!showOnlyNoActivityTab && (
              <TabsContent value="recent">
                {loading ? (
                  <div className="flex justify-center items-center p-8">
                    <p className="text-muted-foreground">Loading activities...</p>
                  </div>
                ) : error ? (
                  <div className="flex justify-center items-center p-8">
                    <p className="text-destructive">Error loading activities. Please try again.</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        {renderTableColumns()}
                      </TableHeader>
                      <TableBody>
                        {currentPageCustomers.length > 0 ? (
                          renderTableContent()
                        ) : (
                          <TableRow>
                            <TableCell colSpan={4} className="text-center py-6 text-muted-foreground">
                              No activities found matching the current filters
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                    {totalPages > 1 && (
                      <div className="flex items-center justify-between px-4 py-3 border-t">
                        <div className="text-sm text-muted-foreground">
                          Showing {startActivityNumber} to {endActivityNumber} of {totalActivities} activities
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(1)}
                            disabled={currentPage === 1}
                          >
                            First
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                            disabled={currentPage === 1}
                          >
                            <ChevronLeft className="h-4 w-4" />
                          </Button>
                          <div className="text-sm text-muted-foreground">
                            Page {currentPage} of {totalPages}
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                            disabled={currentPage === totalPages}
                          >
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(totalPages)}
                            disabled={currentPage === totalPages}
                          >
                            Last
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </TabsContent>
            )}
            <TabsContent value="no-activity">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[200px] font-semibold">Customer</TableHead>
                      <TableHead className="w-[150px] font-semibold">Account Owner</TableHead>
                      <TableHead className="w-[500px] font-semibold">Remarks</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={3} className="text-center py-6 text-muted-foreground">
                          Loading customers without activity...
                        </TableCell>
                      </TableRow>
                    ) : error ? (
                      <TableRow>
                        <TableCell colSpan={3} className="text-center py-6 text-destructive">
                          Error loading customers. Please try again.
                        </TableCell>
                      </TableRow>
                    ) : currentPageNoActivity.length > 0 ? (
                      currentPageNoActivity.map((customer) => (
                        <TableRow key={customer.id}>
                          <TableCell className="text-sm font-medium border-r w-[200px] whitespace-nowrap">
                            {customer.customer_full_name}
                          </TableCell>
                          <TableCell className="w-[150px] whitespace-nowrap">
                            {customer.account_owner}
                          </TableCell>
                          <TableCell className="w-[500px]">
                            {editingRemarks === customer.id ? (
                              <div className="flex items-center gap-2">
                                <Textarea
                                  value={remarksEditForm.current}
                                  onChange={(e) => setRemarksEditForm({ ...remarksEditForm, current: e.target.value })}
                                  className="min-h-[100px] resize-y w-[400px]"
                                  placeholder="Enter remarks..."
                                />
                                <div className="flex flex-col gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleSaveRemarks(customer.id)}
                                  >
                                    <Check className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setEditingRemarks(null)}
                                  >
                                    <X className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <div className="flex items-center gap-2">
                                <span className="line-clamp-3 w-[400px]">{customer.reviewRemarksCurrent || '-'}</span>
                                <div className="flex gap-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleAddNewRemarks(customer.id)}
                                  >
                                    <Plus className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleEditRemarks(customer.id, customer.reviewRemarksCurrent || '')}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Dialog>
                                    <DialogTrigger asChild>
                                      <Button variant="ghost" size="sm">
                                        <History className="h-4 w-4" />
                                      </Button>
                                    </DialogTrigger>
                                    <DialogContent className="max-w-[1000px]">
                                      <DialogHeader>
                                        <DialogTitle>Remarks History</DialogTitle>
                                      </DialogHeader>
                                      <div className="max-h-[400px] overflow-y-auto">
                                        <div className="space-y-4">
                                          {customer.remarks_history?.length > 0 ? (
                                            [...customer.remarks_history].reverse().map((remark, index) => (
                                              <div key={index} className="border-b pb-3 last:border-b-0">
                                                <div className="flex justify-between items-start mb-1">
                                                  <h4 className="text-sm font-medium">
                                                    Remark {index + 1}
                                                  </h4>
                                                  <span className="text-xs text-muted-foreground">
                                                    {new Date(remark.created_at).toLocaleDateString('en-GB', {
                                                      day: '2-digit',
                                                      month: '2-digit',
                                                      year: 'numeric'
                                                    })}
                                                  </span>
                                                </div>
                                                <p className="text-sm text-muted-foreground">
                                                  {remark.remarks || 'No remarks'}
                                                </p>
                                              </div>
                                            ))
                                          ) : (
                                            <div className="text-sm text-muted-foreground">
                                              No previous remarks
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </DialogContent>
                                  </Dialog>
                                </div>
                              </div>
                            )}
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={3} className="text-center py-6 text-muted-foreground">
                          No customers found without recent activity
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
                {totalNoActivityPages > 1 && (
                  <div className="flex items-center justify-between px-4 py-3 border-t">
                    <div className="text-sm text-muted-foreground">
                      Showing {noActivityStartIndex + 1} to {noActivityEndIndex} of {totalNoActivityCustomers} customers
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setNoActivityPage(1)}
                        disabled={noActivityPage === 1}
                      >
                        First
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setNoActivityPage(prev => Math.max(prev - 1, 1))}
                        disabled={noActivityPage === 1}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <div className="text-sm text-muted-foreground">
                        Page {noActivityPage} of {totalNoActivityPages}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setNoActivityPage(prev => Math.min(prev + 1, totalNoActivityPages))}
                        disabled={noActivityPage === totalNoActivityPages}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setNoActivityPage(totalNoActivityPages)}
                        disabled={noActivityPage === totalNoActivityPages}
                      >
                        Last
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
};
