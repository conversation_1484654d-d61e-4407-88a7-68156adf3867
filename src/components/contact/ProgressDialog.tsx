
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { CheckCircle, LoaderCircle } from "lucide-react";

interface ProgressDialogProps {
  showDialog: boolean;
  setShowDialog: (show: boolean) => void;
  isSuccess: boolean;
}

const ProgressDialog = ({ showDialog, setShowDialog, isSuccess }: ProgressDialogProps) => {
  return (
    <Dialog open={showDialog} onOpenChange={setShowDialog}>
      <DialogContent className="sm:max-w-md bg-white border-2 border-[#E6EAE9] rounded-lg shadow-sm">
        <DialogHeader>
          <DialogTitle className="text-center text-[#294d48] font-semibold">
            {isSuccess ? "Request Sent Successfully!" : "Sending Your Request"}
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center py-8">
          {isSuccess ? (
            <div className="flex flex-col items-center space-y-4">
              <CheckCircle className="h-12 w-12 text-[#294d48] opacity-90 animate-scale-in" />
              <p className="text-[#555555]">We'll be in touch soon!</p>
            </div>
          ) : (
            <div className="flex flex-col items-center space-y-4">
              <LoaderCircle className="h-12 w-12 text-[#294d48] opacity-70 animate-spin" />
              <p className="text-[#555555]">Processing your request...</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ProgressDialog;
