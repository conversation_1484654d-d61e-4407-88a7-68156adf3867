
import { Input } from "@/components/ui/input";
import { LucideIcon } from "lucide-react";

interface ContactFormFieldProps {
  icon: LucideIcon;
  placeholder: string;
  value: string;
  onChange: (value: string) => void;
  type?: string;
  className?: string;
}

const ContactFormField = ({ 
  icon: Icon, 
  placeholder, 
  value, 
  onChange, 
  type = "text",
  className = ""
}: ContactFormFieldProps) => {
  return (
    <div className={`relative group ${className}`}>
      <Icon className="absolute left-3 top-2.5 h-4 w-4 text-primary-500" />
      <Input
        type={type}
        placeholder={placeholder}
        className="pl-9 border-primary-100 focus:border-primary-300 transition-colors"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        required
      />
    </div>
  );
};

export default ContactFormField;
