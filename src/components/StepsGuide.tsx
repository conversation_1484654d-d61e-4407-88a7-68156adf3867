
import { Card } from "@/components/ui/card";
import { ArrowRight } from "lucide-react";

const StepsGuide = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-8">
      {[
        {
          title: "Create Query",
          description: "Add your chemical details and quantities required",
        },
        {
          title: "Add Info",
          description: "Provide additional specifications and requirements",
        },
        {
          title: "Submit",
          description: "Review your query and submit for processing",
        },
        {
          title: "Track Status",
          description: "Monitor the progress of your enquiry",
        },
      ].map((step, index) => (
        <div
          key={index}
          className="p-6 bg-white border border-gray-100 rounded-lg hover:border-[#294d48]/30 transition-all duration-300 group"
        >
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-[#294d48]">{step.title}</h3>
              <p className="text-sm text-gray-600 leading-relaxed">
                {step.description}
              </p>
            </div>
            <ArrowRight 
              className="h-5 w-5 text-[#294d48] opacity-70 group-hover:opacity-100 transition-colors" 
              strokeWidth={2}
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default StepsGuide;
