import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Search, ChevronDown, ChevronUp } from "lucide-react";
import { MeetingForm } from "./MeetingForm";
import { MeetingsList, Meeting } from "./MeetingsList";
import { useMeetings } from "./hooks/useMeetings";
import { useCustomers } from "@/hooks/use-customers";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { MeetingsFilter } from "./MeetingsFilter";

export function MeetingsContainer({customerId}) {
  const { meetings, isLoading, error, submitMeeting, isSubmitting } = useMeetings();
  const { customers } = useCustomers();
  const [isAddingMeeting, setIsAddingMeeting] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState('');
  const [expandAll, setExpandAll] = useState(false);
  const [filterParams, setFilterParams] = useState({
    customerName: "",
    accountOwner: "",
    dateFilter: "all",
    meetingType: "all"
  });

  // Transform meetings data to match Meeting type
  const transformedMeetings: Meeting[] = (meetings || []).map(meeting => ({
    id: meeting.id,
    customer: meeting.customerName,
    customerId: meeting.customerId,
    description: meeting.summary,
    date: meeting?.date && !isNaN(new Date(meeting.date).getTime())
      ? new Date(meeting.date).toISOString()
      : null,
    meetingType: meeting.meetingType,
    status: "Scheduled",
    remarks: "",
    accountOwner: meeting.accountOwner,
    meetingWith: meeting.meetingWith,
    followUpSteps: meeting.followUpSteps,
    enquiriesReceived: "",
    sampleRequests: "",
    posRaised: ""
  }));

  // Get unique account owners from customers and add current user
  const currentUserEmail = localStorage.getItem('userEmail');
  const accountOwnerList = Array.from(new Set([
    ...customers.map(c => c.account_owner),
    currentUserEmail
  ].filter(Boolean)));

  useEffect(() => {
    const customer = customers.find(c => c.id === customerId);
    setFilterParams({
      customerName: customer?.customer_full_name || "",
      accountOwner: customer?.account_owner || "",
      dateFilter: "",
      meetingType: "all"
    });
  }, [customerId, customers]);

  const handleAddMeeting = async (newMeeting: Omit<Meeting, "id">) => {
    try {
      await submitMeeting({
        customer: newMeeting.customer,
        meetingWith: newMeeting.meetingWith,
        meetingType: newMeeting.meetingType,
        date: newMeeting.date,
        accountOwner: newMeeting.accountOwner,
        description: newMeeting.description,
        followUpSteps: newMeeting.followUpSteps,
        enquiriesReceived: newMeeting.enquiriesReceived,
        sampleRequests: newMeeting.sampleRequests,
        posRaised: newMeeting.posRaised
      });
      setIsAddingMeeting(false);
      toast.success("Meeting added successfully");
    } catch (error) {
      toast.error("Failed to add meeting");
    }
  };

  const handleUpdateMeeting = async (meeting: Meeting) => {
    try {
      await submitMeeting({
        id: meeting.id,
        customer: meeting.customer,
        meetingWith: meeting.meetingWith,
        meetingType: meeting.meetingType,
        date: meeting.date,
        accountOwner: meeting.accountOwner,
        description: meeting.description,
        followUpSteps: meeting.followUpSteps,
        enquiriesReceived: meeting.enquiriesReceived,
        sampleRequests: meeting.sampleRequests,
        posRaised: meeting.posRaised
      });
      toast.success("Meeting updated successfully");
    } catch (error) {
      toast.error("Failed to update meeting");
    }
  };

  const handleDeleteMeeting = async (id: string) => {
    try {
      // TODO: Implement delete functionality in useMeetings hook
      toast.success("Meeting deleted successfully");
    } catch (error) {
      toast.error("Failed to delete meeting");
    }
  };

  const handleFilter = (params: {
    customerName?: string;
    accountOwner?: string;
    dateFilter?: string;
    meetingType?: string;
  }) => {
    setFilterParams({
      customerName: params.customerName || "",
      accountOwner: params.accountOwner || "",
      dateFilter: params.dateFilter || "all",
      meetingType: params.meetingType || "all"
    });
  };

  // Filter meetings based on search term and filter params
  const filteredMeetings = transformedMeetings.filter(meeting => {
    const matchesSearch = searchTerm.trim() === '' || 
      meeting.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      meeting.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (meeting.accountOwner && meeting.accountOwner.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCustomer = filterParams.customerName === "all" || !filterParams.customerName || 
      meeting.customer === filterParams.customerName;

    const matchesAccountOwner = filterParams.accountOwner === "all" || !filterParams.accountOwner || 
      meeting.accountOwner === filterParams.accountOwner;

    const matchesMeetingType = filterParams.meetingType === "all" || !filterParams.meetingType || 
      meeting.meetingType === filterParams.meetingType;

    const matchesDate = filterParams.dateFilter === "all" || (() => {
      const today = new Date();
      const meetingDate = new Date(meeting.date);

      switch (filterParams.dateFilter) {
        case "today":
          return meetingDate.toDateString() === today.toDateString();
        case "thisWeek":
          const startOfWeek = new Date(today);
          startOfWeek.setDate(today.getDate() - today.getDay());
          const endOfWeek = new Date(today);
          endOfWeek.setDate(startOfWeek.getDate() + 6);
          return meetingDate >= startOfWeek && meetingDate <= endOfWeek;
        case "thisMonth":
          return meetingDate.getMonth() === today.getMonth() &&
            meetingDate.getFullYear() === today.getFullYear();
        default:
          return true;
      }
    })();

    return matchesSearch && matchesCustomer && matchesAccountOwner && matchesMeetingType && matchesDate;
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 text-center p-4">
        Error loading meetings: {error.message}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row items-center justify-between mb-4 gap-4">
        <div>
          <h1 className="text-2xl font-bold">Meetings</h1>
          <p className="text-muted-foreground">View and manage customer meetings</p>
        </div>
        
        <div className="relative w-full md:w-1/3 mx-auto">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search meetings..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setExpandAll(!expandAll)}
            className="flex items-center gap-1"
          >
            {expandAll ? (
              <>
                <ChevronUp className="h-4 w-4" />
                Collapse All
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4" />
                Expand All
              </>
            )}
          </Button>
          <Button onClick={() => setIsAddingMeeting(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Meeting
          </Button>
        </div>
      </div>

      {!isAddingMeeting && (
        <MeetingsFilter 
          onFilter={handleFilter} 
          customers={customers}
          accountOwnerList={accountOwnerList}
          initialFilters={filterParams}
        />
      )}

      {isAddingMeeting && (
        <MeetingForm
          onSubmit={handleAddMeeting}
          onCancel={() => setIsAddingMeeting(false)}
          selectedCustomerId={selectedCustomerId}
          customers={customers}
        />
      )}

      <MeetingsList 
        meetings={filteredMeetings}
        onAddMeeting={handleAddMeeting}
        onUpdateMeeting={handleUpdateMeeting}
        onDeleteMeeting={handleDeleteMeeting}
        selectedCustomerId={selectedCustomerId}
        expandAll={expandAll}
      />
    </div>
  );
}
