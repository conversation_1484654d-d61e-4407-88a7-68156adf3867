import React, { useState, useEffect } from 'react';
import { Check, Clock, Edit, Filter, Search, X, Trash, Calendar, Users, Video, Phone, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { Card, CardContent } from '@/components/ui/card';
import { format } from 'date-fns';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useNavigate } from 'react-router-dom';

// Define interface that matches MeetingsContainer.tsx's Meeting type
export interface Meeting {
  id: string;
  date: string;
  customer: string;
  customerId: string;
  description: string;
  meetingType: string;
  status: string;
  remarks: string;
  accountOwner: string;
  meetingWith?: string;
  followUpSteps?: {id: string; description: string; dueDate: Date}[];
  enquiriesReceived?: string;
  sampleRequests?: string;
  posRaised?: string;
}

interface MeetingsListProps {
  meetings: Meeting[];
  onAddMeeting: (meeting: Omit<Meeting, 'id'>) => void;
  onUpdateMeeting: (meeting: Meeting) => void;
  onDeleteMeeting: (id: string) => void;
  selectedCustomerId?: string;
  expandAll?: boolean;
}

export const MeetingsList = ({ 
  meetings, 
  onAddMeeting, 
  onUpdateMeeting, 
  onDeleteMeeting,
  selectedCustomerId,
  expandAll = false
}: MeetingsListProps) => {
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);
  const [expandedMeetings, setExpandedMeetings] = useState<string[]>([]);
  const navigate = useNavigate();
  
  // Handle expand all change
  useEffect(() => {
    if (expandAll) {
      setExpandedMeetings(meetings.map(m => m.id));
    } else {
      setExpandedMeetings([]);
    }
  }, [expandAll]);

  // Improved toggle functionality with stable event handling
  const toggleMeetingExpand = (e: React.MouseEvent, id: string) => {
    // Prevent event bubbling to avoid parent click handlers
    e.preventDefault();
    e.stopPropagation();
    
    // Use a callback to ensure we're working with the latest state
    setExpandedMeetings(prev => {
      // Create a new array to ensure React detects the state change
      const isExpanded = prev.includes(id);
      if (isExpanded) {
        return prev.filter(meetingId => meetingId !== id);
      } else {
        return [...prev, id];
      }
    });
  };
  
  const handleEditMeeting = (e: React.MouseEvent, meeting: Meeting) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent event bubbling
    // Navigate to edit meeting page with meeting data
    navigate(`/meetings/edit/${meeting.id}`, { 
      state: { 
        meeting,
        activeTab: "meetings",
        keepOpen : true // Add the tab information
      } 
    });
  };
  
  const handleConfirmDelete = () => {
    if (deleteConfirm) {
      onDeleteMeeting(deleteConfirm);
      setDeleteConfirm(null);
      toast.success('Meeting deleted successfully');
    }
  };
  
  const getMeetingTypeIcon = (type: string) => {
    switch (type) {
      case 'In-person Meeting':
        return <Users className="h-4 w-4" />;
      case 'Virtual Meeting':
        return <Video className="h-4 w-4" />;
      case 'Phone Call':
        return <Phone className="h-4 w-4" />;
      default:
        return <Calendar className="h-4 w-4" />;
    }
  };

  if (meetings.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No meetings found.</p>
      </div>
    );
  }
  
  return (
    <div>
      <div className="space-y-4">
        {meetings.length > 0 ? (
          meetings.map((meeting) => (
            <Card 
              key={meeting.id} 
              className="overflow-hidden"
            >
              <div 
                className="border-b p-4 flex justify-between items-center bg-muted/30 cursor-pointer"
                onClick={(e) => toggleMeetingExpand(e, meeting.id)}
              >
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className="text-lg font-semibold">{meeting.customer}</h3>
                    {meeting.meetingWith && (
                      <span className="text-muted-foreground">with {meeting.meetingWith}</span>
                    )}
                  </div>
                  {meeting.meetingType && (
                    <div className="flex items-center gap-2 mt-1">
                      {getMeetingTypeIcon(meeting.meetingType)}
                      <span className="text-sm text-muted-foreground">{meeting.meetingType}</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <div className="text-sm">{format(new Date(meeting.date), 'MMM d, yyyy')}</div>
                    <div className="text-sm text-muted-foreground">{meeting.accountOwner}</div>
                  </div>
                  <div className="flex items-center justify-center w-6 h-6">
                    {expandedMeetings.includes(meeting.id) ? 
                      <ChevronUp className="h-5 w-5" /> : 
                      <ChevronDown className="h-5 w-5" />
                    }
                  </div>
                </div>
              </div>
              
              {expandedMeetings.includes(meeting.id) && (
                <CardContent className="p-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium flex items-center gap-2 mb-2">
                        <Calendar className="h-4 w-4" /> Meeting Summary
                      </h4>
                      <p className="text-sm">{meeting.description}</p>
                    </div>
                    
                    {meeting.followUpSteps && meeting.followUpSteps.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium flex items-center gap-2 mb-2">
                          <Check className="h-4 w-4" /> Follow Up Steps
                        </h4>
                        <ul className="space-y-2">
                          {meeting.followUpSteps.map(step => (
                            <li key={step.id} className="text-sm border-l-2 border-blue-500 pl-3 py-1">
                              <div>{step.description}</div>
                              <div className="text-xs text-muted-foreground">
                                Due: {format(new Date(step.dueDate), 'MMM d, yyyy')}
                              </div>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 pt-4 border-t">
                    {meeting.enquiriesReceived && (
                      <div>
                        <h4 className="text-sm font-medium mb-1">Enquiries Received</h4>
                        <p className="text-sm">{meeting.enquiriesReceived}</p>
                      </div>
                    )}
                    
                    {meeting.sampleRequests && (
                      <div>
                        <h4 className="text-sm font-medium mb-1">Sample Requests</h4>
                        <p className="text-sm">{meeting.sampleRequests}</p>
                      </div>
                    )}
                    
                    {meeting.posRaised && (
                      <div>
                        <h4 className="text-sm font-medium mb-1">POs Raised</h4>
                        <p className="text-sm">{meeting.posRaised}</p>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex justify-end gap-2 mt-4" onClick={(e) => e.stopPropagation()}>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={(e) => handleEditMeeting(e, meeting)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="text-red-600 hover:text-red-700"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setDeleteConfirm(meeting.id);
                      }}
                    >
                      <Trash className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </CardContent>
              )}
            </Card>
          ))
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No meetings match your search criteria.</p>
          </div>
        )}
      </div>
      
      <AlertDialog open={!!deleteConfirm} onOpenChange={(open) => !open && setDeleteConfirm(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the meeting record.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
