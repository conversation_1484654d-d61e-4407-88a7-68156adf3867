import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";

type Customer = Database["public"]["Tables"]["customer"]["Row"];

export const useCustomers = () => {
  const {
    data: customers,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["customers"],
    queryFn: async () => {
      // Get current user's role and email
      const user = {
        email: localStorage.getItem('userEmail'),
        role: localStorage.getItem('userRole'),
        category: localStorage.getItem('userCategory')
      };

      let query = supabase
        .from("customer")
        .select("id, customer_full_name, account_owner")
        .order("customer_full_name", { ascending: true });

      // Apply filters based on user role
      if (user.role === 'sales' && user.email) {
        query = query.eq('account_owner', user.email);
      } else if (user.role === 'bu_head') {
        // First get all users of the category from user_roles
        const { data: categoryUsers, error: usersError } = await supabase
          .from('user_list')
          .select('email, user_roles!inner(*)')
          .eq('user_roles.category', user?.category);

        if (usersError) throw usersError;

        // Get the list of emails from the users
        const accountOwners = categoryUsers?.map(user => user.email) || [];

        // Filter customers by these account owners
        query = query.in('account_owner', accountOwners);
      }

      const { data, error } = await query;

      if (error) throw error;

      return data.map((customer: Customer) => ({
        id: customer.id,
        name: customer.customer_full_name,
        accountOwner: customer.account_owner
      }));
    },
  });

  return {
    customers,
    isLoading,
    error,
  };
}; 