import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";
import { toast } from "sonner";

const BATCH_SIZE = 1000;

type CustomerMeeting = Database["public"]["Tables"]["customer_meeting"]["Row"] & {
  customer?: Database["public"]["Tables"]["customer"]["Row"];
};

interface FollowUpStep {
  id: string;
  description: string;
  dueDate: Date;
}

interface NewMeeting {
  id?: string;
  customer: string;
  meetingWith?: string;
  meetingType: string;
  date: string;
  accountOwner?: string;
  description?: string;
  followUpSteps?: FollowUpStep[];
  enquiriesReceived?: string;
  sampleRequests?: string;
  posRaised?: string;
}

// Helper function to validate and convert JSON to FollowUpStep
const convertToFollowUpStep = (json: unknown): FollowUpStep | null => {
  if (typeof json !== 'object' || json === null) return null;
  
  const obj = json as Record<string, unknown>;
  if (
    typeof obj.id === 'string' &&
    typeof obj.description === 'string' &&
    (typeof obj.dueDate === 'string' || obj.dueDate instanceof Date)
  ) {
    return {
      id: obj.id,
      description: obj.description,
      dueDate: obj.dueDate instanceof Date ? obj.dueDate : new Date(obj.dueDate),
    };
  }
  return null;
};

export const useMeetings = () => {
  const queryClient = useQueryClient();

  const {
    data: meetings,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["customer-meetings"],
    queryFn: async () => {
      // Get current user's role and email
      const user = {
        email: localStorage.getItem('userEmail'),
        role: localStorage.getItem('userRole'),
        category: localStorage.getItem('userCategory')
      };

      // First get total count
      const { count } = await supabase
        .from("customer_meeting")
        .select("*", { count: 'exact', head: true });

      if (!count) return [];

      // Calculate number of batches needed
      const numBatches = Math.ceil(count / BATCH_SIZE);
      let allMeetingsData: CustomerMeeting[] = [];

      // Fetch each batch
      for (let i = 0; i < numBatches; i++) {
        const start = i * BATCH_SIZE;
        const end = start + BATCH_SIZE - 1;

        let query = supabase
          .from("customer_meeting")
          .select(`
            *,
            customer:customer_id (*)
          `)
          .order("date", { ascending: false })
          .range(start, end);

        // Apply filters based on user role
        if (user.role === 'sales' && user.email) {
          query = query.eq('account_owner', user.email);
        } else if (user.role === 'bu_head') {
          // First get all users of the category from user_roles
          const { data: categoryUsers, error: usersError } = await supabase
            .from('user_list')
            .select('email, user_roles!inner(*)')
            .eq('user_roles.category', user?.category);

          if (usersError) throw usersError;

          // Get the list of emails from the users
          const accountOwners = categoryUsers?.map(user => user.email) || [];

          // Filter customers by these account owners
          query = query.in('account_owner', accountOwners);
        }

        const { data: batchMeetings, error: batchError } = await query;

        if (batchError) throw batchError;
        if (batchMeetings) {
          allMeetingsData = [...allMeetingsData, ...batchMeetings];
        }
      }

      // Transform the data to match your frontend structure
      return allMeetingsData.map((meeting: CustomerMeeting) => ({
        id: meeting.id,
        customerName: meeting.customer?.customer_full_name || "",
        customerId: meeting.customer?.id,
        meetingWith: meeting.title || "",
        meetingType: meeting.meeting_type || "",
        date: new Date(meeting.date || ""),
        accountOwner: meeting.account_owner || "",
        summary: meeting.summary || "",
        followUpSteps: (meeting.follow_up_steps || [])
          .map(convertToFollowUpStep)
          .filter((step): step is FollowUpStep => step !== null),
      }));
    },
  });

  const { mutate: submitMeeting, isPending: isSubmitting } = useMutation({
    mutationFn: async (newMeeting: NewMeeting) => {
      // First, get the customer ID from the customer name
      const { data: customerData, error: customerError } = await supabase
        .from("customer")
        .select("id")
        .eq("customer_full_name", newMeeting.customer)
        .limit(1)
        .single();

      if (customerError || !customerData) {
        throw new Error("Customer not found");
      }

      // Prepare the meeting data
      const meetingData = {
        title: newMeeting.meetingWith,
        meeting_type: newMeeting.meetingType,
        customer_id: customerData.id,
        account_owner: newMeeting.accountOwner,
        date: newMeeting.date,
        summary: newMeeting.description,
        follow_up_steps: newMeeting.followUpSteps?.map(step => ({
          id: step.id,
          description: step.description,
          dueDate: step.dueDate.toISOString()
        })) || [],
        created_at: new Date().toISOString()
      };

      if (newMeeting.id) {
        // Update existing meeting
        const { error: updateError } = await supabase
          .from("customer_meeting")
          .update(meetingData)
          .eq("id", newMeeting.id);

        if (updateError) {
          throw updateError;
        }
      } else {
        // Insert new meeting
        const { data: insertData, error: insertError } = await supabase
          .from("customer_meeting")
          .insert(meetingData)
          .select()
          .single();

        if (insertError) {
          throw insertError;
        }

        const activityData = {
          activity_type: "meetings",
          customer_id: customerData.id,
          description: newMeeting.description,
          enquiry_id: null,
          reference_id: insertData?.id || ""
        };

        const { data: ActivityData } = await supabase
          .from("customer_activity")
          .insert(activityData);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["customer-meetings"] });
      toast.success("Meeting saved successfully");
    },
    onError: (error) => {
      toast.error("Failed to save meeting: " + error.message);
    }
  });

  return {
    meetings,
    isLoading,
    error,
    refetch,
    submitMeeting,
    isSubmitting
  };
}; 