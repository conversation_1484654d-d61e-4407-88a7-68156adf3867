import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Plus, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Meeting } from "./MeetingsList";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { Search, X } from "lucide-react";

type FollowUpStep = {
  id: string;
  description: string;
  dueDate: Date;
};

type MeetingFormProps = {
  onSubmit: (meeting: Omit<Meeting, "id">) => void;
  onCancel: () => void;
  selectedCustomerId?: string;
  initialValues?: Meeting;
  isEdit?: boolean;
  customers: Array<{
    id: string;
    customer_full_name: string;
    account_owner: string;
  }>;
};

export function MeetingForm({ 
  onSubmit, 
  onCancel, 
  selectedCustomerId = "", 
  initialValues,
  isEdit = false,
  customers
}: MeetingFormProps) {
  const [customer, setCustomer] = useState(initialValues?.customer || "");
  const [customerId, setCustomerId] = useState(initialValues?.customerId || selectedCustomerId);
  const [meetingWith, setMeetingWith] = useState(initialValues?.meetingWith || "");
  const [meetingType, setMeetingType] = useState(initialValues?.meetingType || "");
  const [date, setDate] = useState<Date | undefined>(
    initialValues?.date ? new Date(initialValues.date) : new Date()
  );
  const [description, setDescription] = useState(initialValues?.description || "");
  const [accountOwner, setAccountOwner] = useState(localStorage.getItem('userEmail') || "");
  const [accountOwnerRole, setAccountOwnerRole] = useState(localStorage.getItem("userRole") || "")
  const [accountOwnerCategory, setAccountOwnerCategory] = useState(localStorage.getItem("userCategory") || "")
  const [selectedSalesPOC, setSelectedSalesPOC] = useState<string>("");
  const [salesTeamMembers, setSalesTeamMembers] = useState<{ value: string; label: string; category: string }[]>([]);
  const [showValidationErrors, setShowValidationErrors] = useState(false);
  const [session, setSession] = useState<any>(null);
  const [followUpSteps, setFollowUpSteps] = useState<FollowUpStep[]>(
    initialValues?.followUpSteps || []
  );
  const [enquiriesReceived, setEnquiriesReceived] = useState(initialValues?.enquiriesReceived || "");
  const [sampleRequests, setSampleRequests] = useState(initialValues?.sampleRequests || "");
  const [posRaised, setPosRaised] = useState(initialValues?.posRaised || "");
  const [errors, setErrors] = useState<{ customer?: string; description?: string; date?: string; meetingType?: string; selectedSalesPOC?: string }>({});
  const [searchTerm, setSearchTerm] = useState("");
  const searchInputRef = useRef<HTMLInputElement>(null);


  useEffect(() => {
    if (selectedCustomerId) {
      setCustomerId(selectedCustomerId);
    }
  }, [selectedCustomerId]);

    useEffect(() => {
      const fetchSession = async () => {
        try {
          const { data } = await supabase.auth.getSession();
          setSession(data?.session);
        } catch (error) {
          console.error("Error fetching session:", error);
        }
      };
      fetchSession();
    }, []);

    // Fetch sales team members from the database
    useEffect(() => {
      const fetchSalesTeam = async () => {
        // setIsLoadingSalesTeam(true);
        try {
          const { data, error } = await supabase
            .from("user_list")
            .select(
              `
            id,
            email,
            user_roles(role, category)
          `
            )
            .order("email");

          if (error) {
            console.error("Error fetching sales team:", error);
            // toast.error("Failed to load sales team members");
            return;
          }

          // Transform data to match the format needed for the dropdown
          const formattedTeam = data
            .map((member) => {
              if (member?.email === accountOwner) {
                return {
                  value: member.email,
                  label: "Me",
                  category: member.user_roles[0]?.category,
                };
              }
              return {
                value: member.email,
                label: member.email,
                category: member.user_roles[0]?.category,
              };
            })
            .sort((a, b) => (a.label === "Me" ? -1 : b.label === "Me" ? 1 : 0));

          // Filter out members with null category or country
          const validMembers = formattedTeam.filter(
            (member) =>
              member.category !== null && member.category !== undefined
          );

          setSalesTeamMembers(validMembers);
        } catch (error) {
          console.error("Exception fetching sales team:", error);
          // toast.error("Failed to load sales team members");÷
        } finally {
          // setIsLoadingSalesTeam(false);
        }
      };

      fetchSalesTeam();
    }, [session]);
  
  // Add this useEffect to auto-set Sales POC for non-admin/bu_head users
  useEffect(() => {
    if (accountOwnerRole !== "admin" && accountOwnerRole !== "bu_head") {
      setSelectedSalesPOC(accountOwner); // Set to logged-in user
    }
  }, [accountOwnerRole, accountOwner]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newErrors: {customer?: string; description?: string; date?: string; meetingType?: string; selectedSalesPOC?: string;} = {};
    if (!customer) newErrors.customer = 'Customer is required.';
    if (!description.trim()) newErrors.description = 'Meeting summary is required.';
    if (!date) newErrors.date = 'Date is required.';
    if (!meetingType) newErrors.meetingType = 'Meeting type is required.';
    
    // Only validate Sales POC for admin/bu_head users
    if ((accountOwnerRole === "admin" || accountOwnerRole === "bu_head") && !selectedSalesPOC) {
      newErrors.selectedSalesPOC = "Sales POC is required.";
    }
    
    setErrors(newErrors);
    if (Object.keys(newErrors).length > 0) return;
    
    // Use selectedSalesPOC if set, otherwise use accountOwner
    const finalAccountOwner = selectedSalesPOC || accountOwner;
    
    onSubmit({
      customer,
      customerId: customerId || "",
      meetingType,
      description,
      date: date?.toISOString() || "",
      status: "Scheduled",
      remarks: "",
      accountOwner: finalAccountOwner, // Use the correct value
      meetingWith,
      followUpSteps,
      enquiriesReceived,
      sampleRequests,
      posRaised
    });

    setSelectedSalesPOC(null)
    setCustomer(null)
  };
  
  const addFollowUpStep = () => {
    const newStep: FollowUpStep = {
      id: Math.random().toString(36).substr(2, 9),
      description: "",
      dueDate: new Date()
    };
    setFollowUpSteps([...followUpSteps, newStep]);
  };
  
  const updateFollowUpStep = (id: string, field: keyof FollowUpStep, value: any) => {
    setFollowUpSteps(followUpSteps.map(step => 
      step.id === id ? { ...step, [field]: value } : step
    ));
  };
  
  const removeFollowUpStep = (id: string) => {
    setFollowUpSteps(followUpSteps.filter(step => step.id !== id));
  };
  // Filter sales team members based on user role and category
  const getFilteredSalesTeam = () => {
    // If admin, show all sales team members
    if (accountOwnerRole === "admin") {
      return salesTeamMembers;
    }

    // If BU head, filter by their category
    else if (accountOwnerRole === "bu_head" && accountOwnerCategory) {
      return salesTeamMembers.filter(
        (member) => member.category === accountOwnerCategory
      );
    }

    // Default case - return empty array
    return [];
  };

    const handleSalesPOCChange = (value: string) => {
    setSelectedSalesPOC(value);

    // If the selected value is "Me", do nothing
    if (value === "Me") {
      return;
    }

  };

  return (
    <div className="border rounded-md p-6 mb-6 bg-card shadow-sm">
      <h2 className="text-xl font-semibold mb-6">{isEdit ? "Edit Meeting" : "Add New Meeting"}</h2>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
          <div className="space-y-2">
            <Label htmlFor="customer">
              Customer <span className="text-red-500">*</span>
            </Label>
            <Select
              value={customer}
              onValueChange={(value) => setCustomer(value)}
            >
              <SelectTrigger id="target-user">
                <SelectValue placeholder="Select a customer" />
              </SelectTrigger>
              <SelectContent>
                <div className="flex items-center px-3 pb-2">
                  <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                  <input
                    ref={searchInputRef}
                    className="flex h-8 w-full rounded-md border-0 bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                    placeholder="Search customers..."
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value);
                      setTimeout(() => {
                        searchInputRef.current?.focus();
                      }, 0);
                    }}
                    onBlur={(e) => {
                      e.preventDefault();
                    }}
                  />
                </div>
                {customers
                  ?.filter((c) =>
                    c.customer_full_name
                      ?.toLowerCase()
                      .includes(searchTerm.toLowerCase())
                  )
                  .map((c) => (
                    <SelectItem key={c.id} value={c.customer_full_name}>
                      {c.customer_full_name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
            {errors.customer && <div className="text-red-500 text-xs mt-1">{errors.customer}</div>}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="meetingWith">Meeting With</Label>
            <Input
              id="meetingWith"
              value={meetingWith}
              onChange={(e) => setMeetingWith(e.target.value)}
              placeholder="Enter attendee names"
            />
          </div>

             {/* Sales POC dropdown - only for admins and BU heads */}
              {(accountOwnerRole === "admin" || accountOwnerRole === "bu_head") && (
                <div>
                  <Label
                    htmlFor="salesPOC"
                    className="block text-sm font-medium mb-1"
                  >
                    Sales POC Name <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={selectedSalesPOC}
                    onValueChange={handleSalesPOCChange}
                  >
                   <SelectTrigger 
                      className={`w-full h-10 text-sm ${
                        showValidationErrors && (accountOwnerRole === "admin" || accountOwnerRole === "bu_head") && !selectedSalesPOC
                          ? "border-red-500"
                          : ""
                      }`}
                      style={{
                        fontSize: '16px',
                        color: selectedSalesPOC ? 'inherit' : '#9CA3AF'
                      }}
                    >
                    <SelectValue placeholder="Select Sales POC" />
                    </SelectTrigger>
                    <SelectContent>
                      {/* <SelectItem value="Me">Me</SelectItem> */}
                      {getFilteredSalesTeam().map((member) => (
                        <SelectItem key={member.value} value={member.value}>
                          {member.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                   {errors.selectedSalesPOC && <div className="text-red-500 text-xs mt-1">{errors.selectedSalesPOC}</div>}
                </div>
              )}
          
          <div className="space-y-2">
            <Label htmlFor="meetingType">
              Meeting Type <span className="text-red-500">*</span>
            </Label>
            <Select
              value={meetingType}
              onValueChange={(value) => setMeetingType(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a meeting type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Phone Call">Phone Call</SelectItem>
                <SelectItem value="Virtual Meeting">Virtual Meeting</SelectItem>
                <SelectItem value="In-person Meeting">In-person Meeting</SelectItem>
              </SelectContent>
            </Select>
            {errors.meetingType && <div className="text-red-500 text-xs mt-1">{errors.meetingType}</div>}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="date">
              Date <span className="text-red-500">*</span>
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "PPP") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={(date) => date && setDate(date)}
                  initialFocus
                  disabled={d => d > new Date()}
                />
              </PopoverContent>
            </Popover>
            {errors.date && <div className="text-red-500 text-xs mt-1">{errors.date}</div>}
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="description">
              Meeting Summary <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter meeting summary"
              className="min-h-[100px] max-h-[100px]"
            />
            {errors.description && <div className="text-red-500 text-xs mt-1">{errors.description}</div>}
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Follow Up Steps</Label>
              <Button 
                type="button" 
                variant="outline" 
                size="sm" 
                onClick={addFollowUpStep}
                className="gap-1"
              >
                <Plus className="h-4 w-4" />
                Add Step
              </Button>
            </div>
            
            <div className="space-y-2 max-h-[100px] overflow-y-auto">
              {followUpSteps.map((step, index) => (
                <div key={step.id} className="flex gap-2 items-start">
                  <div className="flex-1">
                    <Input
                      value={step.description}
                      onChange={(e) => updateFollowUpStep(step.id, 'description', e.target.value)}
                      placeholder="Enter follow up step"
                    />
                  </div>
                  <div className="w-40">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !step.dueDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {step.dueDate ? format(step.dueDate, "MM/dd/yy") : <span>Due date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={step.dueDate}
                          onSelect={(date) => date && updateFollowUpStep(step.id, 'dueDate', date)}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => removeFollowUpStep(step.id)}
                    className="text-red-500"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div className="border rounded-md p-4 bg-blue-50 text-blue-700">
          <p>
            Please log your Enquiry updates, Sample Updates and POs on{" "}
            <a 
              href="https://salesstack.mstack.co" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 underline font-medium"
            >
              SalesStack
            </a>
          </p>
        </div>
        
        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            {isEdit ? "Save Meeting" : "Add Meeting"}
          </Button>
        </div>
      </form>
    </div>
  );
}
