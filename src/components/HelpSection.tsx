import { Card } from "@/components/ui/card";
import { HelpCircle } from "lucide-react";
import Header from "@/components/Header";

interface HelpVideo {
  title: string;
  description: string;
  videoUrl: string;
}

const helpVideos: HelpVideo[] = [
  {
    title: "Enquiry Creation",
    description: "Creating a new enquiry",
    videoUrl: "https://drive.google.com/file/d/1V2vjIwxSoPVsIy9TioArVj8THrA1_QY1/preview",
  },
  {
    title: "Navigating CRM + Adding a new customer meeting",
    description: "Navigating CRM + Adding a new customer meeting",
    videoUrl: "https://drive.google.com/file/d/1exvlg96nevSdPg4I5h6eUUuyGUGdDKBU/preview",
  },
  {
    title: "Providing customer feedback on a delivered sample",
    description: "Providing customer feedback on a delivered sample",
    videoUrl: "https://drive.google.com/file/d/1sK8CFFy47DRVIEV--dSAc_fFtrsVscO5/preview",
  },
  {
    title: "Providing customer feedback on price quotation",
    description: "Providing customer feedback on price quotation",
    videoUrl: "https://drive.google.com/file/d/18DBu5J1ZpZAUDinn6ZHPGB51LwgF8EeR/preview",
  },
  {
    title: "Requesting a new sample",
    description: "Requesting a new sample",
    videoUrl: "https://drive.google.com/file/d/1dX46YvINiwtC_GoeGDCNfJglm7boxXqs/preview",
  },
  {
    title: "Uploading a new purchase order",
    description: "Uploading a new purchase order",
    videoUrl: "https://drive.google.com/file/d/19UX28U1wuCsFd1dFK3o7YQZbWyzOHI5t/preview",
  }
];

const HelpSection = () => {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-white via-blue-50 to-primary-50">
      <Header />
      <main className="flex-grow w-full pt-8 animate-fade-in">
        <div className="max-w-4xl mx-auto p-6">
          {/* Header */}
          <div className="flex items-center gap-3 mb-8">
            <div className="bg-primary/10 p-2 rounded-full">
              <HelpCircle className="h-6 w-6 text-primary" />
            </div>
            <h1 className="text-2xl font-semibold text-gray-900">Help Center</h1>
          </div>

          {/* Video Tutorials Section */}
          <div className="space-y-6">
            {/* <h2 className="text-xl font-medium text-gray-900 mb-4">Video Tutorials</h2> */}
            <div className="space-y-6">
              {helpVideos.map((video, index) => (
                <Card key={index} className="overflow-hidden">
                  <div className="aspect-video w-full">
                    <iframe
                      src={video.videoUrl}
                      className="w-full h-full border-0"
                      allow="autoplay"
                    />
                  </div>
                  <div className="p-6">
                    <h3 className="font-medium text-xl text-gray-900">{video.title}</h3>
                    <p className="text-base text-gray-600 mt-2">{video.description}</p>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default HelpSection;
