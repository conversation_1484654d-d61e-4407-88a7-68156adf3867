
import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from "@/integrations/supabase/client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useCustomers } from '@/hooks/use-customers';

// Sample project data
const projectsData = {
  'active-enquiries': [
    { id: 1, name: 'Chemical A Requirements', customer: 'Acme Corporation', value: '$25,000', status: 'In Progress', dueDate: '2025-05-15' },
    { id: 2, name: 'Raw Material Inquiry', customer: 'TechCorp Industries', value: '$18,000', status: 'New', dueDate: '2025-05-20' },
    { id: 3, name: 'Polymer Solution Quote', customer: 'Global Manufacturing', value: '$32,000', status: 'In Progress', dueDate: '2025-05-10' },
  ],
  'open-samples': [
    { id: 1, name: 'Chemical B Sample', customer: 'Acme Corporation', value: '$0', status: 'Shipped', dueDate: '2025-04-20' },
    { id: 2, name: 'Polymer X Test Batch', customer: 'TechCorp Industries', value: '$0', status: 'In Production', dueDate: '2025-04-25' },
    { id: 3, name: 'Specialized Catalyst', customer: 'Global Manufacturing', value: '$0', status: 'Pending Approval', dueDate: '2025-04-30' },
  ],
  'purchase-orders': [
    { id: 1, name: 'Chemical A Bulk Order', customer: 'Acme Corporation', value: '$48,000', status: 'Processing', dueDate: '2025-05-30' },
    { id: 2, name: 'Weekly Raw Materials', customer: 'TechCorp Industries', value: '$32,000', status: 'Shipped', dueDate: '2025-04-15' },
    { id: 3, name: 'Quarterly Chemical Supply', customer: 'Global Manufacturing', value: '$65,000', status: 'Pending', dueDate: '2025-06-01' },
  ]
};

export const ProjectDetail = () => {
  const { type } = useParams<{ type: string }>();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const { customers, loading, error } = useCustomers()
  
  // Normalize type parameter to match our data keys
  const normalizedType = type?.toLowerCase().replace(' ', '-') || '';
  
  // Get the title based on type
  const getTitle = () => {
    switch (normalizedType) {
      case 'active-enquiries':
        return 'Active Enquiries';
      case 'open-samples':
        return 'Open Samples';
      case 'purchase-orders':
        return 'Purchase Orders';
      default:
        return 'Projects';
    }
  };
  
  // Get data based on normalized type
  const getData = () => {
    return projectsData[normalizedType as keyof typeof projectsData] || [];
  };
  
  // Filter data based on search query
  const filteredData = getData().filter(item => 
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.customer.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Button 
          variant="ghost" 
          size="sm" 
          className="mr-2"
          onClick={() => navigate(-1)}
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">{getTitle()}</h1>
      </div>
      
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <CardTitle>All {getTitle()}</CardTitle>
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Value</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Due Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.length > 0 ? (
                filteredData.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">{item.name}</TableCell>
                    <TableCell>{item.customer}</TableCell>
                    <TableCell>{item.value}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        item.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                        item.status === 'New' ? 'bg-green-100 text-green-800' :
                        item.status === 'Shipped' ? 'bg-purple-100 text-purple-800' :
                        item.status === 'Processing' ? 'bg-orange-100 text-orange-800' :
                        item.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {item.status}
                      </span>
                    </TableCell>
                    <TableCell>{new Date(item.dueDate).toLocaleDateString()}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                    No results found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
     

          {loading ? (
            <p>Loading customers...</p>
          ) : (
            <ul>
              {customers.map(customer => (
                <li key={customer.id}>{customer.name}</li>
              ))}
            </ul>
          )}
    </div>
  );
  
};
