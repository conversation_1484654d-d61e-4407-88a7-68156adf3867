
import { Modal } from "antd";
import { But<PERSON> } from "@/components/ui/button";
import { Mail, LogOut, FilePlus, HelpCircle, ArrowLeft } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/use-toast";
import { useQuery } from "@tanstack/react-query";
import { useLocation, useNavigate } from "react-router-dom";
import { useState } from "react";
import CreatePurchaseOrderModal from "@/components/stats/enquiries/components/CreatePurchaseOrderModal"
import PurchaseOrderForm from "@/components/stats/enquiries/components/PurchaseOrderForm";
import {
  She<PERSON>,
  <PERSON>et<PERSON>ontent,
  SheetTrigger,
} from "@/components/ui/sheet"
import { Menu } from "lucide-react"
import HelpSection from "@/components/HelpSection";

const Header = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const isAuthPage = location.pathname === "/auth";
  const isHelpPage = location.pathname === "/help";

  const { data: session } = useQuery({
    queryKey: ['auth-session'],
    queryFn: async () => {
      const { data: { session } } = await supabase.auth.getSession();
      return session;
    },
  });

  const handleSignOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      localStorage.clear()
      if (error) throw error;
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to sign out. Please try again.",
      });
    }
  };

  const scrollToEnquiryWidget = () => {
    const enquiryWidget = document.querySelector('[data-enquiry-widget]');
    if (enquiryWidget) {
      enquiryWidget.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const [showPOModal, setShowPOModal] = useState(false);

  const purchaseOrderClick = () => {
    setShowPOModal(true);
  };

  const goToHelpCenter = () => {
    navigate('/help');  // Assuming '/help' is your help section route
    // Close the sheet/burger menu if needed
  };

  const menuContent = (
    <nav className="flex flex-col gap-4">
      <div className="px-2 py-4">
        <img 
          src="/lovable-uploads/ad13b7d1-ebd6-43a9-a6e1-aa5027505965.png" 
          alt="Logo" 
          className="h-8 w-auto object-contain"
        />
      </div>
      
      {/* Menu Items */}
      <div className="flex flex-col space-y-3">
        <Button
          variant="ghost"
          className="justify-start"
          onClick={() => {
            // Add navigation logic
          }}
        >
          <FilePlus className="mr-2 h-5 w-5" />
          Create Enquiry
        </Button>

        <Button
          variant="ghost"
          className="justify-start"
          onClick={purchaseOrderClick}
        >
          <FilePlus className="mr-2 h-5 w-5" />
          Upload Purchase Order
        </Button>

        <Button
          variant="ghost"
          className="justify-start"
          onClick={goToHelpCenter}
        >
          <HelpCircle className="mr-2 h-5 w-5" />
          Help Center
        </Button>

        {session?.user?.email && (
          <Button
            variant="ghost"
            className="justify-start"
          >
            <Mail className="mr-2 h-5 w-5" />
            {session.user.email}
          </Button>
        )}

        <Button
          variant="ghost"
          className="justify-start text-red-500 hover:text-red-600 hover:bg-red-50"
          onClick={handleSignOut}
        >
          <LogOut className="mr-2 h-5 w-5" />
          Sign Out
        </Button>
      </div>
    </nav>
  );

  return (
    <>
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-[1500px] mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center gap-8">
            {/* Mobile Menu (Burger) - Only show on mobile */}
            <Sheet>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="md:hidden"
                >
                  <Menu className="h-6 w-6" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[300px] sm:w-[400px]">
                {menuContent}
              </SheetContent>
            </Sheet>

            {/* Back Button - Only show on Help page */}
            {isHelpPage && (
              <Button 
                variant="ghost" 
                onClick={() => navigate(-1)}
                className="hover:bg-primary/10 hidden md:flex"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Home
              </Button>
            )}

            {/* Logo and Title */}
            <div className="flex items-center gap-3">
              <img 
                src="/lovable-uploads/ad13b7d1-ebd6-43a9-a6e1-aa5027505965.png" 
                alt="Logo" 
                className="h-8 w-auto object-contain animate-fade-in"
              />
              <div>
                <h1 className="text-xl font-semibold text-gradient">Sales-Stack</h1>
              </div>
            </div>
          </div>
          {!isAuthPage && session && (
            <div className="hidden md:flex items-center gap-4">
              <Button
                onClick={purchaseOrderClick}
                variant="default"
                className="flex items-center gap-2 bg-[#294d48] hover:bg-[#294d48]/90 text-white shadow-md animate-fade-in"
              >
                <FilePlus className="h-5 w-5" />
                Upload Purchase Order
              </Button>
              <Button
                variant="outline"
                className="hidden sm:flex items-center gap-2 border-[#294d48]/20 text-[#294d48] hover:bg-[#294d48]/10 transition-all duration-300"
              >
                <Mail className="h-5 w-5" />
                {session.user?.email}
              </Button>
              <Button
                variant="ghost"
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-all duration-300"
                onClick={goToHelpCenter}
              >
                <HelpCircle className="h-5 w-5" />
                Help
              </Button>
              <Button
                variant="ghost"
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-all duration-300"
                onClick={handleSignOut}
              >
                <LogOut className="h-5 w-5" />
                Sign Out
              </Button>
            </div>
          )}
        </div>
      </header>
      <CreatePurchaseOrderModal
        open={showPOModal}
        onClose={() => setShowPOModal(false)}
        onSuccess={() => {
          setShowPOModal(false);
          toast({
            title: "Success",
            description: "Purchase order created successfully",
          });
        }}
      />
    </>
  );
};

export default Header;
