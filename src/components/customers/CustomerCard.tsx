{customer?.customer_enquiries?.length > 0 && (
  <div className="flex items-center gap-1.5 mb-1">
    <FileSearch
      size={14}
      className="text-blue-600 shrink-0"
    />
    <span className="truncate">
      {customer.customer_enquiries
        .filter((enquiry: any) => [
          "enquiry_created",
          "enquiry_assigned",
          "clarification_needed",
          "pricing_quotation_generated"
        ].includes(enquiry.current_status))
        .map((enquiry: any) => enquiry.chemical_name)
        .join(", ")}
    </span>
  </div>
)}