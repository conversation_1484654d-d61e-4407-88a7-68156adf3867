import { useEffect, useState } from "react";
import {
  Search,
  Plus,
  ChevronDown,
  ChevronUp,
  X,
  Users,
  Calendar,
  FileSearch,
  Package,
  FilePlus,
  FileX,
  Loader2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { AddCustomerForm } from "@/components/customers/AddCustomerForm";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useCustomers } from "@/hooks/use-customers";
import { supabase } from "@/integrations/supabase/client";
import { c } from "node_modules/framer-motion/dist/types.d-6pKw1mTI";
import { useQuarterlyMetrics, QuarterlyMetrics } from "@/hooks/use-quarterly-metrics";
import { useQueryClient } from "@tanstack/react-query";
import { formatEmailToName } from "@/utils/format";
import { toast } from "sonner";

export type ContactEntry = {
  id?: string; // Optional id field for existing contacts
  name: string;
  role: string;
  email: string;
  phone: string;
};

export type Customer = {
  id: number;
  customer_full_name: string;
  city: string;
  country: string;
  contacts: ContactEntry[];
  industries: string[];
  expectedQuarterlyProcurement: string;
  annual_chemicals_procurement: string;
  account_owner: string;
  activeEnquiries: string[];
  openSamples: string[];
  openPOs: string[];
  noActivity: {
    name: string;
    offset_chemical_id: string | null;
    offset_chemical_name: string | null;
  }[];
  targetQuarterly: string;
  revenueQuarterly: string;
  procurementMapped: number;
  shareOfWallet: number;
  repeatRate: number;
  industry: string;
  website: string;
  contactPerson: string;
  contactEmail: string;
  contactPhone: string;
  turnover: string;
  ytdOrderbook: string;
  ytdRevenue: string;
  marginPercentage: number;
  notes: string;
  targetQuarterlyPrev: string;
  revenueQuarterlyPrev: string;
  procurementMappedPrev: number;
  shareOfWalletPrev: number;
  repeatRatePrev: number;
  projects: { enquiries: number; samples: number; pos: number };
  chemicals: {
    name: string;
    offset_chemical_id: string | null;
    offset_chemical_name: string | null;
  }[];
  annualRevenue: string;
};

type SortField =
  | "name"
  | "targetQuarterly"
  | "revenueQuarterly"
  | "procurementMapped"
  | "shareOfWallet"
  | "repeatRate";
type SortDirection = "asc" | "desc";
type QuarterFilter = "current" | "previous";

const getQuarterDateRange = (quarter: QuarterFilter) => {
  const now = new Date();
  const currentQuarter = Math.floor(now.getMonth() / 3);
  const currentYear = now.getFullYear();
  
  let startDate, endDate;
  
  if (quarter === 'current') {
    startDate = new Date(currentYear, currentQuarter * 3, 1);
    endDate = new Date(currentYear, (currentQuarter + 1) * 3, 0);
  } else {
    // Previous quarter
    const prevQuarter = currentQuarter === 0 ? 3 : currentQuarter - 1;
    const prevYear = currentQuarter === 0 ? currentYear - 1 : currentYear;
    startDate = new Date(prevYear, prevQuarter * 3, 1);
    endDate = new Date(prevYear, (prevQuarter + 1) * 3, 0);
  }
  
  return { startDate, endDate };
};

const isActivityInQuarter = (date: string, quarter: QuarterFilter) => {
  const activityDate = new Date(date);
  const { startDate, endDate } = getQuarterDateRange(quarter);
  return activityDate >= startDate && activityDate <= endDate;
};

export const CustomerList = ({
  onSelectCustomer,
}: {
  onSelectCustomer: (id: number, name?: string, customerId?: string) => void;
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [sortField, setSortField] = useState<SortField>("name");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");
  const [showAddForm, setShowAddForm] = useState(false);
  const [customers, setCustomers] = useState<any>([]);
  const [quarterFilter, setQuarterFilter] = useState<QuarterFilter>("current");
  const [isAdmin, setIsAdmin] = useState(false);
  const [selectedOwners, setSelectedOwners] = useState<string[]>([]);
  const [allAccountOwners, setAllAccountOwners] = useState<string[]>([]);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const queryClient = useQueryClient();

  const { customers: dbCustomers, loading: customersLoading, error: customersError, refetch: refetchCustomers } = useCustomers();
  const { metrics, loading: metricsLoading, error: metricsError } = useQuarterlyMetrics(quarterFilter);

  useEffect(() => {
    async function fetchUser() {
      try {
        const user = {
          email: localStorage.getItem('userEmail'),
          id: localStorage.getItem('userId'),
          role: localStorage.getItem('userRole'),
          country: localStorage.getItem('userCountry'),
          category: localStorage.getItem('userCategory')
        }
        if (user?.email) {
          setCurrentUser(user);
          setIsAdmin(user?.role === 'admin' || user?.role === 'bu_head');
          
          // If sales, set selected owner to current user
          if (user?.role === 'sales') {
            setSelectedOwners([user.email]);
          }
        }
      } catch (error) {
        console.error("Error fetching user:", error);
      }
    }
    fetchUser();
  }, []);

  useEffect(() => {
    // Merge quarterly metrics with customer data
    const customersWithMetrics = dbCustomers.map((customer) => {
      const customerMetrics = metrics.find(
        (metric) => metric.sales_stack_customer_id === customer.id.toString()
      );

      return {
        ...customer,
        targetQuarterly: customerMetrics?.sum_target ? `$${customerMetrics.sum_target.toLocaleString()}` : "N/A",
        revenueQuarterly: customerMetrics?.total_revenue ? `$${customerMetrics.total_revenue?.toLocaleString() || "N/A"}` : "N/A",
        procurementMapped: customerMetrics?.procurement_percentage ? `${customerMetrics.procurement_percentage}%` : "N/A",
        shareOfWallet: customerMetrics?.share_wallet ? `${customerMetrics.share_wallet}%` : "N/A",
        repeatRate: customerMetrics?.repeated_rate ? `${customerMetrics.repeated_rate}%` : "N/A",
      };
    });

    setCustomers(customersWithMetrics);
    // Set all account owners from the customers data
    const owners = Array.from(
      new Set(dbCustomers.map((customer) => customer.account_owner))
    ).sort();
    setAllAccountOwners(owners);
  }, [dbCustomers, metrics]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field)
      return <ChevronDown size={16} className="opacity-30" />;
    return sortDirection === "asc" ? (
      <ChevronUp size={16} />
    ) : (
      <ChevronDown size={16} />
    );
  };

  const handleAddCustomer = async (newCustomer: Omit<Customer, "id">) => {
    try {
      const customerData = {
        customer_full_name: newCustomer.customer_full_name,
        customer_company: newCustomer.customer_full_name, // Using full name as company name
        customer_phone: "",
        customer_email:  "",
        account_owner: currentUser?.email || "",
        address:"",
        city: newCustomer.city || "", // Not available in the form
        state:"", // Not available in the form
        country: newCustomer.country || "", // Not available in the form
        // type: "", // Not available in the form
        // size: "",
        created_by: currentUser?.id,
        industries: newCustomer.industries || [],
        postal_code: "", // Not available in the form
        customer_poc: "",
        annual_turnover: newCustomer.annualRevenue || "0-25",
        annual_chemicals_procurement:
          parseFloat(
            newCustomer.expectedQuarterlyProcurement?.replace(/[$,]/g, "") ||
              "0"
          ) * 4,
        created_at: new Date().toISOString(),
        modified_at: new Date().toISOString(),
      };
      const { data, error } = await supabase
        .from("customer")
        .insert(customerData)
        .select();

      if (error) {
        // Check if it's a 409 conflict error (duplicate customer name)
        if (error.code === '23505' || error.message?.includes('duplicate') || error.message?.includes('already exists')) {
          throw new Error(`A customer with the name "${newCustomer.customer_full_name}" already exists. Kindly reach out to the IT team for further help.`);
        }
        throw error;
      }

      // Get the newly created customer ID
      const newCustomerId = data[0].id;

      // Create entries in customer_projects table for each chemical
      // if (newCustomer.chemicals && newCustomer.chemicals.length > 0) {
      //   const projectEntries = newCustomer.chemicals.map((chemical) => ({
      //     customer_id: newCustomerId,
      //     chemical_name: chemical.name,
      //     offset_chemical_id: chemical.offset_chemical_id,
      //     offset_chemical_name: chemical.offset_chemical_name,
      //     project_type: "NO_ACTIVITY" as
      //       | "ENQUIRY"
      //       | "SAMPLE"
      //       | "PURCHASE_ORDER"
      //       | "NO_ACTIVITY",
      //   }));

      //   const { error: projectsError } = await supabase
      //     .from("customer_projects")
      //     .insert(projectEntries);

      //   if (projectsError) {
      //     console.error("Error adding customer projects:", projectsError);
      //   }
      // }

      // Create entries in account_planning table for each chemical
      if (newCustomer.chemicals && newCustomer.chemicals.length > 0) {
        const planningEntries = newCustomer.chemicals.map((chemical) => ({
          customer_id: newCustomerId,
          chemical_name: chemical.name,
          offset_chemical_id: chemical.offset_chemical_id,
          offset_chemical_name: chemical.offset_chemical_name,
          grade: "", // Default value
          quarterly_volume: 0,
          status: "Cold - No Response",
          quarterly_value: 0,
          previous_quarter_value: 0,
          target_this_quarter: 0,
          conversion_probability: 0,
          comments: "",
        }));

        const { error: planningError } = await supabase
          .from("account_planning")
          .insert(planningEntries);

        if (planningError) {
        }
      }

      if(newCustomer.contacts && newCustomer.contacts.length > 0) {
        const contactEntries = newCustomer.contacts.map((contact) => ({
          customer_id: newCustomerId,
          name: contact.name,
          email: contact.email,
          role: contact.role,
          phone: contact.phone,
          is_primary: contact.role === "sales" || false,
        }));

        const { error: contactsError } = await supabase
          .from("customer_contact")
          .insert(contactEntries);

        if (contactsError) {
        }
      }

      // Invalidate queries and refetch data
      await queryClient.invalidateQueries({ queryKey: ["customers"] });
      await queryClient.invalidateQueries({ queryKey: ["quarterly-metrics"] });
      
      // Immediately refetch both queries
      await Promise.all([
        refetchCustomers(),
      ]);

      // Show success toast
      toast.success(`Customer "${newCustomer.customer_full_name}" added successfully!`);
      setShowAddForm(false);
    } catch (error) {
      console.error("Error adding customer:", error);
      // Show error toast
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("Failed to add customer. Please try again.");
      }
      // Re-throw the error so it can be handled by the form component
      throw error;
    }
  };

  const handleOwnerFilter = (owner: string) => {
    if (!isAdmin) return; // Don't allow changes if not admin
    setSelectedOwners((prev) =>
      prev.includes(owner) ? prev.filter((o) => o !== owner) : [...prev, owner]
    );
  };

  const resetFilters = () => {
    setSelectedOwners([]);
    setSearchQuery("");
    setQuarterFilter("current");
  };
  
  const sortedCustomers = [...customers]?.sort((a, b) => {
    if (sortField === "name") {
      return sortDirection === "asc"
        ? a?.customer_full_name?.localeCompare(b?.customer_full_name)
        : b?.customer_full_name?.localeCompare(a?.customer_full_name);
    }

    if (sortField === "targetQuarterly" || sortField === "revenueQuarterly") {
      const fieldName =
        quarterFilter === "current" ? sortField : `${sortField}Prev`;
      const valueA = parseInt(
        (a[fieldName as keyof Customer] as string).replace(/[$,]/g, "")
      );
      const valueB = parseInt(
        (b[fieldName as keyof Customer] as string).replace(/[$,]/g, "")
      );
      return sortDirection === "asc" ? valueA - valueB : valueB - valueA;
    }

    const fieldName =
      quarterFilter === "current" ? sortField : `${sortField}Prev`;
    return sortDirection === "asc"
      ? (a[fieldName as keyof Customer] as number) -
          (b[fieldName as keyof Customer] as number)
      : (b[fieldName as keyof Customer] as number) -
          (a[fieldName as keyof Customer] as number);
  });

  const filteredCustomers = sortedCustomers.filter((customer) => {
    const matchesSearch = customer?.customer_full_name
      ?.toLowerCase()
      .includes(searchQuery.toLowerCase());

    const matchesOwner =
      selectedOwners.length === 0 ||
      selectedOwners.includes(customer.account_owner);

    return matchesSearch && matchesOwner;
  });

  const getOwnerFilterText = () => {
    if (selectedOwners?.length === 0) return "All Account Owners";
    if (selectedOwners?.length === 1) return selectedOwners[0];
    return `${selectedOwners.length} Owners`;
  };

  const getFilteredActivities = (customer: any) => {
    const { startDate, endDate } = getQuarterDateRange(quarterFilter);
    
    // Filter enquiries
    const filteredEnquiries = customer?.customer_enquiries?.filter((enquiry: any) => {
      const isInQuarter = isActivityInQuarter(enquiry.created_at, quarterFilter);
      const isActiveStatus = [
        "enquiry_created",
        "enquiry_assigned",
        "clarification_needed",
        "quote_redo",
        "pricing_quotation_generated",
      ].includes(enquiry.current_status);
      return isInQuarter && isActiveStatus;
    }) || [];

    // Filter sample requests
    const filteredSamples = customer?.customer_enquiries?.filter((enquiry: any) => {
      const hasSamples = enquiry.sample_requests && enquiry.sample_requests.length > 0;
      if (!hasSamples) return false;
      
      // Check if any sample request is in the current quarter
      return enquiry.sample_requests.some((sample: any) => 
        isActivityInQuarter(sample.created_at, quarterFilter)
      );
    }) || [];

    // Filter purchase orders
    const filteredPOs = customer?.customer_enquiries?.filter((enquiry: any) => {
      const hasPOs = enquiry.purchase_orders && enquiry.purchase_orders.length > 0;
      if (!hasPOs) return false;
      
      // Check if any purchase order is in the current quarter
      return enquiry.purchase_orders.some((po: any) => 
        isActivityInQuarter(po.created_at, quarterFilter)
      );
    }) || [];

    const filteredActivities = customer?.planning?.filter((planning: any) => {
      return planning.status === "Cold - No Response" && 
        isActivityInQuarter(planning.created_at, quarterFilter);
    }) || [];

    return {
      enquiries: filteredEnquiries,
      samples: filteredSamples,
      purchaseOrders: filteredPOs,
      noActivity: filteredActivities,
    };
  };


  return (
    <div className="flex flex-col h-full">
      {showAddForm ? (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Add New Customer</h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowAddForm(false)}
            >
              <X size={20} />
            </Button>
          </div>
          <AddCustomerForm
            onSubmit={handleAddCustomer}
            onCancel={() => setShowAddForm(false)}
          />
        </div>
      ) : (
        <>
          <div className="bg-white border rounded-lg p-4 mb-4 shadow-sm">
            <div className="flex flex-wrap items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                <h3 className="text-lg font-medium">Customers View</h3>
              </div>

              <Button
                size="sm"
                onClick={() => setShowAddForm(true)}
                className="ml-auto"
              >
                <Plus size={16} className="mr-2" />
                Add Customer
              </Button>

              <Button
                variant="outline"
                className="text-sm"
                onClick={resetFilters}
                disabled={
                  selectedOwners.length === 0 &&
                  searchQuery === "" &&
                  quarterFilter === "current"
                }
              >
                Clear Filters
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
              <div className="relative w-full">
                <Search
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
                  size={16}
                />
                <Input
                  placeholder="Search customers..."
                  className="pl-9 w-full"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <Popover>
                <PopoverTrigger asChild>
                  <Button 
                    variant="outline" 
                    className="w-full justify-between"
                    disabled={!isAdmin}
                  >
                    <span>{getOwnerFilterText()}</span>
                    <ChevronDown size={16} />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0" align="start">
                  <div className="p-2 z-100 bg-white border rounded-lg shadow-lg">
                    <div className="font-medium px-2 py-1.5">
                      Account Owners
                    </div>
                    <div className="max-h-[300px] overflow-y-auto p-2">
                      {allAccountOwners.map((owner) => (
                        <div
                          key={owner}
                          className="flex items-center space-x-2 py-1"
                        >
                          <Checkbox
                            id={`owner-${owner}`}
                            checked={selectedOwners.includes(owner)}
                            onCheckedChange={() => handleOwnerFilter(owner)}
                            disabled={!isAdmin}
                          />
                          <Label
                            htmlFor={`owner-${owner}`}
                            className="text-sm font-normal whitespace-nowrap"
                          >
                            {formatEmailToName(owner)}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="mb-2 flex items-center gap-2 text-xs text-muted-foreground">
            <span className="font-medium mr-1">Legend:</span>
            <div className="flex items-center gap-1">
              <FileSearch size={14} className="text-blue-600" />
              <span>Enquiries</span>
            </div>
            <div className="flex items-center gap-1 ml-3">
              <FilePlus size={14} className="text-green-600" />
              <span>Samples</span>
            </div>
            <div className="flex items-center gap-1 ml-3">
              <Package size={14} className="text-purple-600" />
              <span>Purchase Orders</span>
            </div>
            <div className="flex items-center gap-1 ml-3">
              <FileX size={14} className="text-red-500" />
              <span>No Current Activity</span>
            </div>
          </div>
        </>
      )}

      {!showAddForm && (
        <div className="border rounded-md overflow-hidden flex-1">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-muted/50">
                  <th
                    colSpan={selectedOwners.length === 1 ? 1 : 2}
                    className="text-center py-3 px-4 text-18x font-semibold"
                  >
                    Basic Information
                  </th>
                  <th
                    colSpan={1}
                    className="text-center py-3 px-4 text-18x font-semibold border-l border-dashed border-gray-300"
                  >
                    Projects
                  </th>
                  <th
                    colSpan={5}
                    className="text-center py-3 px-4 text-18x font-semibold border-l border-dashed border-gray-300"
                  >
                    <div className="flex items-center justify-between">
                      <span>Metrics</span>
                      <Select
                        value={quarterFilter}
                        onValueChange={(value) =>
                          setQuarterFilter(value as QuarterFilter)
                        }
                      >
                        <SelectTrigger className="h-7 text-xs bg-white w-48">
                          <SelectValue placeholder="Select Quarter" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="current">
                            Current Quarter (Q)
                          </SelectItem>
                          <SelectItem value="previous">
                            Previous Quarter (Q-1)
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </th>
                </tr>
                <tr className="bg-muted/30">
                  <th
                    className="text-left py-3 px-4 text-14x font-bold text-muted-foreground cursor-pointer"
                    onClick={() => handleSort("name")}
                  >
                    <div className="flex items-center">
                      Customer Name
                      {getSortIcon("name")}
                    </div>
                  </th>
                  {selectedOwners.length !== 1 && (
                    <th className="text-left py-3 px-4 text-14x font-bold text-muted-foreground border-r border-dashed border-gray-300">
                      Account Owner
                    </th>
                  )}
                  <th className="text-left py-3 px-4 text-14x font-bold text-muted-foreground border-l border-dashed border-gray-300 border-r border-dashed border-gray-300">
                    <div className="flex items-center">Projects</div>
                  </th>
                  <th
                    className="text-left py-3 px-4 text-14x font-bold text-muted-foreground cursor-pointer border-l border-dashed border-gray-300"
                    onClick={() => handleSort("targetQuarterly")}
                  >
                    <div className="flex items-center">
                      Target
                      {getSortIcon("targetQuarterly")}
                    </div>
                  </th>
                  <th
                    className="text-left py-3 px-4 text-14x font-bold text-muted-foreground cursor-pointer"
                    onClick={() => handleSort("revenueQuarterly")}
                  >
                    <div className="flex items-center">
                      Revenue (E)
                      {getSortIcon("revenueQuarterly")}
                    </div>
                  </th>
                  <th
                    className="text-left py-3 px-4 text-14x font-bold text-muted-foreground cursor-pointer"
                    onClick={() => handleSort("procurementMapped")}
                  >
                    <div className="flex items-center">
                      Proc. Mapped (%)
                      {getSortIcon("procurementMapped")}
                    </div>
                  </th>
                  <th
                    className="text-left py-3 px-4 text-14x font-bold text-muted-foreground cursor-pointer"
                    onClick={() => handleSort("shareOfWallet")}
                  >
                    <div className="flex items-center">
                      Share of Wallet (%)
                      {getSortIcon("shareOfWallet")}
                    </div>
                  </th>
                  <th
                    className="text-left py-3 px-4 text-14x font-bold text-muted-foreground cursor-pointer"
                    onClick={() => handleSort("repeatRate")}
                  >
                    <div className="flex items-center">
                      Repeat Rate (%)
                      {getSortIcon("repeatRate")}
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {(customersLoading) ? (
                  <tr>
                    <td colSpan={8}>
                      <div className="flex justify-center py-8">
                        <div className="flex flex-col items-center gap-2">
                          <Loader2 className="h-6 w-6 animate-spin" />
                          <p className="text-sm text-muted-foreground">Loading customer data...</p>
                        </div>
                      </div>
                    </td>
                  </tr>
                ) : customersError ? (
                  <tr>
                    <td colSpan={8}>
                      <div className="flex justify-center py-4">
                        <p className="text-red-500">Error loading data</p>
                      </div>
                    </td>
                  </tr>
                ) : !filteredCustomers || filteredCustomers.length === 0 ? (
                  <tr>
                    <td colSpan={8}>
                      <div className="flex justify-center py-8">
                        <div className="flex flex-col items-center gap-2">
                          <p className="text-muted-foreground">No customers exist</p>
                          <Button 
                            variant="outline" 
                            onClick={() => setShowAddForm(true)}
                            className="mt-2"
                          >
                            <Plus className="mr-2 h-4 w-4" /> Add Customer
                          </Button>
                        </div>
                      </div>
                    </td>
                  </tr>
                ) : filteredCustomers.map((customer) => {
                  const filteredActivities = getFilteredActivities(customer);
                  return (
                    <tr
                      key={customer.id}
                      className="border-t hover:bg-muted/20 cursor-pointer transition-colors"
                      onClick={() =>
                        onSelectCustomer(
                          customer?.id,
                          customer?.customer_full_name,
                          customer?.customer_id
                        )
                      }
                    >
                      <td className="py-3 px-4 text-sm">
                        <div className="font-semibold">
                          {customer?.customer_full_name}
                        </div>
                      </td>
                      {selectedOwners.length !== 1 && (
                        <td className="py-3 px-4 text-sm border-r border-dashed border-gray-300">
                          <div className="flex items-center whitespace-nowrap">
                            <Users
                              size={14}
                              className="mr-2 text-muted-foreground"
                            />
                            {formatEmailToName(customer.account_owner)}
                          </div>
                        </td>
                      )}
                      <td className="py-3 px-4 text-sm border-l border-dashed border-gray-300 border-r border-dashed border-gray-300 max-w-[350px]">
                        <div>
                          {/* Enquiries */}
                          {filteredActivities.enquiries.length > 0 && (
                            <div className="flex items-center gap-1.5 mb-1">
                              <FileSearch
                                size={14}
                                className="text-blue-600 shrink-0"
                              />
                              <span className="truncate">
                                {(() => {
                                  const chemicals = filteredActivities.enquiries
                                    .map((enquiry: any) => enquiry.chemical_name);
                                  
                                  return chemicals.length <= 3 
                                    ? chemicals.join(", ")
                                    : `${chemicals.slice(0, 3).join(", ")}...`;
                                })()}
                              </span>
                            </div>
                          )}

                          {/* Sample Requests */}
                          {filteredActivities.samples.length > 0 && (
                            <div className="flex items-center gap-1.5 mb-1">
                              <FilePlus
                                size={14}
                                className="text-green-600 shrink-0"
                              />
                              <span className="truncate">
                                {(() => {
                                  const chemicals = filteredActivities.samples
                                    .map((enquiry: any) => enquiry.chemical_name);
                                  
                                  return chemicals.length <= 3 
                                    ? chemicals.join(", ")
                                    : `${chemicals.slice(0, 3).join(", ")}...`;
                                })()}
                              </span>
                            </div>
                          )}

                          {/* Purchase Orders */}
                          {filteredActivities.purchaseOrders.length > 0 && (
                            <div className="flex items-center gap-1.5 mb-1">
                              <Package size={14} className="text-purple-600 shrink-0" />
                              <span className="truncate">
                                {(() => {
                                  const chemicals = filteredActivities.purchaseOrders
                                    .map((enquiry: any) => enquiry.chemical_name);
                                  
                                  return chemicals.length <= 3 
                                    ? chemicals.join(", ")
                                    : `${chemicals.slice(0, 3).join(", ")}...`;
                                })()}
                              </span>
                            </div>
                          )}

                          {/* No Current Activity */}
                          {filteredActivities.noActivity.length > 0 && (
                            <div className="flex items-center gap-1.5 mb-1">
                              <FileX size={14} className="text-red-600 shrink-0" />
                              <span className="truncate">
                                {(() => {
                                  const chemicals = filteredActivities.noActivity
                                    .map((enquiry: any) => enquiry.chemical_name);
                                  
                                  return chemicals.length <= 3 
                                    ? chemicals.join(", ")
                                    : `${chemicals.slice(0, 3).join(", ")}...`;
                                })()}
                              </span>
                            </div>
                          )}
                        </div>
                      </td>
                      {metricsLoading || !customer.targetQuarterly ? (
                        <td colSpan={5} className="py-3 px-4 text-sm">
                          <div className="flex justify-center">
                            <Loader2 className="h-4 w-4 animate-spin" />
                          </div>
                        </td>
                      ) : (
                        <>
                          <td className="py-3 px-4 text-sm border-l border-dashed border-gray-300">
                            {customer?.targetQuarterly}
                          </td>
                          <td className="py-3 px-4 text-sm">
                            {customer?.revenueQuarterly}
                          </td>
                          <td className="py-3 px-4 text-sm">
                            {customer?.procurementMapped}
                          </td>
                          <td className="py-3 px-4 text-sm">
                            {customer?.shareOfWallet}
                          </td>
                          <td className="py-3 px-4 text-sm">
                            {customer?.repeatRate}
                          </td>
                        </>
                      )}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};
