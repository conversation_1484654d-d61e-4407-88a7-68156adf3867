
import { Card } from "@/components/ui/card";
import { Check } from "lucide-react";

interface KeyValueCardProps {
  title: string;
  items: { key: string; value: string }[];
}

const KeyValueCard = ({ title, items }: KeyValueCardProps) => {
  return (
    <Card className="p-6 bg-white border-primary-100">
      <h3 className="text-lg font-semibold mb-4 text-primary-600">{title}</h3>
      <div className="space-y-3">
        {items.map((item, index) => (
          <div key={index} className="flex items-start gap-2">
            <Check className="h-5 w-5 text-primary-500 mt-0.5 flex-shrink-0" />
            <div>
              <span className="font-medium text-gray-700">{item.key}: </span>
              <span className="text-gray-600">{item.value}</span>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default KeyValueCard;
