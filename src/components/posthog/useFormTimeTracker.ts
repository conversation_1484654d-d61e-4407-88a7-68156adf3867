import { useEffect, useRef } from "react";
import { usePostHog } from "posthog-js/react";

const useFormTimeTracker = (formName = "Unnamed Form") => {
  const posthog = usePostHog();
  const formStartTimeRef = useRef(null);
  const hasEnded = useRef(false);
  const hasStarted = useRef(false);
  const userName = localStorage.getItem("userEmail")

  const startTracking = () => {
    if (!hasStarted.current) {
      formStartTimeRef.current = Date.now();
      hasStarted.current = true;
      console.log(`[Form Tracker] Started tracking for "${formName}" at ${new Date().toLocaleTimeString()}`);
    }
  };

  const endTracking = (submitted = true,selectedOption = "",additionalInfo = {}) => {
    if (hasStarted.current && userName) {
      const durationMs = Date.now() - formStartTimeRef.current;
      const durationMinutes = Math.round((durationMs / (1000 * 60)) * 100) / 100;
      
      posthog.capture("form_filled", {
        form_name: formName,
        time_taken_minutes: durationMinutes,
        submitted,
        email: userName,
        formStartTimeRef:formStartTimeRef.current,
        order_type : selectedOption,
        ...additionalInfo
      });

      console.log(`[Form Tracker] "${formName}" completed. Time: ${durationMinutes} minutes`);
      hasEnded.current = true;
      hasStarted.current = false;
    }
  };

  // Cleanup on unmount (abandoned form)
  useEffect(() => {
    return () => {
      if (hasStarted.current && userName) {
        const durationMs = Date.now() - formStartTimeRef.current;
        const durationMinutes = Math.round(durationMs / (1000 * 60));

        posthog.capture("form_filled", {
          form_name: formName,
          time_taken_minutes: durationMinutes,
          submitted: false,
          email: userName,
          formStartTimeRef : formStartTimeRef.current,
          order_type: ""
        });

        console.log(`[Form Tracker] "${formName}" abandoned after ${durationMinutes} minutes`);
      }
    };
  }, [formName, posthog, userName]);

  return { startTracking, endTracking };
};

export default useFormTimeTracker;