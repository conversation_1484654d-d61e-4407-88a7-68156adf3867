import { usePostHog } from "posthog-js/react";

const useClickTracker = () => {
  const posthog = usePostHog();
  const user =localStorage.getItem("userEmail")

  const trackClick = (eventName = "button_clicked", metadata = {}) => {
    if (posthog && user) {
      posthog.capture(eventName, {
        email: user,
        timestamp: new Date().toISOString(),
        ...metadata,
      });
      console.log(`[Click Tracker] Event '${eventName}' sent`, metadata);
    }
  };

  return { trackClick };
};

export default useClickTracker;