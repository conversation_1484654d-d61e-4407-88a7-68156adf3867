import { EnvKeys } from "@/config/constants";
import { supabase } from "@/integrations/supabase/client";
import { CloudFog } from "lucide-react";
import { usePostHog } from "posthog-js/react";
import { useEffect, useRef, useState } from "react";
// import { useSelector } from 'react-redux';
import { useLocation } from "react-router-dom";
// import EnvKeys from '../../constants/EnviornmentKeys';
// import { handlePageName } from './utils';

const INACTIVITY_LIMIT = 5 * 60 * 1000; // 5 minutes in ms

const getValidActiveDuration = ({
  isTabVisible,
  lastActivityTime,
  lastVisibilityChange,
}) => {
  const now = Date.now();
  const timeSinceLastActivity = now - lastActivityTime.current;

  if (isTabVisible.current && timeSinceLastActivity < INACTIVITY_LIMIT) {
    const activeDuration = now - lastVisibilityChange.current;
    lastVisibilityChange.current = now;
    lastActivityTime.current = now;
    return activeDuration;
  }

  console.log("[PostHog] Skipping duration due to inactivity or hidden tab");
  return 0;
};

const PostHogTrackerOnPageView = ({currentTab}) => {
  const location = useLocation();
  const posthog = usePostHog();
  const pageStartTime = useRef(Date.now());
  const lastPath = useRef(window.location.href);
  const dailyActiveTime = useRef(0); // Total active time across app
  const pageActiveTime = useRef(0); // Total active time per page
  const lastVisibilityChange = useRef(Date.now());
  const lastActivityTime = useRef(Date.now());
  const isTabVisible = useRef(true);
  const hourlyTimer = useRef(null);
  const [user, setUser] = useState<any>();
  const lastTab = useRef(currentTab);

useEffect(() => {
  const getUser = async () => {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (session) {
      setUser(session.user);
    }
  };

  getUser();
}, []);

useEffect(() => {
  if (user && posthog) {
    posthog.identify(user.id, {
      email: user.email,
      CLIENT: "SALESSTACK",
      ENV: EnvKeys.environment,
    });
    posthog.register({
        CLIENT: 'SALESSTACK',
        ENV: EnvKeys.environment,
    });
  }
}, [user, posthog]);

  // Track time on page navigation with full accumulation
useEffect(() => {
  const latestActive = getValidActiveDuration({
    isTabVisible,
    lastActivityTime,
    lastVisibilityChange,
  });

  pageActiveTime.current += latestActive;

  const durationInMin = Math.round((pageActiveTime.current / 60000) * 100) / 100;

  const tabChanged = lastTab.current !== currentTab;
  const pathChanged = lastPath.current !== window.location.href;

  if (posthog && (tabChanged || pathChanged)) {
    if (durationInMin > 0) {
      posthog.capture("page_time_spent", {
        path: lastPath.current,
        tab: lastTab.current,
        minutes: durationInMin,
      });
    }

    posthog.capture("page_visited", {
      path: window.location.href,
      tab: currentTab,
    });

    lastTab.current = currentTab;
    lastPath.current = window.location.href;
    pageStartTime.current = Date.now();
    pageActiveTime.current = 0;
  }
}, [location, posthog, currentTab]);

  // Track user activity
  useEffect(() => {
    const updateActivity = () => {
      const now = Date.now();
      const timeSinceLastActivity = now - lastActivityTime.current;

      // If user was inactive for a while, reset visibility window
      if (timeSinceLastActivity > INACTIVITY_LIMIT) {
        lastVisibilityChange.current = now;
      }

      lastActivityTime.current = now;
    };

    const events = ["mousemove", "keydown", "scroll", "click"];
    events.forEach((event) => window.addEventListener(event, updateActivity));

    return () => {
      events.forEach((event) =>
        window.removeEventListener(event, updateActivity)
      );
    };
  }, []);

  // Track visibility changes (accumulate time for both daily and page)
  useEffect(() => {
    const handleVisibilityChange = () => {
      const now = Date.now();

      if (document.visibilityState === "hidden") {
        if (isTabVisible.current) {
          const timeSinceLastActivity = now - lastActivityTime.current;
          if (timeSinceLastActivity < INACTIVITY_LIMIT) {
            const delta = now - lastVisibilityChange.current;
            dailyActiveTime.current += delta;
            pageActiveTime.current += delta;
          }
        }
        isTabVisible.current = false;
      } else {
        lastVisibilityChange.current = now;
        lastActivityTime.current = now;
        isTabVisible.current = true;
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);

  // Daily engagement reporting every 15 minutes
  useEffect(() => {
    const reportActiveTime = () => {
      const latestActive = getValidActiveDuration({
        isTabVisible,
        lastActivityTime,
        lastVisibilityChange,
      });

      dailyActiveTime.current += latestActive;
      pageActiveTime.current += latestActive;

      const minutesSpent =
        Math.round((dailyActiveTime.current / 60000) * 100) / 100;
      const pageMinutes =
        Math.round((pageActiveTime.current / 60000) * 100) / 100;

      if (posthog && user && minutesSpent > 0) {
        posthog.capture("daily_time_spent", {
          user_id: user,
          email: user,
          date: new Date().toISOString().split("T")[0],
          minutes: minutesSpent,
              tab: currentTab,

        });
      }

      if (posthog && pageMinutes > 0) {
        posthog.capture("page_time_spent", {
          path: window.location.href,
          minutes: pageMinutes,
              tab: currentTab,

        });
      }

      // Reset
      dailyActiveTime.current = 0;
      pageActiveTime.current = 0;
    };

    hourlyTimer.current = setInterval(reportActiveTime, 15 * 60 * 1000); // every 15 minutes

    window.addEventListener("beforeunload", reportActiveTime);

    return () => {
      clearInterval(hourlyTimer.current);
      window.removeEventListener("beforeunload", reportActiveTime);
    };
  }, [posthog, user]);

  return null;
};

export default PostHogTrackerOnPageView;
