import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Phone, Mail, User, FileText } from "lucide-react";
import { toast } from "sonner";
import Contact<PERSON>orm<PERSON>ield from "./contact/ContactFormField";
import ProgressDialog from "./contact/ProgressDialog";
import { PERSONAL_EMAIL_DOMAINS, API_ENDPOINTS } from "@/config/constants";
import { Textarea } from "@/components/ui/textarea";

interface ContactFormProps {
  selectedQuestion?: string;
}

interface FormData {
  name: string;
  phone: string;
  email: string;
  description: string;
}

const ContactForm = ({ selectedQuestion }: ContactFormProps) => {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    phone: "",
    email: "",
    description: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showProgressDialog, setShowProgressDialog] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const validateEmail = (email: string) => {
    const domain = email.split('@')[1];
    if (PERSONAL_EMAIL_DOMAINS.includes(domain)) {
      toast.error("Please use your corporate email address");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateEmail(formData.email)) {
      return;
    }

    setIsSubmitting(true);
    setShowProgressDialog(true);
    setIsSuccess(false);

    try {
      const response = await fetch(API_ENDPOINTS.CONTACT_FORM, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          selectedQuestion,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit form');
      }

      setIsSuccess(true);
      toast.success("Thank you for your interest. We'll be in touch soon!");
      setFormData({ name: "", phone: "", email: "", description: "" });
      
      setTimeout(() => {
        setShowProgressDialog(false);
        setIsSuccess(false);
      }, 1500);
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error("Failed to submit form. Please try again later.");
      setShowProgressDialog(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-gradient-to-br from-[#F8F9FF] to-white p-6 rounded-xl shadow-lg animate-fadeIn relative">
      {selectedQuestion && (
        <div className="absolute -top-3 left-6 bg-primary-600 text-white px-4 py-1 rounded-full text-sm font-medium shadow-lg animate-pulse-3-times transition-all duration-300">
          Request Noted!
        </div>
      )}
      <div className="mb-6 space-y-2">
        <h2 className="text-2xl font-bold text-[#294d48] leading-tight">
          Ask for Support
        </h2>
        <p className="text-sm text-gray-600 leading-relaxed">
          Simple utility to create an enquiry & search information
        </p>
      </div>
      <form onSubmit={handleSubmit} className="space-y-4">
        <ContactFormField
          icon={User}
          placeholder="Your Name"
          value={formData.name}
          onChange={(value) => setFormData({ ...formData, name: value })}
          className={selectedQuestion ? 'animate-pulse bg-[#F8F9FF] rounded-lg' : ''}
        />
        <ContactFormField
          icon={Phone}
          placeholder="Phone with Country Code"
          value={formData.phone}
          onChange={(value) => setFormData({ ...formData, phone: value })}
        />
        <ContactFormField
          icon={Mail}
          placeholder="Corporate Email"
          value={formData.email}
          onChange={(value) => setFormData({ ...formData, email: value })}
          type="email"
        />
        <div className="relative group">
          <FileText className="absolute left-3 top-3 h-4 w-4 text-primary-500" />
          <Textarea
            placeholder="Describe your problem or request"
            className="pl-9 min-h-[100px] border-primary-100 focus:border-primary-300 transition-colors"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            required
          />
        </div>
        <Button 
          type="submit" 
          className="w-full bg-primary-600 hover:bg-primary-700 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Sending..." : "Report to Technology Team"}
        </Button>
      </form>

      <ProgressDialog
        showDialog={showProgressDialog}
        setShowDialog={setShowProgressDialog}
        isSuccess={isSuccess}
      />
    </div>
  );
};

export default ContactForm;
