
import { useState, useEffect } from "react";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";

// Mock data for filters
const customers = [
  { id: "1", name: "Acme Corporation" },
  { id: "2", name: "TechGiant Inc." },
  { id: "3", name: "Global Industries" },
  { id: "4", name: "Innovate Solutions" }
];

export interface AccountPlanningFiltersProps {
  onFilterChange: (filters: { customer: string; accountOwner: string }) => void;
  initialCustomer?: string;
}

export const AccountPlanningFilters = ({ onFilterChange, initialCustomer = "" }: AccountPlanningFiltersProps) => {
  const [customer, setCustomer] = useState<string>(initialCustomer);

  useEffect(() => {
    // Initialize with provided customer or empty selection
    if (initialCustomer) {
      setCustomer(initialCustomer);
      onFilterChange({ customer: initialCustomer, accountOwner: "all" });
    }
  }, [initialCustomer, onFilterChange]);

  const handleCustomerChange = (value: string) => {
    setCustomer(value);
    onFilterChange({ customer: value, accountOwner: "all" });
  };

  return (
    <div className="w-full md:w-96">
      <div className="flex items-center gap-2">
        <Label htmlFor="customer-filter" className="whitespace-nowrap text-xs font-medium">Customer *</Label>
        <Select value={customer} onValueChange={handleCustomerChange} required>
          <SelectTrigger id="customer-filter" className="w-full h-8">
            <SelectValue placeholder="Select customer" />
          </SelectTrigger>
          <SelectContent>
            {customers.map((customer) => (
              <SelectItem key={customer.id} value={customer.id}>
                {customer.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};
