import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PencilIcon, PlusIcon, SaveIcon, XIcon, TrashIcon, ChevronDownIcon } from "lucide-react";
import { toast } from "sonner";

// Status options for the dropdowns (from pipeline data without numbers)
const statusOptions = [
  "Active recurring orders",
  "Order fulfilled, pending repeat order",
  "Order received",
  "Hot - Sample approved",
  "Hot - Sampling stage_Customer",
  "Hot - Sampling stage_Supply",
  "Cold - No response after enquiry",
  "Enquiry lost",
  "Warm - Enquiry stage_Customer",
  "Warm - Enquiry stage_Supply",
  "Warm - Pitched",
  "Cold - No response",
  "Discontinued",
];

export const AccountPlanningSheet = () => {
  const [chemicals, setChemicals] = useState(null);
  const [editingId, setEditingId] = useState<number | null>(null);

  const handleAddChemical = () => {
    const newChemical = {
      id: chemicals.length + 1,
      name: "",
      grade: "",
      quarterlyVolume: "",
      quarterlyValue: "",
      mstackPrevQtr: "",
      targetThisQuarter: "",
      status: statusOptions[0],
      conversionProbability: "",
      comment: ""
    };
    setChemicals([...chemicals, newChemical]);
    setEditingId(newChemical.id);
  };

  const toggleEdit = (id: number) => {
    setEditingId(editingId === id ? null : id);
  };

  const handleDeleteChemical = (id: number) => {
    setChemicals(chemicals.filter(chem => chem.id !== id));
    if (editingId === id) {
      setEditingId(null);
    }
    toast.success("Chemical removed successfully");
  };

  const handleStatusChange = (id: number, value: string) => {
    setChemicals(chemicals.map(chem => 
      chem.id === id ? { ...chem, status: value } : chem
    ));
  };

  const handleInputChange = (id: number, field: string, value: string) => {
    setChemicals(chemicals.map(chem => 
      chem.id === id ? { ...chem, [field]: value } : chem
    ));
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="flex flex-row items-center justify-between py-3">
        <CardTitle className="text-lg font-bold">Account Planning</CardTitle>
        <Button 
          onClick={handleAddChemical} 
          size="sm" 
          className="h-8 px-2 flex items-center gap-1"
        >
          <PlusIcon className="h-3.5 w-3.5" />
          Add Chemical
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <h3 className="font-medium text-sm pl-4 pt-2 pb-1">Top chemicals (or ~80% of procurement)</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-10 text-xs">S.No</TableHead>
                <TableHead className="text-xs">Chemical name</TableHead>
                <TableHead className="text-xs">Grade</TableHead>
                <TableHead className="text-xs">Quarterly volume</TableHead>
                <TableHead className="text-xs">Quarterly value</TableHead>
                <TableHead className="text-xs">Mstack prev Qtr</TableHead>
                <TableHead className="text-xs">Target this quarter</TableHead>
                <TableHead className="text-xs">Status</TableHead>
                <TableHead className="text-xs">Conversion probability</TableHead>
                <TableHead className="text-xs">Latest Comments</TableHead>
                <TableHead className="w-20 text-xs">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {chemicals.map((row) => (
                <TableRow key={row.id} className={editingId === row.id ? "bg-muted/30" : ""}>
                  <TableCell className="text-sm">{row.id}</TableCell>
                  <TableCell className="text-sm">{row.name}</TableCell>
                  <TableCell className="text-sm">{row.grade}</TableCell>
                  <TableCell className="text-sm">{row.quarterlyVolume}</TableCell>
                  <TableCell className="text-sm">{row.quarterlyValue}</TableCell>
                  <TableCell className="text-sm">{row.mstackPrevQtr}</TableCell>
                  <TableCell className="text-sm">{row.targetThisQuarter}</TableCell>
                  <TableCell>
                    <Select 
                      value={row.status}
                      onValueChange={(value) => handleStatusChange(row.id, value)}
                      disabled={editingId !== row.id}
                    >
                      <SelectTrigger 
                        className={`w-[180px] h-8 text-sm justify-between ${
                          editingId === row.id 
                            ? "border border-input" 
                            : "border-0 bg-transparent hover:bg-transparent"
                        }`}
                      >
                        <SelectValue placeholder="Select status" className="truncate">
                          {row.status}
                        </SelectValue>
                        {editingId !== row.id && (
                          <ChevronDownIcon className="h-4 w-4 opacity-50 ml-2" />
                        )}
                      </SelectTrigger>
                      <SelectContent>
                        {statusOptions.map((option) => (
                          <SelectItem key={option} value={option} className="text-xs">
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell className="text-sm">{row.conversionProbability}</TableCell>
                  <TableCell className="text-sm">{row.comment}</TableCell>
                  <TableCell>
                    {editingId === row.id ? (
                      <div className="flex space-x-1">
                        <Button 
                          variant="ghost" 
                          size="icon"
                          className="h-7 w-7"
                          onClick={() => toggleEdit(row.id)}
                        >
                          <SaveIcon className="h-3.5 w-3.5 text-green-600" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          className="h-7 w-7"
                          onClick={() => toggleEdit(row.id)}
                        >
                          <XIcon className="h-3.5 w-3.5 text-red-600" />
                        </Button>
                      </div>
                    ) : (
                      <div className="flex space-x-1">
                        <Button 
                          variant="ghost" 
                          size="icon"
                          className="h-7 w-7"
                          onClick={() => toggleEdit(row.id)}
                        >
                          <PencilIcon className="h-3.5 w-3.5" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          className="h-7 w-7"
                          onClick={() => handleDeleteChemical(row.id)}
                        >
                          <TrashIcon className="h-3.5 w-3.5 text-red-600" />
                        </Button>
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};
