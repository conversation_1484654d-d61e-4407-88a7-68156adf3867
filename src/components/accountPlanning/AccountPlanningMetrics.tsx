
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";

// Mock data for the metrics section
const revenuePotentialData = [
  { id: 1, label: "Quarterly procurement", value: "-" },
  { id: 2, label: "Total mapped", value: "0" },
  { id: 3, label: "Quarterly potential", value: "0" },
  { id: 4, label: "Target", value: "0" },
  { id: 5, label: "Achievement last Qtr", value: "0" },
  { id: 6, label: "Achievement this Qtr", value: "0" },
];

const pipelineData = [
  { id: 1, status: "1. Active recurring orders", products: "1", targetSum: "8000" },
  { id: 2, status: "2. Order fulfilled, pending repeat order", products: "0", targetSum: "0" },
  { id: 3, status: "3. Order received", products: "1", targetSum: "5000" },
  { id: 4, status: "4. Hot - Sample approved", products: "0", targetSum: "0" },
  { id: 5, status: "5. Hot - Sampling stage_Customer", products: "0", targetSum: "0" },
  { id: 6, status: "6. Hot - Sampling stage_Supply", products: "0", targetSum: "0" },
  { id: 7, status: "7. Cold - No response after enquiry", products: "0", targetSum: "0" },
  { id: 8, status: "8. Enquiry lost", products: "0", targetSum: "0" },
  { id: 9, status: "9. Warm - Enquiry stage_Customer", products: "1", targetSum: "9000" },
  { id: 10, status: "10. Warm - Enquiry stage_Supply", products: "0", targetSum: "0" },
  { id: 11, status: "11. Warm - Pitched", products: "0", targetSum: "0" },
  { id: 12, status: "12. Cold - No response", products: "0", targetSum: "0" },
  { id: 13, status: "13. Discontinued", products: "0", targetSum: "0" },
];

// Function to group pipeline data into rows for display
const groupPipelineData = (data: typeof pipelineData, itemsPerRow: number) => {
  const result = [];
  for (let i = 0; i < data.length; i += itemsPerRow) {
    result.push(data.slice(i, i + itemsPerRow));
  }
  return result;
};

export const AccountPlanningMetrics = () => {
  // Group pipeline data into rows of 7 for first row, 6 for second row
  const pipelineRows = groupPipelineData(pipelineData, 7);
  
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Account Planning Metrics</h2>
      
      {/* Main container with border */}
      <div className="border rounded-lg p-4 space-y-8">
        {/* Revenue Potential Summary */}
        <div>
          <h3 className="text-sm font-semibold mb-3">Summary - Revenue potential</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-3">
            {revenuePotentialData.map(item => (
              <div key={item.id} className="border rounded-md p-3">
                <div className="text-sm text-muted-foreground">{item.label}</div>
                <div className="text-lg font-medium mt-1">{item.value}</div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Pipeline Summary */}
        <div>
          <h3 className="text-sm font-semibold mb-3">Summary - Pipeline</h3>
          <div className="space-y-3">
            {/* First row of pipeline cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-3">
              {pipelineRows[0].map(item => (
                <div key={item.id} className="border rounded-md p-3">
                  <div className="text-xs text-muted-foreground">{item.status}</div>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm">{item.products} products</span>
                    <span className="text-lg font-medium">{item.targetSum !== "0" ? item.targetSum : "0"}</span>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Second row of pipeline cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-3">
              {pipelineRows[1].map(item => (
                <div key={item.id} className="border rounded-md p-3">
                  <div className="text-xs text-muted-foreground">{item.status}</div>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm">{item.products} products</span>
                    <span className="text-lg font-medium">{item.targetSum !== "0" ? item.targetSum : "0"}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
