import { Card } from "@/components/ui/card";
import { COMPANY_INFO } from "@/config/constants";

const HeroBanner = () => {
  return (
    <Card className="w-full bg-gradient-to-r from-[#D3E4FD] to-white relative overflow-hidden">
      {/* Chemical-like design elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-1/4 left-1/4 w-12 h-12 rounded-full border-2 border-primary-300" />
        <div className="absolute top-1/2 left-3/4 w-8 h-8 rounded-full border-2 border-primary-400" />
        <div className="absolute bottom-1/4 left-1/2 w-10 h-10 rounded-full border-2 border-primary-200" />
        {/* Additional technical design elements */}
        <div className="absolute top-1/3 right-1/4 w-16 h-1 bg-primary-200 rotate-45" />
        <div className="absolute bottom-1/3 left-1/3 w-16 h-1 bg-primary-300 -rotate-45" />
        <div className="absolute top-1/2 right-1/3 w-3 h-3 bg-primary-200" />
        <div className="absolute bottom-2/3 left-2/3 w-3 h-3 bg-primary-300" />
        <div className="absolute grid grid-cols-3 gap-0.5 top-1/4 right-1/4">
          <div className="w-1 h-1 bg-primary-400"></div>
          <div className="w-1 h-1 bg-primary-400"></div>
          <div className="w-1 h-1 bg-primary-400"></div>
          <div className="w-1 h-1 bg-primary-400"></div>
          <div className="w-1 h-1 bg-primary-400"></div>
          <div className="w-1 h-1 bg-primary-400"></div>
        </div>
      </div>
      
      <div className="py-4 px-4">
        <p className="text-sm text-primary-800 leading-relaxed max-w-2xl mx-auto text-center">
          Reduce import costs by up to {COMPANY_INFO.COST_REDUCTION}% with our data-driven tariff optimization. 
          Our procurement intelligence helps Fortune 500 companies save an average of ${COMPANY_INFO.AVERAGE_ANNUAL_SAVINGS}M annually 
          through strategic duty management and compliance automation.
        </p>
      </div>
    </Card>
  );
};

export default HeroBanner;