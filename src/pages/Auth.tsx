import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const Auth = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Clear any existing toasts when auth page loads
    toast.dismiss();

    // Handle hash fragment if present (needed for OAuth redirects)
    const handleHashFragment = () => {
      if (window.location.hash) {
        console.log("Hash fragment detected:", window.location.hash);

        // Just remove the hash without causing a page reload
        window.history.replaceState(null, '', window.location.pathname);
      }
    };

    handleHashFragment();

    // Check if user is already authenticated
    const checkSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          console.log("User is authenticated, redirecting to landing page");
          navigate("/", { replace: true });
        }
      } catch (error) {
        console.error("Error checking session:", error);
        toast.error("Error checking authentication status");
      }
    };

    checkSession();

    // Listen for auth changes - using non-async callback to avoid deadlocks
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log("Auth state changed:", event, session?.user?.email);

      if (event === 'SIGNED_IN' || session?.user) {
        // Use setTimeout to avoid potential deadlocks with Supabase
        setTimeout(() => {
          handleHashFragment(); // Clean any hash fragments first
          console.log("Auth state change detected, redirecting to landing page");
          navigate("/", { replace: true });
        }, 0);
      }
    });

    return () => subscription.unsubscribe();
  }, [navigate]);

  const handleGoogleSignIn = async () => {
    try {
      // Get the current origin for the redirect
      const redirectUrl = window.location.origin

      console.log("Signing in with Google, redirecting to:", redirectUrl);

      const { error } = await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: redirectUrl,
          skipBrowserRedirect:false
        }
      });

      if (error) {
        console.error("Google sign in error:", error);
        throw error;
      }
    } catch (error) {
      console.error("Google sign in error:", error);
      toast.error("Failed to sign in with Google. Please try again.");
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-white to-gray-50">
      <Header />
      <div className="flex-grow flex items-center justify-center px-4 py-12">
        <Card className="w-full max-w-md p-8 space-y-8 shadow-lg border-primary-100 bg-white/95 backdrop-blur-sm">
          <div className="text-center space-y-3">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary-600 via-primary-700 to-primary-800 bg-clip-text text-transparent">
              Sales-Stack
            </h1>
            <p className="text-gray-600 text-lg">Sign in with your account to continue</p>
          </div>

          <Button
            onClick={handleGoogleSignIn}
            className="w-full flex items-center justify-center gap-3 py-6 text-lg bg-white hover:bg-gray-50 text-gray-700 border-2 border-gray-200 shadow-sm transition-all duration-300 hover:scale-[1.02]"
          >
            <svg className="h-6 w-6" viewBox="0 0 24 24">
              <path
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                fill="#4285F4"
              />
              <path
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                fill="#34A853"
              />
              <path
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                fill="#FBBC05"
              />
              <path
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                fill="#EA4335"
              />
            </svg>
            Sign in with Google
          </Button>
        </Card>
      </div>
      <Footer />
    </div>
  );
};

export default Auth;
