
import { useState, useEffect } from "react";
import { SidebarLayout } from "@/components/layout/SidebarLayout";
import { AccountPlanningSheet } from "@/components/accountPlanning/AccountPlanningSheet";
import { AccountPlanningMetrics } from "@/components/accountPlanning/AccountPlanningMetrics";
import { AccountPlanningFilters } from "@/components/accountPlanning/AccountPlanningFilters";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { UserIcon } from "lucide-react";
import { useLocation } from "react-router-dom";

const AccountPlanning = () => {
  const [filters, setFilters] = useState({ customer: "", accountOwner: "all" });
  const location = useLocation();

  useEffect(() => {
    // Get customer filter from location state
    if (location.state?.selectedCustomer) {
      setFilters(prev => ({
        ...prev,
        customer: location.state.selectedCustomer
      }));
    }
  }, [location.state]);

  const handleFilterChange = (newFilters: { customer: string; accountOwner: string }) => {
    setFilters(newFilters);
  };

  const hasSelectedCustomer = filters.customer !== "" && filters.customer !== "all";

  return (
    <SidebarLayout>
      {() => (
        <div className="p-2 space-y-2">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
            <div className="flex items-center gap-4">
              <h1 className="text-lg font-bold">Account Planning</h1>
              <AccountPlanningFilters onFilterChange={handleFilterChange} initialCustomer={filters.customer} />
            </div>
          </div>
          
          {!hasSelectedCustomer ? (
            <Alert variant="default" className="bg-muted/50 border-muted">
              <UserIcon className="h-4 w-4 mr-2" />
              <AlertDescription>
                Please select a customer to begin account planning
              </AlertDescription>
            </Alert>
          ) : (
            <>
              <AccountPlanningSheet />
              <AccountPlanningMetrics />
            </>
          )}
        </div>
      )}
    </SidebarLayout>
  );
};

export default AccountPlanning;
