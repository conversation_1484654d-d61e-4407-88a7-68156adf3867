import { SidebarLayout } from "@/components/layout/SidebarLayout";
import { PlaceholderContent } from "@/components/placeholder/PlaceholderContent";
import { <PERSON><PERSON><PERSON>, ClipboardList } from "lucide-react";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { CustomerDetail } from "@/components/customers/CustomerDetail";
import { CustomerList } from "@/components/customers/CustomerList";
import { CurrentSnapshot } from "@/components/snapshot/CurrentSnapshot";
import SalesStack from "./SalesStack";
import { Meetings } from "@/pages/Meetings";
import { UserSettings } from "@/components/settings/UserSettings";
import Metabase from "@/components/metabase";
import LandingPage from "@/components/LandingPage";

const Index = () => {

   const [selectedCustomerId, setSelectedCustomerId] = useState<number | null>(null);
   const [selectedCommonCustomerId, setSelectedCommonCustomerId] = useState<string | null>(null);
    const [selectedCustomerName, setSelectedCustomerName] = useState<string>("");
    const navigate = useNavigate();
    const location = useLocation();
    
    // Handle state passed from navigation
    useEffect(() => {
      if (location.state) {
        if (location.state.selectedCustomer) {
          setSelectedCustomerId(location.state.selectedCustomer);
        } else {
          setSelectedCustomerId(null);
        }
        if (location.state.selectedCustomerName) {
          setSelectedCustomerName(location.state.selectedCustomerName);
        } else {
          setSelectedCustomerName("");
        }
        setSelectedCommonCustomerId(location.state.selectedCommonCustomerId);
      }
    }, [location.state]);


  const renderContent = (tab: string) => {
    // Handle customers tab
    if (tab === "customers") {
      return (selectedCustomerId && selectedCommonCustomerId )? (
        <CustomerDetail 
          customerId={selectedCustomerId}
          commonCustomerId={selectedCommonCustomerId}
          onBack={() => setSelectedCustomerId(null)} 
        />
      ) : (
        <CustomerList 
          onSelectCustomer={(id, name, customerId) => {
            setSelectedCustomerId(id);
            if (name) setSelectedCustomerName(name);
            setSelectedCommonCustomerId(customerId);
          }} 
        />
      );
    }
    // Handle snapshot tab
    if (tab === "snapshot") {
      return <CurrentSnapshot selectedCustomerId={selectedCustomerId} />;
    }
    
    if (tab === "meetings") {
      return <Meetings 
      customerId={selectedCustomerId}
      />;
    }

    // Navigate to meetings page
    if (tab === "salesstack") {
      return <SalesStack />;
    }

    if (tab === "user-settings") {
      return <UserSettings />;
    }
    
    if (tab === "metabase") {
      return <Metabase />;
    }

    if(tab === "landing-page") {
      return <LandingPage />;
    }
    // Handle other tabs with placeholder content
    const placeholders = {
      dashboard: {
        title: "Dashboard", 
        icon: BarChart,
        color: "#9b87f5",
        message: "This page will contain sales reports and visualizations."
      },
      salesstack: {
        title: "SalesStack",
        icon: ClipboardList,
        color: "#64a9f5",
        message: "This page will integrate with SalesStack tools."
      }
    };
    
    const content = placeholders[tab as keyof typeof placeholders];
    if (!content) return <div>Content not found</div>;
    
    return (
      <PlaceholderContent 
        title={content.title}
        icon={content.icon}
        color={content.color}
        message={content.message}
      />
    );
  };
  
  return (
    <>
      {/* <Header /> */}

          <SidebarLayout>
            {(tab: string) => (
              <div className="px-4 py-4">
                {renderContent(tab)}
              </div>
            )}
          </SidebarLayout>
     
      {/* <Footer /> */}
    </>
  
  );
};

export default Index;
