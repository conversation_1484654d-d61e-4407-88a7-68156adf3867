import NewCreateEnquiryForm from "@/components/enquiries/NewCreateEnquiryForm";
import EnquiryKanban from "@/components/stats/kanban/EnquiryKanban";
import StatsTracker from "@/components/stats/StatsTracker";

const SalesStack = () => {
  return (
    <main className="flex-grow w-full pt-8 animate-fade-in">
      <div className="max-w-[1500px] mx-auto px-4 space-y-8">
        {/* <StatsTracker /> */}

        <div data-enquiry-widget>
          <NewCreateEnquiryForm />
        </div>
       
        {/* Add Kanban Board */}
        <EnquiryKanban />

        {/* <div className="py-8">
          <div className="bg-white rounded-lg overflow-hidden border border-gray-100">
            <div className="p-6">
              <ContactForm />
            </div>
          </div>
        </div> */}
      </div>
    </main>
  );
};

export default SalesStack; 