import { useEffect, useState } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { SidebarLayout } from "@/components/layout/SidebarLayout";
import { MeetingForm } from "@/components/meetings/MeetingForm";
import { Meeting } from "@/components/meetings/MeetingsList";
import { toast } from "sonner";
import { useMeetings } from "@/components/meetings/hooks/useMeetings";
import { useCustomers } from "@/hooks/use-customers";

export function EditMeeting() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const [meeting, setMeeting] = useState<Meeting | null>(null);
  const { submitMeeting, isSubmitting } = useMeetings();
  const { customers } = useCustomers();
  
  useEffect(() => {
    // Get meeting data from location state
    if (location.state?.meeting) {
      setMeeting(location.state.meeting);
    } else {
      // If no meeting data in state, navigate back to meetings page
      toast.error("Meeting not found");
      navigate("/meetings", {
        state: {
          activeTab: 'meetings',
          keepOpen: true
        }
      });
    }
  }, [location.state, navigate]);

  const handleUpdateMeeting = async (updatedMeeting: Omit<Meeting, 'id'>) => {
    if (!meeting?.id) {
      toast.error("Meeting ID not found");
      return;
    }

    try {
      await submitMeeting({
        id: meeting.id,
        customer: updatedMeeting.customer,
        meetingWith: updatedMeeting.meetingWith,
        date: updatedMeeting.date,
        accountOwner: updatedMeeting.accountOwner,
        description: updatedMeeting.description,
        followUpSteps: updatedMeeting.followUpSteps,
        enquiriesReceived: updatedMeeting.enquiriesReceived,
        sampleRequests: updatedMeeting.sampleRequests,
        posRaised: updatedMeeting.posRaised
      });

      // Navigate back to meetings page with active tab state
      navigate("/crm", {
        state: {
          activeTab: 'meetings',
          keepOpen: true
        }
      });
    } catch (error) {
      console.error("Update error:", error);
      toast.error("Failed to update meeting");
    }
  };

  const handleCancel = () => {
    // Navigate back to meetings page with active tab state
    navigate("/crm", { state: { activeTab: "meetings"} });
  };

  return (
    <SidebarLayout>
      {(tab: string) => (
        <div className="container mx-auto px-4 py-6">
          <h1 className="text-2xl font-bold mb-6">Edit Meeting</h1>
          {meeting ? (
            <MeetingForm
              onSubmit={handleUpdateMeeting}
              onCancel={handleCancel}
              initialValues={meeting}
              isEdit={true}
              customers={customers}
            />
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Loading meeting details...</p>
            </div>
          )}
        </div>
      )}
    </SidebarLayout>
  );
}
