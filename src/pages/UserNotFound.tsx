import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ShieldX, ArrowLeft, Lock, User, AlertTriangle } from "lucide-react";
import { useNavigate } from "react-router-dom";

const NotAuthorized = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted relative overflow-hidden flex items-center justify-center p-4">
      {/* Background Decorative Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-warning/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-info/5 rounded-full blur-2xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-24 h-24 bg-primary/3 rounded-full blur-xl"></div>
        
        {/* Floating Icons */}
        <div className="absolute top-20 left-20 opacity-10">
          <Lock className="w-8 h-8 text-primary animate-pulse" />
        </div>
        <div className="absolute top-32 right-32 opacity-10">
          <User className="w-6 h-6 text-warning" />
        </div>
        <div className="absolute bottom-32 left-32 opacity-10">
          <AlertTriangle className="w-7 h-7 text-info" />
        </div>
        <div className="absolute bottom-20 right-20 opacity-10">
          <ShieldX className="w-9 h-9 text-primary" />
        </div>
      </div>

      {/* Grid Pattern Overlay */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.15)_1px,transparent_0)] [background-size:20px_20px] opacity-20"></div>

      <div className="w-full max-w-md relative z-10">
        <Card className="border-0 shadow-[--shadow-medium] backdrop-blur-sm bg-card/95">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto mb-4 w-20 h-20 bg-gradient-to-br from-warning/10 to-warning/5 rounded-full flex items-center justify-center relative">
              <div className="absolute inset-0 bg-warning/5 rounded-full animate-pulse"></div>
              <ShieldX className="w-10 h-10 text-warning relative z-10" />
            </div>
            <CardTitle className="text-2xl font-semibold text-foreground">
              Access Not Authorized
            </CardTitle>
            <CardDescription className="text-muted-foreground text-base leading-relaxed">
              Your Google account was authenticated successfully, but you're not registered in our system yet.
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <div className="bg-gradient-to-r from-muted/50 to-muted/30 p-5 rounded-xl border border-border/50 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-20 h-20 bg-info/5 rounded-full blur-xl"></div>
              <div className="relative z-10">
                <p className="text-sm text-foreground font-medium mb-2 flex items-center">
                  <AlertTriangle className="w-4 h-4 mr-2 text-info" />
                  What's next?
                </p>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  Please contact your system administrator to request access to this application.
                </p>
              </div>
            </div>
            
            <div className="space-y-4">
              <Button 
                onClick={()=>{
                  localStorage.clear()
navigate("/auth")
                }}
                className="w-full relative overflow-hidden group"
                size="lg"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary/80 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <ArrowLeft className="w-4 h-4 mr-2 relative z-10" />
                <span className="relative z-10">Back to Login</span>
              </Button>
              
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default NotAuthorized;