import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate, useNavigate, useLocation } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import Index from "./pages/Index";
import Auth from "./pages/Auth";
import NotFound from "./pages/NotFound";
import HelpSection from "@/components/HelpSection";
import { supabase } from "@/integrations/supabase/client";
import "@/styles/focus-override.css";
import { useEffect } from "react";
import { EditMeeting } from "@/pages/EditMeeting";
import LandingPage from "./components/LandingPage";
import UserNotFound from "./pages/UserNotFound";
// import { Meetings } from "@/pages/Meetings";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {

  const navigate = useNavigate();
  const location = useLocation();
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const enquiryId = queryParams.get('enquiryId');
    const sampleId = queryParams.get('sampleId');
    
    if (enquiryId) {
      console.log("Deep link detected for enquiry ID:", enquiryId);
      // Remove the query parameter to avoid reopening on refresh
      sessionStorage.setItem('openEnquiryId', enquiryId);
      if(sampleId) {
        sessionStorage.setItem('sampleTab', "true");
      }

      navigate(location.pathname, { replace: true });
    }
  }, [location, navigate]);
  
  const { data: session, isLoading } = useQuery({
    queryKey: ['auth-session'],
    queryFn: async () => {
      const { data: { session } } = await supabase.auth.getSession();
      return session;
    },
  });

  // Show nothing while checking authentication
  if (isLoading) {
    return null;
  }

  if (!session) {
    return <Navigate to="/auth" replace />;
  }

  return <>{children}</>;
};

const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <Routes>
            <Route path="/auth" element={<Auth />} />
             <Route
              path="/"
              element={
                <ProtectedRoute>
                  <LandingPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/crm"
              element={
                <ProtectedRoute>
                  <Index />
                </ProtectedRoute>
              }
            />
            <Route
              path="/landing-page"
              element={
                <ProtectedRoute>
                  <LandingPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/meetings/edit/:id"
              element={
                <ProtectedRoute>
                  <EditMeeting />
                </ProtectedRoute>
              }
            />
            <Route
              path="/help"
              element={
                <ProtectedRoute>
                  <HelpSection />
                </ProtectedRoute>
              }
            />
            <Route path="/user-not-found" element={<UserNotFound />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </TooltipProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

export default App;
