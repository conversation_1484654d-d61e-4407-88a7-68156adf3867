import {SUPABASE_URL,SUPABASE_PUBLISHABLE_KEY} from "@/integrations/supabase/client"

export const API_CONFIG = {
  baseUrl: `${SUPABASE_URL}/functions/v1/metabase/`,
  catalogBaseUrl : `${SUPABASE_URL}/functions/v1/`,
  accessToken : `${SUPABASE_PUBLISHABLE_KEY}`,
  endpoints: {
    purchaseOrderDetails: 'purchaseOrderDetails',
    margins: 'margins',
    achievement: 'achievement',
    quarterlyMetrics: 'quarterlyMetrics',
    customerMetrics: 'customerMetrics',
    salesProgress: 'salesProgress',
    salesProgressBuHead: 'salesProgressBuHead'
  }
} as const;
